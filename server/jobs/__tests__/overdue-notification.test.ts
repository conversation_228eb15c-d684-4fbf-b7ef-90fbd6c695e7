import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as notificationService from '@server/services/capa-notification.service';
import * as capaService from '@server/services/capa.service';
import * as locationService from '@server/services/location.service';
import * as userService from '@server/services/user.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { logger } from '../../utils/logger';

vi.mock('@server/services/capa.service', () => ({
  getOverdueCapas: vi.fn(),
}));
vi.mock('@server/services/capa-notification.service', () => ({
  sendCapaOverdueNotification: vi.fn(),
}));
vi.mock('@server/services/location.service', () => ({
  searchLocationsPublic: vi.fn(),
}));
vi.mock('@server/services/user.service', () => ({
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
}));
vi.mock('@server/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock process.exit
const mockExit = vi.fn();
process.exit = mockExit as never;

describe('overdue-notification job', () => {
  const mockCapas = [
    {
      id: 'capa1',
      title: 'Test CAPA',
      dueDate: new Date(Date.now() - ********), // 1 day overdue
      status: 'open' as const,
      upkeepCompanyId: 'company1',
      locationId: 'loc1',
      ownerId: 'user1',
      slug: 'capa-1',
      rcaFindings: 'Root cause',
      type: 'corrective' as const,
      priority: 'high' as const,
      eventId: 'event1',
      eventSlug: 'event-1',
      eventTitle: 'Test Event',
      summary: 'Summary',
      assetId: 'asset1',
      rcaMethod: 'other' as const,
      rootCauses: ['other'],
      otherRootCause: 'Other root cause',
      aiSuggestedAction: 'AI suggested action',
      aiConfidenceScore: 0.9,
      actionsToAddress: 'Actions to address',
      tags: ['tag1', 'tag2'],
      privateToAdmins: true,
      teamMembersToNotify: ['user2', 'user3'],
      archived: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      createdBy: 'user1',
      triggerWorkOrder: true,
      actionsImplemented: 'Actions implemented',
      implementationDate: new Date(),
      closedAt: new Date(),
      closedBy: 'user1',
      closedReason: 'Closed reason',
      implementedBy: 'user1',
      voeDueDate: new Date(),
      verificationFindings: 'Verification findings',
      voePerformedBy: 'user1',
      voePerformedAt: new Date(),
      voeStatus: 'Completed',
      voeComments: 'Voe comments',
      voeDate: new Date(),
      effectivenessStatus: 'effective' as const,
    },
  ];

  const mockLocation = {
    noResults: false,
    result: [{ id: 'loc1', name: 'Test Location' }],
    nextCursor: undefined,
  };
  const mockAdmins = {
    noResults: false,
    result: [
      { id: 'admin1', username: 'admin1', firstName: 'Admin', email: '<EMAIL>', fullName: 'Admin User' },
    ],
    nextCursor: undefined,
  };
  const mockOwner = {
    id: 'owner1',
    username: 'owner1',
    firstName: 'Owner',
    email: '<EMAIL>',
    fullName: 'Owner User',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetModules();
    mockExit.mockClear();
    vi.mocked(getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(getUserPublic).mockResolvedValue(mockOwner);
  });

  it('should send notifications for admins and owner', async () => {
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue(mockCapas);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(notificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    expect(notificationService.sendCapaOverdueNotification).toHaveBeenCalledTimes(1);

    expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);

    expect(userService.getUsersPublic).toHaveBeenCalledWith({
      upkeepCompanyId: 'company1',
      userAccountType: USER_ACCOUNT_TYPES.ADMIN,
    });
    expect(userService.getUserPublic).toHaveBeenCalledWith({
      upkeepCompanyId: 'company1',
      id: 'user1',
    });

    expect(notificationService.sendCapaOverdueNotification).toHaveBeenCalledWith({
      capa: {
        ...mockCapas[0],
        daysOverdue: 1,
        location: mockLocation.result[0],
        owner: mockOwner,
        capaUrl: expect.any(String),
      },
      toUsers: [
        { email: '<EMAIL>', fullName: 'Admin User', type: 'to' },
        { email: '<EMAIL>', fullName: 'Owner User', type: 'to' },
      ],
    });
  });

  it('should NOT send notification if there is no one to notify', async () => {
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue(mockCapas);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue({ noResults: true, result: [], nextCursor: undefined }); // admins
    vi.mocked(userService.getUserPublic).mockResolvedValue(undefined);
    vi.mocked(notificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    expect(capaService.getOverdueCapas).toHaveBeenCalledTimes(1);
    expect(locationService.searchLocationsPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);

    expect(notificationService.sendCapaOverdueNotification).not.toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith('No owner found for CAPA overdue notification', {
      capaId: mockCapas[0].id,
    });
  });

  it('should handle CAPA without ownerId (only admins notified)', async () => {
    const capaNoOwner = { ...mockCapas[0], ownerId: undefined } as any;
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue([capaNoOwner]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(notificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Should only call getUsersPublic once (for admins)
    expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);

    // Should send notification only to admins
    expect(notificationService.sendCapaOverdueNotification).not.toHaveBeenCalled();
  });

  it('should handle CAPA without locationId (location is undefined)', async () => {
    const capaNoLocation = { ...mockCapas[0], locationId: null };
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue([capaNoLocation]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue({
      noResults: true,
      result: [],
      nextCursor: undefined,
    });
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(notificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Should not call searchLocationsPublic
    expect(locationService.searchLocationsPublic).not.toHaveBeenCalled();
    expect(notificationService.sendCapaOverdueNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        capa: expect.objectContaining({ location: undefined }),
      }),
    );
  });

  it('should log error if sendCAPAOverdueNotification throws', async () => {
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue(mockCapas);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(notificationService.sendCapaOverdueNotification).mockRejectedValue(new Error('Email error'));

    await import('../overdue-notification');

    expect(logger.error).toHaveBeenCalledWith(
      'Failed to send overdue notification',
      expect.objectContaining({
        error: expect.any(Error),
        capaId: mockCapas[0].id,
      }),
    );
  });
});
