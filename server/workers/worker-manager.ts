import { Worker, Job } from 'bullmq';
import { bullMqRedisClient } from '../redis/queue-client';
import { QUEUE_JOB_NAMES } from '../queue/job-names';
import { sendCapaUpdateNotification, sendCapaAssignedNotification } from '../services/capa-notification.service';
import { sendCommentMentionNotification } from '../services/comment-notification.service';
import {
  sendPublicEventCreateNotification,
  sendEventCreateNotification,
  sendEventUpdateNotificationWithUser,
} from '../services/event-notification.service';
import { logger } from '../utils/logger';

const workers: Worker[] = [];

/**
 * Generic worker job handler that abstracts common try-catch boilerplate
 * @param serviceFunction - The service function to execute
 * @param jobType - Human-readable job type for logging
 * @param getJobContext - Function to extract context data for logging
 */
const createJobHandler = <T>(
  serviceFunction: (data: T) => Promise<void>,
  jobType: string,
  getJobContext: (data: T) => Record<string, unknown>,
) => {
  return async (job: Job<T>) => {
    const context = getJobContext(job.data);

    try {
      logger.info(`Processing ${jobType} job: ${job.id}`, {
        jobId: job.id,
        attempts: job.attemptsMade,
        ...context,
      });

      await serviceFunction(job.data);

      logger.info(`Successfully processed ${jobType} job: ${job.id}`, {
        jobId: job.id,
        ...context,
      });
    } catch (error) {
      logger.error(`Failed to process ${jobType} job: ${job.id}`, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        attempts: job.attemptsMade,
        ...context,
      });
      throw error; // Re-throw to trigger retry
    }
  };
};

export const registerWorkers = async () => {
  // CAPA Update Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
      createJobHandler(sendCapaUpdateNotification, 'CAPA update notification', (data) => ({ capaId: data.capa?.id })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // CAPA Assigned Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION,
      createJobHandler(sendCapaAssignedNotification, 'CAPA assigned notification', (data) => ({
        capaId: data.capa?.id,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Comment Mention Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.COMMENT_MENTION_NOTIFICATION,
      createJobHandler(sendCommentMentionNotification, 'comment mention notification', (data) => ({
        commentId: data.newComment?.id,
        entityType: data.input?.entityType,
        entityId: data.input?.entityId,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Public Event Create Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.PUBLIC_EVENT_CREATE_NOTIFICATION,
      createJobHandler(sendPublicEventCreateNotification, 'public event create notification', (data) => ({
        eventId: data.createdEvent?.id,
        upkeepCompanyId: data.upkeepCompanyId,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Event Create Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.EVENT_CREATE_NOTIFICATION,
      createJobHandler(sendEventCreateNotification, 'event create notification', (data) => ({
        eventId: data.createdEvent?.id,
        userId: data.user?.id,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Event Update Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.EVENT_UPDATE_NOTIFICATION,
      createJobHandler(sendEventUpdateNotificationWithUser, 'event update notification', (data) => ({
        eventId: data.updatedEvent?.id,
        userId: data.user?.id,
        actionPrefix: data.actionPrefix,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  logger.info('[BullMQ] Workers registered successfully');
};

export const shutdownWorkers = async () => {
  await Promise.all(workers.map((w) => w.close()));
  logger.info('[BullMQ] All workers shut down');
};
