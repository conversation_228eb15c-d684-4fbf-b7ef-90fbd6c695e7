import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { JobsOptions } from 'bullmq';

// Override global mock for this test file to test real functionality
vi.unmock('../queue-utils');

// Mock BullMQ
const mockQueue = {
  add: vi.fn(),
  close: vi.fn(),
};

const mockCreateQueue = vi.fn(() => mockQueue);

vi.mock('../create-queue', () => ({
  createQueue: mockCreateQueue,
}));

vi.mock('../job-names', () => ({
  QUEUE_JOB_NAMES: {
    UPDATE_CAPA_NOTIFICATION: 'update-capa-notification',
  },
}));

describe('Queue Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('addJobToQueue', () => {
    it('should create a new queue if not cached', async () => {
      // Import after mocking to ensure fresh instance
      const { addJobToQueue } = await import('../queue-utils');

      const queueName = 'test-queue';
      const jobData = { test: 'data' };

      addJobToQueue(queueName, jobData);

      expect(mockCreateQueue).toHaveBeenCalledWith(queueName);
      expect(mockQueue.add).toHaveBeenCalledWith(queueName, jobData, {});
    });

    it('should reuse cached queue', async () => {
      // Import after mocking to ensure fresh instance
      const { addJobToQueue } = await import('../queue-utils');

      const queueName = 'test-queue-reuse';
      const jobData1 = { test: 'data1' };
      const jobData2 = { test: 'data2' };

      // First call
      addJobToQueue(queueName, jobData1);
      // Second call
      addJobToQueue(queueName, jobData2);

      expect(mockCreateQueue).toHaveBeenCalledTimes(1);
      expect(mockQueue.add).toHaveBeenCalledTimes(2);
    });

    it('should merge default options with custom options', async () => {
      const { addJobToQueue } = await import('../queue-utils');

      const queueName = 'update-capa-notification';
      const jobData = { test: 'data' };
      const customOptions: JobsOptions = {
        jobId: 'custom-id',
        attempts: 5, // This should override the default of 13
      };

      addJobToQueue(queueName, jobData, customOptions);

      expect(mockQueue.add).toHaveBeenCalledWith(
        queueName,
        jobData,
        expect.objectContaining({
          jobId: 'custom-id',
          attempts: 5, // Custom value should override default
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnFail: true,
          removeOnComplete: true,
        }),
      );
    });

    it('should use default options for CAPA notification queue', async () => {
      const { addJobToQueue } = await import('../queue-utils');

      const queueName = 'update-capa-notification';
      const jobData = { test: 'data' };

      addJobToQueue(queueName, jobData);

      expect(mockQueue.add).toHaveBeenCalledWith(
        queueName,
        jobData,
        expect.objectContaining({
          attempts: 13,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnFail: true,
          removeOnComplete: true,
        }),
      );
    });

    it('should handle queues without default options', async () => {
      const { addJobToQueue } = await import('../queue-utils');

      const queueName = 'unknown-queue';
      const jobData = { test: 'data' };

      addJobToQueue(queueName, jobData);

      expect(mockQueue.add).toHaveBeenCalledWith(queueName, jobData, {});
    });

    it('should handle queue creation errors', async () => {
      // Clear module cache to get fresh import
      vi.resetModules();

      // Set up the error before importing
      mockCreateQueue.mockImplementationOnce(() => {
        throw new Error('Redis connection failed');
      });

      const { addJobToQueue } = await import('../queue-utils');

      const queueName = 'error-queue';
      const jobData = { test: 'data' };

      expect(() => addJobToQueue(queueName, jobData)).toThrow('Redis connection failed');
    });

    it('should handle job addition errors', async () => {
      const { addJobToQueue } = await import('../queue-utils');

      mockQueue.add.mockImplementationOnce(() => {
        throw new Error('Job validation failed');
      });

      const queueName = 'test-queue';
      const jobData = { test: 'data' };

      expect(() => addJobToQueue(queueName, jobData)).toThrow('Job validation failed');
    });
  });
});
