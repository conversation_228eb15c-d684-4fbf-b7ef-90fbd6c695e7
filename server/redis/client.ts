import { logger } from '@server/utils/logger';
import { env } from '../../env';
import Redis from 'ioredis';

declare global {
  var _redisClient: Redis | undefined;
}

export const redisClient =
  globalThis._redisClient ??
  new Redis(env.REDIS_URL, {
    keyPrefix: env.ENVIRONMENT_PREFIX,
    connectTimeout: 10000,
    noDelay: true,
    maxLoadingRetryTime: 15000,
    connectionName: `ehs-${env.ENVIRONMENT_PREFIX}-${process.pid}`,
  });

// In development and test environments, use globalThis to persist the Redis client
// across module reloads (e.g., hot reloading, Vitest, Vite dev), avoiding multiple connections.
// In production (e.g., K8s), let each process/pod manage its own isolated client instance.
if (process.env.NODE_ENV !== 'production') {
  globalThis._redisClient = redisClient;
}

// Optional: add listeners once
redisClient.on('connect', () => logger.info('[Redis] Connected'));
redisClient.on('error', (err) => logger.error('[Redis] Connection error:', err));
redisClient.on('reconnecting', () => logger.info('[Redis] Reconnecting...'));

export const shutdown = async () => {
  await redisClient.quit();
};
