import React from 'react';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/router.types';
import { severityEnum } from '@shared/schema';
import { Asset, Location, REPORT_TYPE_MAP, SEVERITY_MAP, UserPublic } from '@shared/schema.types';
import { getSeverityColors } from './helpers';

export type EventCreateTemplateParams = Omit<RouterOutputs['event']['create'], 'reportedBy'> & {
  assets?: Asset[];
  location?: Location;
  reportedBy?: Partial<UserPublic> | undefined;
  eventUrl: string;
};

export default function EventCreateTemplate({
  slug,
  title,
  type,
  description,
  assets,
  severity,
  reportedAt,
  location,
  reportedBy,
  eventUrl,
}: EventCreateTemplateParams) {
  const getSeverityBadgeStyle = (severity: (typeof severityEnum.enumValues)[number]) => {
    const colors = getSeverityColors(severity);
    return {
      display: 'inline-block',
      padding: '2px 10px',
      borderRadius: '8px',
      fontSize: '13px',
      fontWeight: 600,
      marginRight: '6px',
      ...colors,
    };
  };

  const styles = {
    body: {
      fontFamily: 'Arial, sans-serif',
      background: '#fff',
      color: '#222',
      margin: 0,
      padding: 0,
    },
    container: {
      maxWidth: '700px',
      margin: '32px auto',
      background: '#fff',
      borderRadius: '4px',
      border: '1px solid #eee',
    },
    content: {
      padding: '0 32px 20px',
    },
    header: {
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '20px',
      padding: '10px',
      background: '#f3f4f6',
      borderBottom: '1px solid #e5e7eb',
      borderRadius: '4px 4px 0 0',
    },
    sectionTitle: {
      fontSize: '18px',
      fontWeight: 600,
      marginTop: '32px',
      marginBottom: '20px',
    },
    infoTable: {
      width: '100%',
      borderCollapse: 'collapse' as const,
      marginBottom: '24px',
    },
    fieldBlock: {
      marginBottom: '18px',
    },
    label: {
      color: '#222',
      display: 'block',
      fontWeight: 600,
      textAlign: 'left' as const,
    },
    value: {
      display: 'block',
      fontWeight: 500,
    },
    badgeEvent: {
      background: '#3e63dd',
      color: '#fff',
    },
    descBlock: {
      marginTop: '36px',
    },
    descLabel: {
      color: '#222',
      display: 'block',
      marginBottom: '8px',
      fontWeight: 600,
    },
    descValue: {
      display: 'block',
      color: '#222',
      fontWeight: 500,
    },
    ctaBtn: {
      display: 'inline-block',
      background: '#3e63dd',
      color: '#fff',
      padding: '10px 28px',
      borderRadius: '6px',
      fontWeight: 600,
      textDecoration: 'none',
      margin: '16px 0 0',
    },
    footer: {
      color: '#888',
      fontSize: '13px',
      marginTop: '32px',
      textAlign: 'left' as const,
    },
    card: {
      border: '1px solid #eee',
      borderRadius: '10px',
      padding: '24px',
      marginBottom: '24px',
      background: '#fff',
    },
    ctaCenter: {
      textAlign: 'center' as const,
    },
    divider: {
      border: 'none',
      borderTop: '1px solid #e5e7eb',
      margin: '24px 0 28px 0',
    },
    notSpecifiedSeverity: {
      display: 'inline-block',
      padding: '2px 10px',
      borderRadius: '8px',
      fontSize: '13px',
      fontWeight: 600,
      marginRight: '6px',
      backgroundColor: '#f1f5f9',
      color: '#64748b',
    },
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Safety Event Created Notification</title>
        <style>{`
          @media only screen and (max-width: 600px) {
            .responsive-container {
              margin: 16px !important;
              border-radius: 0 !important;
            }
            
            .responsive-content {
              padding: 0 16px 20px !important;
            }
            
            .responsive-card {
              padding: 16px !important;
              margin-bottom: 16px !important;
            }
            
            .responsive-table {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table tbody,
            .responsive-table tr,
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table td {
              display: block !important;
              width: 100% !important;
              padding-bottom: 24px !important;
              border: none !important;
              box-sizing: border-box !important;
            }
            
            .responsive-table td:last-child {
              padding-bottom: 0 !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
              vertical-align: top !important;
            }
            
            .mobile-section-title {
              font-size: 16px !important;
              font-weight: 600 !important;
              margin: 0 0 20px 0 !important;
              padding-bottom: 8px !important;
              border-bottom: 1px solid #e5e7eb !important;
            }
            
            .mobile-divider {
              display: block !important;
              border: none !important;
              border-top: 1px solid #e5e7eb !important;
              margin: 24px 0 !important;
            }
          }
          
          /* Fallback for email clients */
          @media screen and (max-device-width: 600px) {
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
            }
          }
        `}</style>
      </head>
      <body style={styles.body}>
        <div style={styles.container} className="responsive-container">
          <div style={styles.header}>Safety Event Created Notification</div>
          <div style={styles.content} className="responsive-content">
            <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>
            <div style={{ marginBottom: '24px' }}>
              A safety event has been reported. Please review this safety event in UpKeep EHS and take appropriate
              action.
            </div>
            <div style={styles.card} className="responsive-card">
              <table style={styles.infoTable} className="responsive-table" width="100%">
                <tbody>
                  <tr>
                    <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                      <div
                        style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                        className="mobile-section-title"
                      >
                        Safety Event Information
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Safety Event ID</span>
                        <span style={styles.value}>{slug}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Type</span>
                        <div style={styles.value}>
                          <span
                            style={{
                              display: 'inline-block',
                              padding: '2px 10px',
                              borderRadius: '8px',
                              fontSize: '13px',
                              fontWeight: 600,
                              marginRight: '6px',
                              ...styles.badgeEvent,
                            }}
                          >
                            {REPORT_TYPE_MAP[type as keyof typeof REPORT_TYPE_MAP]}
                          </span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Severity</span>
                        <div style={styles.value}>
                          {severity ? (
                            <span style={getSeverityBadgeStyle(severity)}>{SEVERITY_MAP[severity]}</span>
                          ) : (
                            <span style={styles.notSpecifiedSeverity}>Not Specified</span>
                          )}
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Asset</span>
                        <span style={styles.value}>
                          {assets && assets.length > 0 ? assets?.map((asset) => asset.name).join(', ') : 'Unspecified'}
                        </span>
                      </div>
                    </td>
                    <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                      <hr className="mobile-divider" style={{ display: 'none' }} />
                      <div
                        style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                        className="mobile-section-title"
                      >
                        Details
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Date/Time</span>
                        <span style={styles.value}>{formatDate(reportedAt)}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Location</span>
                        <span style={styles.value}>{location?.name}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Reported By</span>
                        {reportedBy?.fullName && reportedBy?.email && (
                          <span style={styles.value}>
                            {reportedBy?.fullName} ({reportedBy?.email})
                          </span>
                        )}
                        {reportedBy?.fullName && !reportedBy?.email && (
                          <span style={styles.value}>{reportedBy?.fullName}</span>
                        )}
                        {!reportedBy?.fullName && reportedBy?.email && (
                          <span style={styles.value}>{reportedBy?.email}</span>
                        )}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              {description && (
                <div style={styles.descBlock}>
                  <span style={styles.descLabel}>Description</span>
                  <span style={styles.descValue}>{description}</span>
                </div>
              )}
            </div>

            <div style={styles.ctaCenter}>
              <a href={eventUrl} style={styles.ctaBtn}>
                View Safety Event ↗
              </a>
            </div>

            <div style={styles.footer}>
              This is an automated notification from your UpKeep EHS system. Please do not reply to this email.
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
