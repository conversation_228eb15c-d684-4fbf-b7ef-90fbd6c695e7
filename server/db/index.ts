import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from '@shared/schema';
import { env } from 'env';

const { DATABASE_MAX_CONNECTIONS, DATABASE_IDLE_TIMEOUT_MS, DATABASE_CONNECTION_TIMEOUT_MS, DATABASE_URL } = env;

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: DATABASE_MAX_CONNECTIONS,
  idleTimeoutMillis: DATABASE_IDLE_TIMEOUT_MS, // How long to wait for a connection
  connectionTimeoutMillis: DATABASE_CONNECTION_TIMEOUT_MS, // How long to wait for a connection
});

export const db = drizzle({ client: pool, schema, logger: false });
