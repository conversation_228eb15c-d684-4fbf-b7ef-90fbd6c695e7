import { EmailUser, sendEmail } from '@server/services/email.service';
import { getCompany } from '@server/services/company.service';
import { getUsersPublic } from '@server/services/user.service';
import CommentMentionTemplate, { CommentMentionTemplateParams } from '@server/templates/comment-mention';
import { CommentMentionNotificationJobPayload } from '@shared/schema.types';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { env } from 'env';
import { toZonedTime } from 'date-fns-tz';
import React from 'react';

export const sendCommentMentionNotification = async ({
  newComment,
  savedMentions,
  user,
  headers,
  input,
}: CommentMentionNotificationJobPayload) => {
  const routeBuilder = {
    event: ROUTES.BUILD_EVENT_DETAILS_PATH,
    capa: ROUTES.BUILD_CAPA_DETAILS_PATH,
  };

  const toUserIds = savedMentions?.map((mention) => mention.userId) || [];

  const [users, { clientTimezone }] = await Promise.all([
    getUsersPublic({
      upkeepCompanyId: newComment.upkeepCompanyId,
      objectId: toUserIds,
    }),
    getCompany(headers),
  ]);

  const timestamp = clientTimezone ? toZonedTime(newComment.createdAt, clientTimezone) : newComment.createdAt;

  const updatedContent = newComment.content.replace(/@(\w+)/g, (_, userId) => {
    const foundUser = users.result.find((u) => u.id === userId);
    return foundUser ? `@${foundUser.fullName}` : `@${userId}`; // fallback to original if not found
  });

  const data: CommentMentionTemplateParams = {
    ...input,
    entityUrl: `${env.EHS_URL}${routeBuilder[input.entityType](input.entityId)}`,
    timestamp,
    reporter: {
      fullName: user.fullName,
      email: user.email,
    },
    content: updatedContent,
    users: users.result,
  };

  const toUsers: EmailUser[] = users.result
    .filter((user) => user.email && user.fullName)
    .map((user) => ({
      email: user.email!,
      fullName: user.fullName!,
      type: 'to',
    }));

  let contentWithNames = data.content;
  if (toUsers && toUsers.length > 0) {
    for (const user of toUsers) {
      if (user && user.fullName) {
        contentWithNames = contentWithNames.replace(`@${user.id}`, `@${user.fullName}`);
      }
    }
  }

  const template = React.createElement(CommentMentionTemplate, {
    ...data,
    content: contentWithNames,
    taggedUser: toUsers.map((user) => user.fullName).join(', '),
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `You've been tagged in a comment: ${data?.entitySlug} – ${data?.entityTitle}`,
  });
};
