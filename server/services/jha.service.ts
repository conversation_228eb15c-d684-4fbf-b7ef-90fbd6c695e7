import { db } from '@server/db';
import { processJhaResult } from '@server/services/jha-processing.service';
import { CreateFullJhaSchema } from '@shared/jha.types';
import { auditTrail, jha, jhaSteps } from '@shared/schema';
import { User } from '@shared/schema.types';
import { z } from 'zod';

export const createJha = async (input: z.infer<typeof CreateFullJhaSchema>, user: User) => {
  return db.transaction(async (tx) => {
    // Process the JHA result to bulk create hazards and control measures within the transaction
    const processedJhaResult = await processJhaResult(input, user, tx);

    const { jha: jhaInput, steps } = processedJhaResult;

    // calculate highestSeverity based on the steps
    const highestSeverity = steps.reduce((max, step) => {
      return Math.max(max, step.severity);
    }, 0);

    // create the jha
    const [jhaData] = await tx
      .insert(jha)
      .values({
        ...jhaInput,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
        highestSeverity,
      })
      .returning({
        id: jha.id,
        instanceId: jha.instanceId,
        slug: jha.slug,
        version: jha.version,
      });

    if (!jhaData?.id) {
      return;
    }

    // create the steps
    const stepsData = steps.map((step) => ({
      ...step,
      jhaId: jhaData.id,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));
    await tx.insert(jhaSteps).values(stepsData);

    // create audit trail with the data created
    await tx.insert(auditTrail).values({
      entityType: 'jha',
      entityId: jhaData.id,
      details: JSON.stringify(processedJhaResult),
      action: 'created',
      upkeepCompanyId: user.upkeepCompanyId,
    });

    return jhaData;
  });
};
