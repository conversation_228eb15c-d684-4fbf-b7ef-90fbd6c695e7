import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { accessPoints, accessPointStatusEnum, auditTrail, auditTrailActionEnum } from '@shared/schema';
import type {
  CreateAccessPointFormSchema,
  ExportAccessPointsSchema,
  IdSchema,
  ListAccessPointsSchema,
  UpdateAccessPointSchema,
  UpkeepCompanyIdSchema,
} from '@shared/schema.types';
import { User } from '@shared/schema.types';
import { endOfDay, startOfDay } from 'date-fns';
import { and, eq, gte, ilike, inArray, isNull, lte, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createAccessPointsBulk = async (input: z.infer<typeof CreateAccessPointFormSchema>[], user: User) => {
  return await db.transaction(async (tx) => {
    const now = new Date();

    const enrichedData = input.map((data) => ({
      ...data,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
      createdAt: now,
    }));

    const inserted = await tx.insert(accessPoints).values(enrichedData).returning();

    // Bulk audit logging
    const auditLogs = inserted.map((accessPoint) => ({
      entityId: accessPoint.id,
      entityType: 'access_point' as const,
      details: JSON.stringify(accessPoint),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: now,
      action: auditTrailActionEnum.enumValues[0], // 'created'
    }));

    await tx.insert(auditTrail).values(auditLogs);

    return inserted;
  });
};

export const updateAccessPoint = async (id: string, data: z.infer<typeof UpdateAccessPointSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const updated = await tx
      .update(accessPoints)
      .set({
        ...data,
        updatedAt: new Date(),
        status: data.archived && data.archived === true ? accessPointStatusEnum.enumValues[1] : data.status,
      })
      .where(and(eq(accessPoints.id, id), eq(accessPoints.upkeepCompanyId, user.upkeepCompanyId)))
      .returning();

    const accessPoint = updated.at(0);

    if (!accessPoint) {
      return;
    }

    await tx.insert(auditTrail).values({
      entityId: accessPoint.id,
      entityType: 'access_point',
      details: JSON.stringify(accessPoint),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: new Date(),
      action: 'updated',
    });

    return accessPoint;
  });
};

export const getAccessPointById = async (id: string, user: z.infer<typeof UpkeepCompanyIdSchema>) => {
  const [accessPoint] = await db
    .select()
    .from(accessPoints)
    .where(
      and(
        eq(accessPoints.id, id),
        eq(accessPoints.upkeepCompanyId, user.upkeepCompanyId),
        isNull(accessPoints.deletedAt),
      ),
    );
  return accessPoint || null;
};

export const listAccessPoints = async (input: z.infer<typeof ListAccessPointsSchema>, user: User) => {
  const {
    cursor = 0,
    limit = 10,
    search = '',
    status = [],
    locationId = [],
    createdBy = [],
    includeArchived = false,
    createdDateRange,
  } = input;

  // Get paginated results
  const data = await db
    .select({
      id: accessPoints.id,
      name: accessPoints.name,
      status: accessPoints.status,
      locationId: accessPoints.locationId,
      archived: accessPoints.archived,
      createdBy: accessPoints.createdBy,
      createdAt: accessPoints.createdAt,
      upkeepCompanyId: accessPoints.upkeepCompanyId,
    })
    .from(accessPoints)
    .where(
      and(
        eq(accessPoints.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? eq(accessPoints.archived, false) : undefined,
        search ? ilike(accessPoints.name, `%${search}%`) : undefined,
        status.length > 0 ? inArray(accessPoints.status, status) : undefined,
        locationId.length > 0 ? inArray(accessPoints.locationId, locationId) : undefined,
        createdBy.length > 0 ? inArray(accessPoints.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(accessPoints.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(accessPoints.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .limit(limit)
    .offset(cursor)
    .orderBy(accessPoints.createdAt);

  return createPaginatedResponse(data, { cursor, limit });
};

export const exportAccessPoints = async (input: z.infer<typeof ExportAccessPointsSchema>, user: User) => {
  const { status = [], locationId = [], createdBy = [], includeArchived = false, createdDateRange, search } = input;

  // Get paginated results
  const data = await db
    .select({
      id: accessPoints.id,
      name: accessPoints.name,
      description: accessPoints.description,
      status: accessPoints.status,
      locationId: accessPoints.locationId,
      archived: accessPoints.archived,
      createdBy: accessPoints.createdBy,
      createdAt: accessPoints.createdAt,
      upkeepCompanyId: accessPoints.upkeepCompanyId,
    })
    .from(accessPoints)
    .where(
      and(
        eq(accessPoints.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? eq(accessPoints.archived, false) : undefined,
        status.length > 0 ? inArray(accessPoints.status, status) : undefined,
        locationId.length > 0 ? inArray(accessPoints.locationId, locationId) : undefined,
        search ? ilike(accessPoints.name, `%${search}%`) : undefined,
        createdBy.length > 0 ? inArray(accessPoints.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(accessPoints.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(accessPoints.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .orderBy(accessPoints.createdAt)
    .limit(500);

  return data;
};

export const toggleArchiveAccessPoint = async (input: z.infer<typeof IdSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const now = new Date().toISOString();
    const updated = await tx
      .update(accessPoints)
      .set({
        archived: sql`CASE WHEN ${accessPoints.archivedAt} IS NULL THEN TRUE ELSE FALSE END`,
        archivedAt: sql`CASE WHEN ${accessPoints.archivedAt} IS NULL THEN CAST(${now} AS TIMESTAMP) ELSE NULL END`,
        status: sql`CASE WHEN ${accessPoints.archivedAt} IS NULL THEN ${accessPointStatusEnum.enumValues[1]}::access_point_status ELSE ${accessPointStatusEnum.enumValues[0]}::access_point_status END`,
      })
      .where(eq(accessPoints.id, input.id))
      .returning({
        id: accessPoints.id,
        name: accessPoints.name,
        archivedAt: accessPoints.archivedAt,
      });

    const accessPoint = updated.at(0);

    if (!accessPoint) {
      return;
    }

    const action = accessPoint.archivedAt ? 'unarchived' : 'archived';
    await tx.insert(auditTrail).values({
      entityId: accessPoint.id,
      entityType: 'access_point' as const,
      details: JSON.stringify(accessPoint),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return accessPoint;
  });
};
