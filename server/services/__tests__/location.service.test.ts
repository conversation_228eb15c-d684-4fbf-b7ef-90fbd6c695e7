import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the API module before importing anything that uses it
vi.mock('@server/utils/api', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
  },
  buildDefaultHeaders: vi.fn().mockImplementation((headers) => headers),
}));

// Import after mocking
import { getLocations, searchLocationsPublic } from '@server/services/location.service';
import api from '@server/utils/api';

describe('location.service.ts', () => {
  const mockHeaders = { 'x-upkeep-company-id': 'test-company' };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getLocations', () => {
    it('should include mustIncludeObjectIds in results when not present in initial search', async () => {
      // Mock initial search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [
            { id: 'loc1', stringName: 'Location 1' },
            { id: 'loc2', stringName: 'Location 2' },
          ],
        },
      });

      // Mock mustIncludeObjectIds search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [
            { id: 'loc3', stringName: 'Location 3' },
            { id: 'loc4', stringName: 'Location 4' },
          ],
        },
      });

      const result = await getLocations(
        {
          limit: 10,
          mustIncludeObjectIds: ['loc3', 'loc4'],
        },
        mockHeaders,
      );

      expect(result.result).toHaveLength(4);
      expect(result.result[0].id).toBe('loc3'); // First mustInclude location
      expect(result.result[1].id).toBe('loc4'); // Second mustInclude location
      expect(result.result[2].id).toBe('loc1'); // Original search results
      expect(result.result[3].id).toBe('loc2');
    });

    it('should not make additional request if mustIncludeObjectIds are already in results', async () => {
      // Mock initial search response with mustIncludeObjectIds already present
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [
            { id: 'loc1', stringName: 'Location 1' },
            { id: 'loc2', stringName: 'Location 2' },
            { id: 'loc3', stringName: 'Location 3' }, // This is in mustIncludeObjectIds
          ],
        },
      });

      const result = await getLocations(
        {
          limit: 10,
          mustIncludeObjectIds: ['loc3'],
        },
        mockHeaders,
      );

      expect(vi.mocked(api.post)).toHaveBeenCalledTimes(1); // Only one API call
      expect(result.result).toHaveLength(3);
      expect(result.result.some((loc) => loc.id === 'loc3')).toBe(true);
    });
  });

  describe('searchLocationsPublic', () => {
    it('should include mustIncludeObjectIds in public search results when not present', async () => {
      // Mock initial search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [
            { id: 'loc1', stringName: 'Location 1' },
            { id: 'loc2', stringName: 'Location 2' },
          ],
        },
      });

      // Mock mustIncludeObjectIds search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [{ id: 'loc3', stringName: 'Location 3' }],
        },
      });

      const result = await searchLocationsPublic({
        upkeepCompanyId: 'test-role',
        search: '',
        limit: 10,
        mustIncludeObjectIds: ['loc3'],
      });

      expect(result.result).toHaveLength(3);
      expect(result.result[0].id).toBe('loc3'); // mustInclude location first
      expect(result.result[1].id).toBe('loc1');
      expect(result.result[2].id).toBe('loc2');
    });

    it('should not make additional request if mustIncludeObjectIds are in public search results', async () => {
      // Mock initial search response with mustIncludeObjectIds already present
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [
            { id: 'loc1', stringName: 'Location 1' },
            { id: 'loc3', stringName: 'Location 3' }, // This is in mustIncludeObjectIds
          ],
        },
      });

      const result = await searchLocationsPublic({
        upkeepCompanyId: 'test-role',
        search: '',
        limit: 10,
        mustIncludeObjectIds: ['loc3'],
      });

      expect(vi.mocked(api.post)).toHaveBeenCalledTimes(1); // Only one API call
      expect(result.result).toHaveLength(2);
      expect(result.result.some((loc) => loc.id === 'loc3')).toBe(true);
    });
  });
});
