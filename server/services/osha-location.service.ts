import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, oshaLocations } from '@shared/schema';
import { User } from '@shared/schema.types';
import {
  ArchiveOshaLocation,
  CreateOshaLocation,
  ExportOshaLocationsSchema,
  ListOshaLocationsSchema,
} from '@shared/settings.types';
import { TRPCError } from '@trpc/server';
import { endOfDay, startOfDay } from 'date-fns';
import { and, asc, eq, gte, ilike, inArray, isNull, lte, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createOshaLocation = async (input: CreateOshaLocation, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
      createdAt: new Date(),
    };

    const inserted = await tx.insert(oshaLocations).values(values).returning({
      id: oshaLocations.id,
      name: oshaLocations.name,
    });

    const oshaLocation = inserted.at(0);

    if (!oshaLocation) {
      return;
    }

    await tx.insert(auditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: oshaLocation.id,
      entityType: 'global_location',
      action: 'created',
      userId: user.id,
      details: JSON.stringify(values),
    });
    return oshaLocation;
  });
};

export const listOshaLocations = async (input: z.infer<typeof ListOshaLocationsSchema>, user: User) => {
  const {
    cursor = 0,
    limit = 10,
    search,
    createdBy = [],
    includeArchived = false,
    createdDateRange,
    mustIncludeObjectIds,
  } = input;

  let query = db
    .select({
      id: oshaLocations.id,
      name: oshaLocations.name,
      upkeepCompanyId: oshaLocations.upkeepCompanyId,
      createdBy: oshaLocations.createdBy,
      createdAt: oshaLocations.createdAt,
      archivedAt: oshaLocations.archivedAt,
    })
    .from(oshaLocations)
    .where(
      and(
        eq(oshaLocations.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(oshaLocations.archivedAt),
        search ? ilike(oshaLocations.name, `%${search}%`) : undefined,
        createdBy.length > 0 ? inArray(oshaLocations.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(oshaLocations.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(oshaLocations.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .$dynamic();

  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0) {
    query = query.orderBy(
      sql`CASE WHEN ${oshaLocations.id} IN (${mustIncludeObjectIds}) THEN 0 ELSE 1 END`,
      asc(oshaLocations.createdAt),
    );
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const toggleArchiveOshaLocation = async (input: ArchiveOshaLocation, user: User) => {
  return db.transaction(async (tx) => {
    // Soft delete by setting archivedAt timestamp
    const now = new Date().toISOString();
    const updated = await tx
      .update(oshaLocations)
      .set({
        archivedAt: sql`CASE WHEN ${oshaLocations.archivedAt} IS NULL THEN CAST(${now} AS TIMESTAMP) ELSE NULL END`,
      })
      .where(eq(oshaLocations.id, input.id))
      .returning({
        id: oshaLocations.id,
        name: oshaLocations.name,
        archivedAt: oshaLocations.archivedAt,
      });

    const deletedLocation = updated.at(0);

    if (!deletedLocation) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to archive OSHA location' });
    }

    const action = deletedLocation.archivedAt ? 'unarchived' : 'archived';
    // Add audit trail entry
    await tx.insert(auditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: deletedLocation.id,
      entityType: 'global_location',
      action,
      userId: user.id,
      details: JSON.stringify({ name: deletedLocation.name }),
    });

    return deletedLocation;
  });
};

export const exportOshaLocations = async (input: z.infer<typeof ExportOshaLocationsSchema>, user: User) => {
  const { search, includeArchived, createdBy = [], createdDateRange } = input;

  return await db
    .select()
    .from(oshaLocations)
    .where(
      and(
        eq(oshaLocations.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(oshaLocations.archivedAt),
        search ? ilike(oshaLocations.name, `%${search}%`) : undefined,
        createdBy.length > 0 ? inArray(oshaLocations.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(oshaLocations.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(oshaLocations.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .limit(500);
};
