import { db } from '@server/db';
import { OshaAuditTrail } from '@shared/osha.types';
import { oshaAuditTrail, oshaEntityTypeEnum } from '@shared/schema';
import { User } from '@shared/schema.types';
import { and, desc, eq } from 'drizzle-orm';

export const getAuditTrail = async (id: string, user: User) => {
  return db
    .select()
    .from(oshaAuditTrail)
    .where(
      and(
        eq(oshaAuditTrail.entityId, id),
        eq(oshaAuditTrail.upkeepCompanyId, user.upkeepCompanyId),
        eq(oshaAuditTrail.entityType, oshaEntityTypeEnum.enumValues[0]),
      ),
    )
    .orderBy(desc(oshaAuditTrail.createdAt));
};

export const createOshaAuditTrail = async (input: OshaAuditTrail, user: User) => {
  return db.insert(oshaAuditTrail).values({
    ...input,
    upkeepCompanyId: user.upkeepCompanyId,
    createdBy: user.id,
  });
};
