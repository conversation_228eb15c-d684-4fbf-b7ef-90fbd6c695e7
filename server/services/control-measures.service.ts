import { db } from '@server/db';
import { auditTrail, controlMeasures } from '@shared/schema';
import { User } from '@shared/schema.types';
import { ControlMeasuresCreateSchema, BulkControlMeasuresCreateSchema } from '@shared/settings.types';
import { and, eq, isNull, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createControlMeasure = async (input: z.infer<typeof ControlMeasuresCreateSchema>, user: User) => {
  return db.transaction(async (tx) => {
    const [controlMeasure] = await tx
      .insert(controlMeasures)
      .values({
        ...input,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
      })
      .returning();

    if (!controlMeasure) {
      return;
    }

    return controlMeasure;
  });
};

export const getControlMeasures = async (user: User) => {
  return db
    .select()
    .from(controlMeasures)
    .where(and(eq(controlMeasures.upkeepCompanyId, user.upkeepCompanyId), isNull(controlMeasures.archivedAt)));
};

export const toggleArchive = async (id: string, user: User) => {
  return db.transaction(async (tx) => {
    const [controlMeasure] = await tx
      .update(controlMeasures)
      .set({ archivedAt: sql`CASE WHEN ${controlMeasures.archivedAt} IS NULL THEN NOW() ELSE NULL END` })
      .where(eq(controlMeasures.id, id))
      .returning({ archivedAt: controlMeasures.archivedAt });

    if (!controlMeasure) {
      return;
    }

    const archivedAt = controlMeasure.archivedAt;
    const action = archivedAt ? 'archived' : 'unarchived';

    await tx.insert(auditTrail).values({
      entityType: 'control_measure',
      entityId: id,
      action,
      details: JSON.stringify(controlMeasure),
      upkeepCompanyId: user.upkeepCompanyId,
    });
  });
};

export const bulkCreateControlMeasures = async (
  input: z.infer<typeof BulkControlMeasuresCreateSchema>,
  user: User,
  tx?: Parameters<Parameters<typeof db.transaction>[0]>[0],
) => {
  const createControlMeasures = async (transaction: typeof tx) => {
    const controlMeasureToCreate = input.map((controlMeasure) => ({
      ...controlMeasure,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));

    const results = await transaction!
      .insert(controlMeasures)
      .values(controlMeasureToCreate)
      .onConflictDoUpdate({
        target: [controlMeasures.name, controlMeasures.type, controlMeasures.upkeepCompanyId],
        set: {
          updatedAt: new Date(),
          archivedAt: null, // Unarchive if it was archived
        },
      })
      .returning();

    return results;
  };

  if (tx) {
    return createControlMeasures(tx);
  }

  return db.transaction(createControlMeasures);
};
