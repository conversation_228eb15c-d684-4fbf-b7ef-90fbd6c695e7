import { db } from '@server/db';
import { HazardsCreateSchema } from '@shared/settings.types';
import { BulkHazardsCreateSchema } from '@shared/settings.types';
import { auditTrail, hazards } from '@shared/schema';
import { User } from '@shared/schema.types';
import { and, eq, isNull, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createHazard = async (input: z.infer<typeof HazardsCreateSchema>, user: User) => {
  return db.transaction(async (tx) => {
    const [hazard] = await tx
      .insert(hazards)
      .values({
        ...input,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
      })
      .returning();

    if (!hazard) {
      return;
    }

    return hazard;
  });
};

export const getHazards = async (user: User) => {
  return db
    .select()
    .from(hazards)
    .where(and(eq(hazards.upkeepCompanyId, user.upkeepCompanyId), isNull(hazards.archivedAt)));
};

export const toggleArchive = async (id: string, user: User) => {
  return db.transaction(async (tx) => {
    const [hazard] = await tx
      .update(hazards)
      .set({ archivedAt: sql`CASE WHEN ${hazards.archivedAt} IS NULL THEN NOW() ELSE NULL END` })
      .where(eq(hazards.id, id))
      .returning();

    if (!hazard) {
      return;
    }

    const archivedAt = hazard.archivedAt;
    const action = archivedAt ? 'archived' : 'unarchived';

    await tx.insert(auditTrail).values({
      entityType: 'hazard',
      entityId: hazard.id,
      action,
      details: JSON.stringify(hazard),
      upkeepCompanyId: user.upkeepCompanyId,
    });
  });
};

export const bulkCreateHazards = async (
  input: z.infer<typeof BulkHazardsCreateSchema>,
  user: User,
  tx?: Parameters<Parameters<typeof db.transaction>[0]>[0],
) => {
  const createHazards = async (transaction: typeof tx) => {
    const hazardToCreate = input.map((hazard) => ({
      ...hazard,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));

    const results = await transaction!
      .insert(hazards)
      .values(hazardToCreate)
      .onConflictDoUpdate({
        target: [hazards.name, hazards.type, hazards.upkeepCompanyId],
        set: {
          updatedAt: new Date(),
          archivedAt: null, // Unarchive if it was archived
        },
      })
      .returning();

    return results;
  };

  if (tx) {
    return createHazards(tx);
  }

  return db.transaction(createHazards);
};
