import { getCompany } from '@server/services/company.service';
import { EmailUser, sendEmail } from '@server/services/email.service';
import { getEventById } from '@server/services/event.service';
import { getLocationById } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import CapaAssignedTemplate, { CapaAssignedTemplateParams } from '@server/templates/capa-assigned';
import CapaOverdueTemplate, { CapaOverdueTemplateParams } from '@server/templates/capa-overdue';
import CapaUpdateTemplate, { CapaUpdateTemplateParams } from '@server/templates/capa-update';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { CapaAssignedNotificationJobPayload, CapaUpdateNotificationJobPayload } from '@shared/schema.types';
import { toZonedTime } from 'date-fns-tz';
import { env } from 'env';
import React from 'react';

export const sendCapaOverdueNotification = async ({
  capa,
  toUsers,
}: {
  capa: CapaOverdueTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(CapaOverdueTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA Overdue: ${capa.slug} - ${capa.title}`,
  });
};

export const sendCapaAssignedNotification = async ({
  capa,
  user,
  headers,
  needPartialCheck,
}: CapaAssignedNotificationJobPayload) => {
  const teamMembers =
    capa?.teamMembersToNotify && capa?.teamMembersToNotify?.length > 0 ? capa?.teamMembersToNotify : [];

  const allUserIds = teamMembers.concat(capa.ownerId!);
  const toNotifyUserIds = [...new Set(allUserIds)];

  const [location, event, usersToNotify, { clientTimezone }] = await Promise.all([
    capa?.locationId ? getLocationById(capa?.locationId, headers) : undefined,
    capa?.eventId ? getEventById(capa?.eventId, user, needPartialCheck) : undefined,
    getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: toNotifyUserIds }),
    getCompany(headers),
  ]);

  const owner = usersToNotify.result.find((u) => u.id === capa?.ownerId);
  const timestamp = clientTimezone && capa.dueDate ? toZonedTime(capa.dueDate, clientTimezone) : null;

  await sendCapaAssignedNotificationEmail({
    capa: {
      ...capa,
      dueDate: timestamp,
      linkedEvent: event
        ? {
            title: event?.title,
            slug: event?.slug!,
            url: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(event?.id)}`,
          }
        : undefined,
      owner: {
        email: owner?.email,
        fullName: owner?.fullName,
      },
      location: location,
      capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id!)}`,
    },
    toUsers: [
      { email: user.email, fullName: user.fullName, type: 'to' },
      ...usersToNotify.result
        .filter((u) => u.email && u.fullName)
        .map((u) => ({
          email: u.email!,
          fullName: u.fullName!,
          type: 'to' as const,
        })),
    ],
  });
};

export const sendCapaAssignedNotificationEmail = async ({
  capa,
  toUsers,
}: {
  capa: CapaAssignedTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(CapaAssignedTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA Assigned: ${capa.slug} - ${capa.title}`,
  });
};

export const sendCapaUpdatedNotification = async ({
  capa,
  toUsers,
  actionPrefix = 'Updated',
}: {
  capa: CapaUpdateTemplateParams;
  toUsers: EmailUser[];
  actionPrefix?: string;
}) => {
  const template = React.createElement(CapaUpdateTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA ${actionPrefix}: ${capa.slug} - ${capa.title}`,
  });
};

export const sendCapaUpdateNotification = async ({
  capa,
  user,
  headers,
  needPartialCheck,
  actionPrefix,
}: CapaUpdateNotificationJobPayload) => {
  const teamMembers =
    capa?.teamMembersToNotify && capa?.teamMembersToNotify?.length > 0 ? capa?.teamMembersToNotify : [];

  const allUserIds = teamMembers.concat(capa.ownerId!);
  const toNotifyUserIds = [...new Set(allUserIds)];

  const [location, event, usersToNotify, { clientTimezone }] = await Promise.all([
    capa?.locationId ? getLocationById(capa?.locationId, headers) : undefined,
    capa?.eventId ? getEventById(capa?.eventId, user, needPartialCheck) : undefined,
    getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: toNotifyUserIds }),
    getCompany(headers),
  ]);

  const owner = usersToNotify.result.find((user) => user.id === capa?.ownerId);
  const timestamp = clientTimezone && capa.dueDate ? toZonedTime(capa.dueDate, clientTimezone) : null;

  await sendCapaUpdatedNotification({
    capa: {
      ...capa,
      dueDate: timestamp,
      linkedEvent: event
        ? {
            title: event?.title,
            slug: event?.slug!,
            url: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(event?.id)}`,
          }
        : undefined,
      owner: {
        email: owner?.email,
        fullName: owner?.fullName,
      },
      updatedBy: {
        email: user.email,
        fullName: user.fullName,
      },
      location: location,
      capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id!)}`,
    },
    toUsers: [
      { email: user.email, fullName: user.fullName, type: 'to' },
      ...usersToNotify.result
        .filter((user) => user.email && user.fullName)
        .map((user) => ({
          email: user.email!,
          fullName: user.fullName!,
          type: 'to' as const,
        })),
    ],
    actionPrefix,
  });
};
