import { openai } from '@ai-sdk/openai';
import { logger } from '@server/utils/logger';
import {
  CreateCapasForm,
  CreateCapasSchema,
  HazardCategorySchema,
  Location,
  ReportTypeSchema,
  SeveritySchema,
  StatusSchema,
  User,
} from '@shared/schema.types';
import { generateObject, experimental_transcribe as transcribe } from 'ai';
import { addDays } from 'date-fns';
import { OpenAIModel } from 'env';
import { z } from 'zod';
import {
  getCapaPrompts,
  getCapaSummaryPrompt,
  getEventPrompts,
  getJhaPrompts,
  getLocationMatchPrompt,
} from './prompts.service';
import { CreateFullJhaSchema } from '@shared/jha.types';

export const transcribeAudio = async (audio: Buffer) => {
  const transcript = await transcribe({
    model: openai.transcription('whisper-1'),
    audio: audio,
  });

  return transcript;
};

export const AnalyzeEventTranscriptSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: HazardCategorySchema,
  severity: SeveritySchema.optional().default(SeveritySchema.enum.low),
  type: ReportTypeSchema.optional(),
  status: StatusSchema.optional(),
  immediateActions: z.string().optional(),
  reportedAt: z.coerce.date().optional(),
});

export type AnalyzeEventTranscript = z.infer<typeof AnalyzeEventTranscriptSchema>;

export const analyzeEventTranscript = async (
  transcript: string,
  timezone?: string,
): Promise<AnalyzeEventTranscript> => {
  const { systemPrompt, generateUserPrompt } = getEventPrompts();

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_4_TURBO),
    schema: AnalyzeEventTranscriptSchema,
    system: systemPrompt,
    prompt: generateUserPrompt(transcript, timezone),
  });

  return result.object;
};

// Start with the base CAPA creation schema, but remove fields that should NOT be provided by AI
const AnalyzeCapaTranscriptSchema = CreateCapasSchema.pick({
  rcaMethod: true,
  rcaFindings: true,
  rootCauses: true,
  otherRootCause: true,
  title: true,
  priority: true,
  actionsToAddress: true,
  tags: true,
  type: true,
  summary: true,
}).extend({
  // Ensure the due date can be coerced from strings like "2025-06-03" to a Date object
  dueDate: z.coerce.date().optional(),
});

export type AnalyzeCapaTranscript = z.infer<typeof AnalyzeCapaTranscriptSchema>;

export const analyzeCapaTranscript = async (transcript: string, timezone?: string): Promise<AnalyzeCapaTranscript> => {
  const { systemPrompt, generateUserPrompt } = getCapaPrompts();

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_4_TURBO),
    schema: AnalyzeCapaTranscriptSchema,
    system: systemPrompt,
    prompt: generateUserPrompt(transcript, timezone),
  });

  // Further validate that dueDate is in the future
  if (result.object.dueDate && new Date(result.object.dueDate) < new Date()) {
    // If the parsed date is in the past, add appropriate days to make it future
    // This is a fallback in case the model still produces a past date
    const correctedDate = addDays(new Date(), 7); // Default to one week from now
    result.object.dueDate = correctedDate;
  }

  return result.object;
};

export const createCapaSummary = async (capa: CreateCapasForm): Promise<string> => {
  const { systemPrompt, generateUserPrompt } = getCapaSummaryPrompt(capa);

  try {
    const result = await generateObject({
      model: openai(OpenAIModel.GPT_3_5_TURBO),
      schema: z.object({
        summary: z.string(),
      }),
      system: systemPrompt,
      prompt: generateUserPrompt,
    });

    return result.object.summary;
  } catch (error) {
    logger.error('Error creating CAPA summary:', { error, capa });
    return '';
  }
};

const MatchSchema = z.object({
  locationId: z.string().optional(),
  locationName: z.string().optional(),
  reason: z.string().optional(),
});

export const matchLocationsWithAI = async (location: string, locations: Location[]) => {
  try {
    const { systemPrompt, userPrompt } = getLocationMatchPrompt(location, locations);

    const result = await generateObject({
      model: openai(OpenAIModel.GPT_4O),
      schema: MatchSchema,
      system: systemPrompt,
      prompt: userPrompt,
    });

    return result.object;
  } catch (error) {
    logger.error('AI location matching failed:', { location, error });
    // Return empty result on failure
    return {
      locationId: undefined,
      locationName: undefined,
      reason: 'AI matching failed',
    };
  }
};

export const analyzeJhaTranscript = async (transcript: string, user: User) => {
  const { systemPrompt, generateUserPrompt } = await getJhaPrompts({ transcript, user });

  const model = openai(OpenAIModel.GPT_4_1);

  const result = await generateObject({
    model,
    schema: CreateFullJhaSchema,
    system: systemPrompt,
    prompt: generateUserPrompt,
  });

  return result.object;
};

export const analyzeJhaDocument = async (analyzeJhaDocumentInput: { document: Buffer; user: User }) => {
  const { document, user } = analyzeJhaDocumentInput;
  const { systemPrompt, generateUserPrompt } = await getJhaPrompts({ user, document: true });

  const model = openai(OpenAIModel.GPT_4_1);

  const result = await generateObject({
    model,
    schema: CreateFullJhaSchema,
    system: systemPrompt,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: generateUserPrompt,
          },
          {
            type: 'file',
            data: document,
            mimeType: 'application/pdf',
          },
        ],
      },
    ],
  });

  return result.object;
};
