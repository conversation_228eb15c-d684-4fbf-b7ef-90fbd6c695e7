import { db } from '@server/db';
import { auditTrail, auditTrailActionEnum, commentMentions, comments } from '@shared/schema';
import { CreateCommentFormSchema, IdSchema, ListCommentsSchema, User } from '@shared/schema.types';
import { and, asc, eq, isNull } from 'drizzle-orm';
import { z } from 'zod';

export const createComment = async (
  { entityId, entityType, content }: z.infer<typeof CreateCommentFormSchema>,
  user: User,
) => {
  return db.transaction(async (tx) => {
    // Create the comment first
    const inserted = await tx
      .insert(comments)
      .values({
        ...(entityType === 'event' ? { eventId: entityId } : { capaId: entityId }),
        content,
        userId: user.id,
        upkeepCompanyId: user.upkeepCompanyId,
      })
      .returning();

    const newComment = inserted.at(0);

    if (!newComment) {
      return;
    }

    await tx.insert(auditTrail).values({
      entityId,
      entityType,
      details: JSON.stringify(newComment),
      userId: newComment.userId,
      upkeepCompanyId: newComment.upkeepCompanyId,
      action: auditTrailActionEnum.enumValues[3],
    });

    // Extract mentions from content
    const mentions = content.match(/@[a-zA-Z0-9]+/g);

    if (!mentions || mentions.length === 0) {
      return { newComment, savedMentions: undefined };
    }

    const mentionsToInsert = mentions.map((mention) => {
      const mentionedUserId = mention.slice(1); // Remove @ symbol
      const position = content.indexOf(mention);

      return {
        commentId: newComment.id,
        userId: mentionedUserId,
        position,
        mentionText: mention,
      };
    });

    const savedMentions = await tx.insert(commentMentions).values(mentionsToInsert).returning();

    return { newComment, savedMentions };
  });
};

export const fetchComments = async (
  { entityId, entityType, options }: z.infer<typeof ListCommentsSchema>,
  user: User,
) => {
  const { limit = 50, offset = 0 } = options || {};

  // Build the where condition based on entity type
  const whereCondition =
    entityType === 'event'
      ? and(eq(comments.eventId, entityId), isNull(comments.capaId))
      : and(eq(comments.capaId, entityId), isNull(comments.eventId));

  // Fetch comments with their mentions
  const commentsWithMentions = await db
    .select({
      id: comments.id,
      content: comments.content,
      createdAt: comments.createdAt,
      updatedAt: comments.updatedAt,
      userId: comments.userId,
      upkeepCompanyId: comments.upkeepCompanyId,
      eventId: comments.eventId,
      capaId: comments.capaId,
      mentions: {
        id: commentMentions.id,
        userId: commentMentions.userId,
        position: commentMentions.position,
        mentionText: commentMentions.mentionText,
        createdAt: commentMentions.createdAt,
      },
    })
    .from(comments)
    .leftJoin(commentMentions, eq(comments.id, commentMentions.commentId))
    .where(and(whereCondition, eq(comments.upkeepCompanyId, user.upkeepCompanyId)))
    .orderBy(asc(comments.createdAt))
    .limit(limit)
    .offset(offset);

  type GroupedComment = {
    id: string;
    content: string;
    createdAt: Date;
    updatedAt: Date | null;
    userId: string;
    upkeepCompanyId: string;
    eventId: string | null;
    capaId: string | null;
    mentions: Array<{
      id: string;
      userId: string;
      position: number | null;
      mentionText: string | null;
      createdAt: Date | null;
    }>;
  };

  // Group mentions by comment
  const groupedComments = commentsWithMentions.reduce(
    (acc, row) => {
      const commentId = row.id;

      if (!acc[commentId]) {
        acc[commentId] = {
          id: row.id,
          content: row.content,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          userId: row.userId,
          upkeepCompanyId: row.upkeepCompanyId,
          eventId: row.eventId,
          capaId: row.capaId,
          mentions: [],
        };
      }

      // Add mention if it exists
      if (row.mentions?.id) {
        acc[commentId].mentions.push(row.mentions);
      }

      return acc;
    },
    {} as Record<string, GroupedComment>,
  );

  return Object.values(groupedComments);
};

export const fetchCommentById = async ({ id }: z.infer<typeof IdSchema>, user: User) => {
  const result = await db
    .select({
      id: comments.id,
      content: comments.content,
      createdAt: comments.createdAt,
      updatedAt: comments.updatedAt,
      userId: comments.userId,
      upkeepCompanyId: comments.upkeepCompanyId,
      eventId: comments.eventId,
      capaId: comments.capaId,
      mentions: {
        id: commentMentions.id,
        userId: commentMentions.userId,
        position: commentMentions.position,
        mentionText: commentMentions.mentionText,
        createdAt: commentMentions.createdAt,
      },
    })
    .from(comments)
    .leftJoin(commentMentions, eq(comments.id, commentMentions.commentId))
    .where(and(eq(comments.id, id), eq(comments.upkeepCompanyId, user.upkeepCompanyId)));

  if (result.length === 0) {
    return null;
  }

  // Group mentions for the single comment
  const comment = {
    id: result[0].id,
    content: result[0].content,
    createdAt: result[0].createdAt,
    updatedAt: result[0].updatedAt,
    userId: result[0].userId,
    upkeepCompanyId: result[0].upkeepCompanyId,
    eventId: result[0].eventId,
    capaId: result[0].capaId,
    mentions: result.filter((row) => row.mentions?.id).map((row) => row.mentions!),
  };

  return comment;
};

export const deleteComment = async ({ id }: z.infer<typeof IdSchema>, user: User) => {
  // First, delete all mentions for this comment
  await db.delete(commentMentions).where(eq(commentMentions.commentId, id));

  // Then delete the comment itself, but only if the user owns it or has appropriate permissions
  const result = await db
    .delete(comments)
    .where(
      and(
        eq(comments.id, id),
        eq(comments.upkeepCompanyId, user.upkeepCompanyId),
        // Optionally, only allow the comment author to delete their own comments
        // eq(comments.userId, user.id)
      ),
    )
    .returning();

  return result.length > 0 ? { success: true, id } : { success: false, id };
};
