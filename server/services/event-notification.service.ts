import { getAssets, searchAssetsPublic } from '@server/services/asset.service';
import { getCompany } from '@server/services/company.service';
import { EmailUser, sendEmail } from '@server/services/email.service';
import { getLocationById, searchLocationsPublic } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import EventCreateTemplate, { EventCreateTemplateParams } from '@server/templates/event-create';
import EventUpdateTemplate, { EventUpdateTemplateParams } from '@server/templates/event-update';
import { type Headers } from '@server/utils/api';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { EventCreateNotificationJobPayload, EventPublicCreateNotificationJobPayload, User } from '@shared/schema.types';
import { toZonedTime } from 'date-fns-tz';
import { env } from 'env';
import React from 'react';

export const sendPublicEventCreateNotification = async ({
  createdEvent,
  upkeepCompanyId,
  reporterInfo,
  teamMembersToNotify,
}: EventPublicCreateNotificationJobPayload) => {
  const [location, assets] = await Promise.all([
    createdEvent.locationId
      ? searchLocationsPublic({
          upkeepCompanyId,
          search: '',
          objectId: [createdEvent.locationId],
          limit: 1,
        })
      : undefined,
    createdEvent.assetIds && createdEvent.assetIds.length > 0
      ? searchAssetsPublic({
          upkeepCompanyId,
          search: '',
          objectId: createdEvent.assetIds,
          limit: 100,
        })
      : { noResults: true, result: [], nextCursor: undefined },
  ]);

  const eventData: EventCreateTemplateParams = {
    ...createdEvent,
    location: location?.result?.at(0),
    assets: assets?.result || [],
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
    reportedBy: {
      email: reporterInfo.email,
      fullName: reporterInfo.fullName,
    },
  };

  const toUsers: EmailUser[] = [
    {
      email: reporterInfo.email,
      fullName: reporterInfo.fullName,
      type: 'to',
    },
    ...teamMembersToNotify.map((user) => ({
      email: user.email,
      fullName: user.fullName,
      type: 'to' as const,
    })),
  ];

  const template = React.createElement(EventCreateTemplate, {
    ...eventData,
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `New Safety Event Submitted: ${createdEvent.title}`,
  });
};

export const sendEventCreateNotification = async ({
  createdEvent,
  user,
  headers,
  teamMembersToNotify,
}: EventCreateNotificationJobPayload) => {
  const [location, assets, teamMembersData, { clientTimezone }] = await Promise.all([
    createdEvent.locationId ? getLocationById(createdEvent.locationId, headers) : undefined,
    createdEvent.assetIds && createdEvent.assetIds.length > 0
      ? getAssets(
          {
            objectId: createdEvent.assetIds,
            cursor: 0,
            limit: 100,
          },
          headers,
        )
      : { result: [] },
    teamMembersToNotify && teamMembersToNotify.length > 0
      ? getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: teamMembersToNotify })
      : { noResults: true, result: [], nextCursor: undefined },
    getCompany(headers),
  ]);

  const usersToNotify = [...teamMembersData.result, user];
  const uniqueUsersToNotify = [...new Map(usersToNotify.map((user) => [user.id, user])).values()];

  const timestamp =
    clientTimezone && createdEvent.reportedAt
      ? toZonedTime(createdEvent.reportedAt, clientTimezone)
      : createdEvent.reportedAt;

  const eventData: EventCreateTemplateParams = {
    ...createdEvent,
    reportedAt: timestamp!,
    location,
    assets: assets.result,
    reportedBy: {
      email: user.email,
      fullName: user.fullName,
    },
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
  };

  const toUsers: EmailUser[] = [
    ...uniqueUsersToNotify
      .filter((user) => user.email && user.fullName)
      .map((user) => ({
        email: user.email!,
        fullName: user.fullName!,
        type: 'to' as const,
      })),
  ];

  const template = React.createElement(EventCreateTemplate, {
    ...eventData,
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `New Safety Event Submitted: ${createdEvent.title}`,
  });
};

export const sendEventUpdateNotification = async ({
  event,
  toUsers,
  actionPrefix = 'Updated',
}: {
  event: EventUpdateTemplateParams;
  toUsers: EmailUser[];
  actionPrefix?: string;
}) => {
  const template = React.createElement(EventUpdateTemplate, {
    ...event,
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(event.id)}`,
  });

  const emailSubject = `Safety Event ${actionPrefix}: ${event.slug} - ${event.title}`;

  await sendEmail(template, {
    to: toUsers,
    subject: emailSubject,
  });
};

export const sendEventUpdateNotificationWithUser = async ({
  updatedEvent,
  user,
  headers,
  actionPrefix = 'Updated',
}: {
  updatedEvent: RouterOutputs['event']['update'];
  user: User;
  headers: Headers;
  actionPrefix?: string;
}) => {
  const [location, usersToNotify, { clientTimezone }] = await Promise.all([
    updatedEvent.locationId ? getLocationById(updatedEvent.locationId, headers) : undefined,
    updatedEvent?.teamMembersToNotify && updatedEvent?.teamMembersToNotify?.length > 0
      ? getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: updatedEvent.teamMembersToNotify })
      : { noResults: true, result: [], nextCursor: undefined },
    getCompany(headers),
  ]);

  const timestamp =
    clientTimezone && updatedEvent.reportedAt
      ? toZonedTime(updatedEvent.reportedAt, clientTimezone)
      : updatedEvent.reportedAt;

  const event: EventUpdateTemplateParams = {
    ...updatedEvent,
    action: updatedEvent.archived ? 'archived' : 'unarchived',
    location,
    reportedAt: timestamp!,
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(updatedEvent.id!)}`,
    reportedBy: {
      email: user.email,
      fullName: user.fullName,
    },
  };

  const toUsers: EmailUser[] = [
    {
      email: user.email,
      fullName: user.fullName,
      type: 'to' as const,
    },
    ...usersToNotify.result
      .filter((user) => user.email && user.fullName)
      .map((user) => ({
        email: user.email!,
        fullName: user.fullName!,
        type: 'to' as const,
      })),
  ];

  await sendEventUpdateNotification({
    event,
    toUsers,
    actionPrefix,
  });
};
