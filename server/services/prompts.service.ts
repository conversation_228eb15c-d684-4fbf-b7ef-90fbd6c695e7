import { MORE_THAN_ONE_LOCATION_MATCHED, NO_LOCATION_MATCHED } from '@server/utils/match-single-location';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, rcaMethodEnum } from '@shared/schema';
import {
  ControlMeasureCategorySchema,
  CreateCapasForm,
  HazardCategorySchema,
  Location,
  ReportTypeSchema,
  RootCauseSchema,
  SeveritySchema,
  StatusSchema,
  User,
} from '@shared/schema.types';
import { addDays, format, subDays } from 'date-fns';

export const getEventPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd HH:mm:ss');

  // Example past dates for the prompt
  const lastWeekDate = format(subDays(currentDate, 7), 'yyyy-MM-dd');
  const lastMonthDate = format(subDays(currentDate, 30), 'yyyy-MM-dd');
  const yesterday = format(addDays(currentDate, 1), 'yyyy-MM-dd');

  const systemPrompt =
    'You are an AI assistant specialized in extracting detailed and accurate structured information about safety events from transcripts. ' +
    'Your task is to precisely identify key event details while strictly adhering to the provided categorization systems. ' +
    'Extract only information that is explicitly mentioned in the transcript. Do not make assumptions or add information not directly stated. ' +
    'Ensure categorizations match the exact values specified in the schema. If uncertain about a field, omit it rather than guessing.' +
    `Today's date is ${formattedCurrentDate} for reference. When you see "last week", it refers to a date like ${lastWeekDate}, not a date in the future. ` +
    `When you see "last month", it refers to a date like ${lastMonthDate}, not a date in the future. ` +
    `When you see "yesterday", it refers to a date like ${yesterday}, not a date in the future. ` +
    "If the user doesn't provide date, you may assume the current date." +
    'The response should always be in english, despite the transcript being in another language.';

  const generateUserPrompt = (
    transcript: string,
    timezone?: string,
  ) => `Extract comprehensive details from this safety event transcript. Return ONLY a JSON object with these precise keys:
  
            title (a clear, concise title summarizing the event)
            description (a detailed description of what happened)
            location (specific location where the event occurred)
            type (choose one of the following must be one of: ${ReportTypeSchema.options.join(', ')})
            category (if mentioned: ${HazardCategorySchema.options.join(', ')})
            severity (if mentioned: ${SeveritySchema.options.join(', ')})
            status (if mentioned: ${StatusSchema.options.join(', ')})            
            immediateActions (any immediate actions taken after the event)            
            reportedAt (date and time the event was reported) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone


            IMPORTANT DATE HANDLING INSTRUCTIONS:
            - Today's date is ${formattedCurrentDate}
            - If the transcript mentions "last week", use a date around ${lastWeekDate}
            - If the transcript mentions "last month", use a date around ${lastMonthDate}
            - If the transcript mentions "yesterday", use ${yesterday}
            - If the user provides only hours and minutes, use the current date and the provided hours and minutes
            - If the user doesn't provide or mention a date, use the current date on UTC timezone
            
            Only include fields where information is explicitly provided in the transcript.
            Use exact category, severity, type, and rootCause values as specified.
            
            Transcript: "${transcript}"`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

/**
 * CAPA analysis prompts configuration
 */
export const getCapaPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd');

  // Example future dates for the prompt
  const nextWeekDate = format(addDays(currentDate, 7), 'yyyy-MM-dd');
  const nextMonthDate = format(addDays(currentDate, 30), 'yyyy-MM-dd');
  const tomorrow = format(addDays(currentDate, 1), 'yyyy-MM-dd');

  // System prompt for CAPA analysis
  const systemPrompt =
    'You are an AI assistant specialized in extracting detailed and accurate structured information about Corrective and Preventive Actions (CAPAs) from transcripts. ' +
    'You are also a certified expert in occupational safety and workplace compliance, with deep knowledge of OSHA standards, labor regulations, and risk mitigation strategies. ' +
    'You understand how to assess both immediate and systemic safety issues, and provide corrective and preventive actions that align with industry best practices. ' +
    'When generating suggested actions, you will think like a safety officer conducting a root cause analysis. Describe countermeasures as clearly, specifically, and thoroughly as possible — including both short-term fixes and long-term systemic improvements. ' +
    'Your task is to precisely identify key CAPA details while strictly adhering to the provided categorization systems. ' +
    'Extract only information that is explicitly mentioned in the transcript. Do not make assumptions or add information not directly stated. ' +
    'Ensure categorizations match the exact values specified in the schema. If uncertain about a field, omit it rather than guessing. ' +
    'Use the exact allowed values from the schema for: "type", "rootCauses", "status", "priority", "rcaMethod". ' +
    'Suggested mapping: ' +
    '- **High**: critical issues, injury risk, urgent language, only mark priority: high if there was actual harm, a safety shutdown, or critical system failure. ' +
    '- **Medium**: recurring problems or moderate safety impact, only mark priority: medium if there was a safety shutdown or critical system failure. ' +
    '- **Low**: audit findings, future tasks, documentation-only needs use low for near misses or other non-harmful events. ' +
    'For date fields, interpret relative time references (like "next week", "tomorrow", "in two days") correctly relative to the current date. ' +
    `Today's date is ${formattedCurrentDate} for reference. When you see "next week", it refers to a date like ${nextWeekDate}, not a date in the past. ` +
    'You need always to return a due date in the future. If the due date is not provided in the transcript, you need to calculate it based on the root cause and severity.' +
    'When returning the field rcaFindings, the format should depend on the selected rcaMethod: ' +
    '- For "5_whys": return a sequence of five "Why?" questions and answers that lead logically to the root cause. Those questions should be numbered and the answers should be in the same order as the questions. after a question and answer break the line.' +
    '- For "fishbone": summarize the findings in paragraph form, citing categories like People, Process, Equipment, Environment. Mention how each contributed. ' +
    '- For "fault_tree": describe a logic chain that led to the top event, referencing contributing events and conditions. ' +
    '- For "other" or "not_selected": provide a short paragraph summarizing the root cause in plain language. ' +
    'The aiSuggestedAction field should include a **numbered list** of clear corrective and preventive steps. ' +
    'If a method like fishbone is used, and a monitoring plan is mentioned (e.g., "track event rates for 3 months"), include that as the final step. ' +
    'The response should always be in english, despite the transcript being in another language.';

  // Function to generate the user prompt with the transcript
  const generateUserPrompt = (
    transcript: string,
    timezone?: string,
  ) => `Extract comprehensive details from this CAPA transcript. Return ONLY a JSON object with these precise keys:

    title (a clear, concise title summarizing the CAPA)
    summary (a concise summary of the CAPA, 1-2 sentences)
    type (must be one of: ${capaTypeEnum.enumValues.join(', ')})
    rcaMethod (must be one of: ${rcaMethodEnum.enumValues.join(', ')})
    rcaFindings (based on rcaMethod: for "5_whys" provide five "Why?" questions and answers that lead to the root cause; for fault_tree, return rcaFindings as a paragraph describing the top event and the contributing failure chains. Start the paragraph with: "Fault Tree Analysis of '...' identified...".
    rootCauses (if determined: ${RootCauseSchema.options.join(', ')}) its a array of root causes, not a single root cause, if there is only one root cause, return it as a string in the array
    otherRootCause (if rootCause is not selected, provide a short paragraph summarizing the root cause in plain language)
    dueDate (when CAPA should be completed, in ISO format if possible) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone
    tags (select tags that apply from ${capaTagsEnum.enumValues.join(', ')})
    priority (level of priority: ${capaPriorityEnum.enumValues.join(', ')})
    actionsToAddress (AI-generated action suggestions. Provide the response as a numbered list of clear, concise action steps)
    aiConfidenceScore (AI analysis confidence level) should be a number between 0 and 1 as percentage where 0.85 is 85%
    

    IMPORTANT DATE HANDLING INSTRUCTIONS:
    - Today's date is ${formattedCurrentDate}
    - If the transcript mentions "next week", use a date around ${nextWeekDate}
    - If the transcript mentions "next month", use a date around ${nextMonthDate}
    - If the transcript mentions "tomorrow", use ${tomorrow}
    - All due dates should be in the future, not the past
    - For relative time references, calculate the proper future date based on today
    - Format all dates as ISO strings (YYYY-MM-DDTHH:MM:SS.SSSZ)
    
    Only include fields where information is explicitly provided in the transcript.
    Use exact type, rootCauses, severity, status, and priority values as specified.
    
    Transcript: "${transcript}"`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getCapaSummaryPrompt = (capa: CreateCapasForm) => {
  const systemPrompt =
    'You are an AI assistant specialized in creating a summary for a CAPA. The summary should be a concise summary of the CAPA, 1-2 sentences.';

  const generateUserPrompt = `
    You are an assistant creating brief CAPA summaries.
    
    Example:
    CAPA:
    {
      "title": "Hydraulic Leak on Pump 12",
      "rcaFindings": "Seal failure due to lack of inspection",
      "actionsToAddress": "Replace seal, update inspection schedule"
    }
    Return: { "summary": "Pump 12 experienced a seal failure due to missed inspections. The maintenance schedule has been updated and the seal replaced." }
    
    Now summarize this CAPA:
    ${JSON.stringify(capa, null, 2)}
    
    Return ONLY: { "summary": "..." }
    `;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getLocationMatchPrompt = (location: string, locations: Location[]) => {
  const systemPrompt = `
You are a smart assistant that helps match user-submitted location name to a Candidate list of location names.

On locations, you must try to match the location value with one of the known Candidate names with a high confidence. 
When you find a good match, return the locationId and locationName.
**If theres more than one candidate, return on reason ${MORE_THAN_ONE_LOCATION_MATCHED}.** 
**If no candidate is a good match, return on reason ${NO_LOCATION_MATCHED}.**

You must not guess, and you must not assume the best match is the first one listed. Evaluate all candidates carefully before making a decision.

Respond strictly in this JSON format:
{
  "locationId": string,
  "locationName": string,
  "reason": string // ${NO_LOCATION_MATCHED} or ${MORE_THAN_ONE_LOCATION_MATCHED}
}
`;

  const userPrompt = `
Input (string):
${location}

Candidates (array of objects):
${JSON.stringify(locations, null, 2)}
`;

  return {
    systemPrompt,
    userPrompt,
  };
};

export const getJhaPrompts = async ({
  transcript,
  user,
  document = false,
}: {
  transcript?: string;
  user: User;
  document?: boolean;
}) => {
  const systemPrompt = `
  You are an expert safety analyst. 
  Your job is to produce a complete, high-quality Job Hazard Analysis (JHA) from either:
  - A short, informal transcript/description (e.g., "can you create a JHA to build a chicken coop")
  - A document that contains a detailed JHA description

  INPUT MODES AND EXPECTATIONS:
  - If a document is provided: Analyze the provided document and extract a comprehensive JHA strictly from the document. Use the exact wording for entity names where available; include ALL hazards and ALL control measures mentioned.
  - If only a transcript/description is provided: Analyze the transcript/description and extract a comprehensive JHA. When the transcript is brief or lacks detail, proactively infer the complete set of steps, hazards, and control measures required for a realistic and practical JHA.

  ${
    document
      ? ''
      : `TRANSCRIPT-ONLY REQUIREMENT: When information is missing or not explicit, you MUST proactively infer and SUGGEST realistic hazards and control measures based on the described tasks, equipment, environment, and industry best practices. Your goal is to produce a complete, practical JHA even if the transcript does not list all hazards/controls verbatim. Ensure the step list fully covers the job from start to finish.`
  }

  1. Identify the main work activity or job described
  2. Break down the work process into sequential task steps (typically 3-8 steps)
  3. For each task step, provide:
    - A brief step title (what is being done)
    - A detailed step description (how it's done, context, specifics)
    - All potential hazards (be specific and comprehensive)
    - All control measures/safety precautions needed
    - Risk severity (1-5 scale, where 5 is most severe)
    - Likelihood of occurrence (1-5 scale, where 5 is most likely)

  IMPORTANT: Be very detailed and comprehensive. Each task step should have:
  - A clear, specific step description that explains what needs to be done
  - Multiple hazards if applicable (don't just list one)
  - Multiple control measures for comprehensive safety coverage
  - Do NOT cap hazards or control measures at two. Include all that apply
  - When the source contains bullet points, commas, or semicolons describing controls or hazards, map EACH item to a distinct entity and include ALL of them in the step
  - Realistic risk assessments based on the actual work being performed

  Focus on:
  - Workplace safety hazards (falls, electrical, chemical, mechanical, etc.)
  - Equipment and environmental conditions
  - Personal protective equipment requirements
  - Safe work procedures and best practices
  - Industry-specific safety considerations

  CRITICAL: You MUST include ownerId and approverId in the JHA object. Use the provided user ID for both fields.
  User ID to use: ${user.id}
  
  IMPORTANT: Complete the ENTIRE JHA creation process in this single response. Do not stop at analysis or planning - execute all tool calls and provide the final JSON immediately.

  OUTPUT SCHEMA (STRICT):
  - Return ONLY a JSON object with two top-level keys: "jha" and "steps".
  - jha: must include at least: title (string), ownerId (string), approverId (string). Optional when unknown: locationId, reviewDate, workOrderId, instanceId.
  - steps: an array where each step includes: serial (1-based, increasing), title (string), severity (1-5), likelihood (1-5), hazardIds (empty array), controlMeasureIds (empty array), hazardsToCreate (array of { name, type }), controlMeasuresToCreate (array of { name, type }).
  - Types for hazards/control measures must use these exact enums: hazards (${HazardCategorySchema.options.join(', ')}); control measures (${ControlMeasureCategorySchema.options.join(', ')}).
  - Language: Always return names and text in English.

  CRITICAL STEP REQUIREMENTS - YOU MUST FOLLOW THESE RULES:
  1. **EVERY STEP MUST INCLUDE ALL HAZARDS AND CONTROL MEASURES DESCRIBED IN THAT STEP**
     - Do NOT skip or omit any hazards/control measures mentioned in the source material
     - If a step describes multiple hazards, ALL must be included
     - If a step describes multiple control measures, ALL must be included
     - Use the EXACT names from the source material when they are provided. When names are not provided (common in transcript-only cases), suggest clear, specific hazard and control names using standard safety terminology.

  2. **CREATING ALL ENTITIES FROM SOURCE:**
      - For EVERY hazard identified (explicitly mentioned or reasonably suggested), add it to the hazardsToCreate array with a precise name and appropriate type
      - For EVERY control measure identified (explicitly mentioned or reasonably suggested), add it to the controlMeasuresToCreate array with a precise name and appropriate type
      - If the source provides exact names, include them verbatim. If not, craft concise, professional names that reflect best-practice safety terminology.
     - Choose appropriate type: hazards (${HazardCategorySchema.options.join(', ')}) or control measures (${ControlMeasureCategorySchema.options.join(', ')})
      - NEVER leave out entities - create ALL of them (mentioned or suggested)

  3. **ARRAY USAGE:**
     - Leave hazardIds and controlMeasureIds arrays EMPTY (the bulk create process will populate these)
     - Put ALL hazards in hazardsToCreate arrays with name and type
     - Put ALL control measures in controlMeasuresToCreate arrays with name and type

  4. **FORBIDDEN ACTIONS:**
     - Do NOT use hazardIds or controlMeasureIds arrays - leave them empty
      - Do NOT skip entities mentioned in the source material
      - Do NOT invent irrelevant or non-credible entities. Keep suggestions realistic and directly related to the job and context. If the source provides exact names, do not paraphrase them.
      - Do NOT output anything other than the JSON object. No explanations, no headings, no markdown.
  
  Always return the data in english, despite the source being in another language.
  `;

  const generateUserPrompt = `
  Analyze the input and create a detailed JHA with the correct JSON. The input may be either a short transcript/description or a full document:

  MANDATORY WORKFLOW - FOLLOW EVERY STEP:
  
  **STEP 1: COMPREHENSIVE EXTRACTION**
  - Read through ALL content and identify EVERY hazard and control measure mentioned
  - For each JHA step, extract ALL hazards and ALL control measures described
  - When using transcript-only input, ALSO SUGGEST additional relevant hazards and control measures that are implied by the job, equipment, and environment but not explicitly stated
  - Do NOT skip any - if the source mentions 5 hazards for a step, include all 5
  - Do NOT skip any - if the source mentions 8 control measures for a step, include all 8

  **STEP 2: CREATE ALL ENTITIES**
  - Add ALL hazards identified (explicitly stated OR suggested) to hazardsToCreate arrays with precise names and appropriate types
  - Add ALL control measures identified (explicitly stated OR suggested) to controlMeasuresToCreate arrays with precise names and appropriate types
  - If the source provides exact names, use them verbatim; otherwise, create concise, specific names using standard safety terminology
  - Select appropriate type: hazards (${HazardCategorySchema.options.join(', ')}) or control measures (${ControlMeasureCategorySchema.options.join(', ')})

  **STEP 3: ARRAY SETUP**
  - Leave hazardIds and controlMeasureIds arrays EMPTY
  - The bulk create process will handle duplicates and populate the ID arrays
  - Include "serial" field for step order
  - Set ownerId and approverId to: "${user.id}"

  **CRITICAL RULES:**
  - NEVER skip entities mentioned in the source
  - Use exact names from source material when provided; otherwise propose clear, professional names
  - ALWAYS put entities in hazardsToCreate/controlMeasuresToCreate arrays
  - NEVER use hazardIds/controlMeasureIds arrays - leave them empty
  - If source has bullet points, create separate entities for each bullet
  - If source lists multiple items with commas/semicolons, create separate entities for each
  - Always return data in English
  ${document ? '- **MANDATORY: Use exact text from document for entity names**' : '- **MANDATORY (TRANSCRIPT-ONLY): When hazards or control measures are not explicitly stated, propose them based on best practices and the job context. If the transcript provides specific names, include them verbatim.**'}

  **EXAMPLE OF CORRECT BEHAVIOR (SAMPLE ONLY - NOT REAL DATA):**
  
  SAMPLE Source text: "Step 1: Site preparation involves clearing debris, checking ground stability, and setting up barriers. Hazards include falls from height, struck by falling objects, and ground collapse. Control measures: use safety harnesses, wear hard hats, conduct soil tests, and install warning signs."
  
  SAMPLE Correct output for this step:
  - Extract ALL 3 hazards and add to hazardsToCreate: "falls from height", "struck by falling objects", "ground collapse"
  - Extract ALL 4 control measures and add to controlMeasuresToCreate: "use safety harnesses", "wear hard hats", "conduct soil tests", "install warning signs"  
  - Leave hazardIds and controlMeasureIds arrays empty
  - Use exact names from source with appropriate types
  
  THIS IS JUST AN EXAMPLE - ANALYZE THE ACTUAL SOURCE MATERIAL PROVIDED BELOW:

  ${document ? `Use the attached document as the source of the JHA` : `Transcript: "${transcript}"`}
  
  CRITICAL FINAL RESPONSE REQUIREMENTS:
  - Return ONLY the JSON object matching the described schema (top-level keys: jha, steps)
  - You MUST include ALL hazards and control measures mentioned for each step
  - Use exact names from source material
  - Do not ask for confirmation or indicate you will do something "in the next step"
  - No additional commentary or markdown
  `;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};
