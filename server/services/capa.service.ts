import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, auditTrailActionEnum, capas, events, files, statusEnum } from '@shared/schema';
import {
  IdSchema,
  CreateCapasForm,
  EditCapasForm,
  ListCapasSchema,
  TransientFileSchema,
  User,
  ExportCapasSchema,
} from '@shared/schema.types';
import { USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { endOfDay, startOfDay } from 'date-fns';
import {
  and,
  arrayContains,
  arrayOverlaps,
  asc,
  desc,
  eq,
  getTableColumns,
  gte,
  ilike,
  inArray,
  isNull,
  lt,
  lte,
  or,
  sql,
} from 'drizzle-orm';
import { db } from 'server/db';
import { z } from 'zod';
import { createCapaSummary } from './ai.service';

/**
 * Create a new CAPA (Corrective and Preventive Action)
 *
 * @param capaInput The CAPA data to create
 * @param user The authenticated user
 * @returns The created CAPA
 */
export const createCapa = async (input: CreateCapasForm, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      createdBy: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      status: input.status || statusEnum.enumValues[0], // Default to the first status (open)
    };

    if (!values.summary) {
      const summary = await createCapaSummary(values);
      values.summary = summary;
    }

    const inserted = await tx.insert(capas).values(values).returning({
      id: capas.id,
      createdBy: capas.createdBy,
      upkeepCompanyId: capas.upkeepCompanyId,
      title: capas.title,
      type: capas.type,
      priority: capas.priority,
      status: capas.status,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      eventId: capas.eventId,
      rcaFindings: capas.rcaFindings,
      slug: capas.slug,
      summary: capas.summary,
      locationId: capas.locationId,
      teamMembersToNotify: capas.teamMembersToNotify,
    });

    const capa = inserted.at(0);

    if (!capa) {
      return;
    }

    // Create audit trail entry
    await tx.insert(auditTrail).values({
      entityId: capa.id,
      entityType: 'capa',
      details: JSON.stringify(capa),
      userId: capa.createdBy,
      upkeepCompanyId: capa.upkeepCompanyId,
      timestamp: new Date(),
      action: auditTrailActionEnum.enumValues[0], // Default to the first status (created)
    });

    return capa;
  });
};

export const getCapaById = async (id: string, user: User, needPartialCheck: boolean) => {
  // Fetch the CAPA that matches the ID and company ID, and is not deleted

  const isAdmin = user.role === USER_ACCOUNT_TYPES.ADMIN;

  const result = await db
    .select({
      ...getTableColumns(capas),
      eventSlug: events.slug,
      attachments: sql<z.infer<typeof TransientFileSchema>[]>`
        CASE
          WHEN COUNT(${files.id}) = 0 THEN NULL
          ELSE json_agg(
            json_build_object(
              'id', ${files.id},
              'name', ${files.fileName},
              'url', ${files.presignedUrl},
              'type', ${files.mimeType},
              'size', ${files.fileSize}
            )
          )
        END
      `,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .leftJoin(
      files,
      and(
        eq(files.entityId, capas.id),
        eq(files.upkeepCompanyId, user.upkeepCompanyId),
        eq(files.status, 'completed'),
        eq(files.entityType, 'capa'),
      ),
    )
    .where(
      and(
        eq(capas.id, id),
        eq(capas.upkeepCompanyId, user.upkeepCompanyId),
        isNull(capas.deletedAt),
        !isAdmin ? eq(capas.privateToAdmins, false) : undefined,
        ...(needPartialCheck
          ? [
              or(
                eq(capas.ownerId, user.id),
                eq(capas.implementedBy, user.id),
                eq(capas.voePerformedBy, user.id),
                eq(capas.createdBy, user.id),
                arrayContains(capas.teamMembersToNotify, [user.id]),
              ),
            ]
          : []),
      ),
    )
    .groupBy(capas.id, events.slug);

  const capa = result.at(0);

  if (!capa) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'CAPA not found',
    });
  }

  return capa;
};

export const getOverdueCapas = async () => {
  const result = await db
    .select({
      ...getTableColumns(capas),
      eventSlug: events.slug,
      eventTitle: events.title,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .where(and(eq(capas.status, statusEnum.enumValues[0]), lt(capas.dueDate, new Date())));

  return result;
};

export const listCapas = async (
  input: z.infer<typeof ListCapasSchema> & { cursor?: number },
  user: User,
  needPartialCheck: boolean,
) => {
  const {
    cursor = 0,
    limit = 10,
    includeArchived,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    status = [],
    type = [],
    priority = [],
    owner = [],
    dueDateRange,
    tags = [],
    eventId,
  } = input;

  const isAdmin = user.role === USER_ACCOUNT_TYPES.ADMIN;

  // Apply all conditions at once using and()
  let query = db
    .select({
      id: capas.id,
      title: capas.title,
      type: capas.type,
      status: capas.status,
      priority: capas.priority,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      eventId: capas.eventId,
      eventSlug: events.slug,
      archived: capas.archived,
      slug: capas.slug,
      tags: capas.tags,
      createdAt: capas.createdAt,
      updatedAt: capas.updatedAt,
      deletedAt: capas.deletedAt,
      createdBy: capas.createdBy,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .where(
      and(
        eq(capas.upkeepCompanyId, user.upkeepCompanyId),
        !isAdmin ? eq(capas.privateToAdmins, false) : undefined,
        needPartialCheck
          ? or(
              eq(capas.ownerId, user.id),
              eq(capas.implementedBy, user.id),
              eq(capas.voePerformedBy, user.id),
              eq(capas.createdBy, user.id),
              arrayContains(capas.teamMembersToNotify, [user.id]),
            )
          : undefined,
        !includeArchived ? eq(capas.archived, false) : undefined,
        search
          ? or(
              ilike(capas.title, `%${search}%`),
              ilike(capas.rcaFindings, `%${search}%`),
              ilike(capas.slug, `%${search}%`),
            )
          : undefined,
        status.length > 0 ? inArray(capas.status, status) : undefined,
        type.length > 0 ? inArray(capas.type, type) : undefined,
        priority.length > 0 ? inArray(capas.priority, priority) : undefined,
        owner.length > 0 ? inArray(capas.ownerId, owner) : undefined,
        dueDateRange?.from ? gte(capas.dueDate, startOfDay(dueDateRange.from)) : undefined,
        dueDateRange?.to ? lte(capas.dueDate, endOfDay(dueDateRange.to)) : undefined,
        tags.length > 0 ? arrayOverlaps(capas.tags, tags) : undefined,
        eventId ? eq(capas.eventId, eventId) : undefined,
      ),
    )
    .$dynamic();

  if (sortBy) {
    query = query.orderBy(sortOrder === 'desc' ? desc(capas.createdAt) : asc(capas.createdAt));
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const exportCapas = async (input: z.infer<typeof ExportCapasSchema>, user: User) => {
  const {
    search,
    status = [],
    type = [],
    priority = [],
    owner = [],
    dueDateRange,
    tags = [],
    eventId,
    includeArchived,
  } = input;

  return await db
    .select({
      id: capas.id,
      title: capas.title,
      type: capas.type,
      status: capas.status,
      priority: capas.priority,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      eventId: capas.eventId,
      eventSlug: events.slug,
      archived: capas.archived,
      slug: capas.slug,
      tags: capas.tags,
      summary: capas.summary,
      rcaMethod: capas.rcaMethod,
      actionsToAddress: capas.actionsToAddress,
      createdAt: capas.createdAt,
      updatedAt: capas.updatedAt,
      deletedAt: capas.deletedAt,
      createdBy: capas.createdBy,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .where(
      and(
        eq(capas.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? eq(capas.archived, false) : undefined,
        search
          ? or(
              ilike(capas.title, `%${search}%`),
              ilike(capas.rcaFindings, `%${search}%`),
              ilike(capas.slug, `%${search}%`),
            )
          : undefined,
        status.length > 0 ? inArray(capas.status, status) : undefined,
        type.length > 0 ? inArray(capas.type, type) : undefined,
        priority.length > 0 ? inArray(capas.priority, priority) : undefined,
        owner.length > 0 ? inArray(capas.ownerId, owner) : undefined,
        dueDateRange?.from ? gte(capas.dueDate, startOfDay(dueDateRange.from)) : undefined,
        dueDateRange?.to ? lte(capas.dueDate, endOfDay(dueDateRange.to)) : undefined,
        tags.length > 0 ? arrayOverlaps(capas.tags, tags) : undefined,
        eventId ? eq(capas.eventId, eventId) : undefined,
      ),
    )
    .limit(500);
};

/**
 * Update an existing CAPA
 *
 * @param id The ID of the CAPA to update
 * @param capaInput The CAPA data to update
 * @param user The authenticated user
 * @returns The updated CAPA data
 */
export const updateCapa = async (id: string, capaInput: EditCapasForm, user: User) => {
  return db.transaction(async (tx) => {
    const updated = await tx
      .update(capas)
      .set(capaInput)
      .where(and(eq(capas.id, id), eq(capas.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        status: capas.status,
        archived: capas.archived,
        id: capas.id,
        createdBy: capas.createdBy,
        upkeepCompanyId: capas.upkeepCompanyId,
        title: capas.title,
        type: capas.type,
        priority: capas.priority,
        dueDate: capas.dueDate,
        ownerId: capas.ownerId,
        eventId: capas.eventId,
        rcaFindings: capas.rcaFindings,
        slug: capas.slug,
        locationId: capas.locationId,
        teamMembersToNotify: capas.teamMembersToNotify,
      });

    const updatedCapa = updated.at(0);

    if (!updatedCapa) {
      return;
    }

    let action: (typeof auditTrailActionEnum.enumValues)[number] = 'updated';

    if ('status' in capaInput && 'status' in updatedCapa) {
      const statusMap = {
        open: auditTrailActionEnum.enumValues[7],
        in_review: auditTrailActionEnum.enumValues[6],
        closed: auditTrailActionEnum.enumValues[5],
      };

      action = statusMap[
        updatedCapa.status as keyof typeof statusMap
      ] as (typeof auditTrailActionEnum.enumValues)[number];
    }

    if ('archived' in capaInput && 'archived' in updatedCapa) {
      action = updatedCapa.archived ? 'archived' : 'unarchived';
    }

    await tx.insert(auditTrail).values({
      entityId: updatedCapa.id,
      entityType: 'capa',
      details: JSON.stringify(updatedCapa),
      userId: updatedCapa.createdBy,
      upkeepCompanyId: updatedCapa.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return updatedCapa;
  });
};

export const toggleArchiveCapa = async (input: z.infer<typeof IdSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const updated = await tx
      .update(capas)
      .set({
        updatedAt: new Date(),
        archived: sql`CASE WHEN ${capas.archived} IS TRUE THEN FALSE ELSE TRUE END`,
      })
      .where(eq(capas.id, input.id))
      .returning({
        status: capas.status,
        archived: capas.archived,
        id: capas.id,
        createdBy: capas.createdBy,
        upkeepCompanyId: capas.upkeepCompanyId,
        title: capas.title,
        type: capas.type,
        priority: capas.priority,
        dueDate: capas.dueDate,
        ownerId: capas.ownerId,
        eventId: capas.eventId,
        rcaFindings: capas.rcaFindings,
        slug: capas.slug,
        locationId: capas.locationId,
        teamMembersToNotify: capas.teamMembersToNotify,
      });

    const capa = updated.at(0);

    if (!capa) {
      return;
    }

    const action = capa.archived ? 'unarchived' : 'archived';
    await tx.insert(auditTrail).values({
      entityId: capa.id,
      entityType: 'capa',
      details: JSON.stringify(capa),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return capa;
  });
};
