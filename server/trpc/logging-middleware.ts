import { logger } from '@server/utils/logger';
import type { AnyMiddlewareFunction } from '@trpc/server';
import type { EnhancedContext } from './trpc';

// Factory function to create logging middleware
export const createLoggingMiddleware = (trpc: { middleware: (fn: AnyMiddlewareFunction) => unknown }) => {
  return trpc.middleware(async ({ path, type, next, ctx }) => {
    const start = Date.now();

    const result = await next();

    const { env } = await import('../../env');

    // we only use development as local development logs are too verbose
    // all upper environments use production
    if (env.NODE_ENV === 'development') {
      return result;
    }

    logger.info('tRPC call started', {
      path,
      type,
      userId: (ctx as EnhancedContext).user?.id,
      upkeepCompanyId: (ctx as EnhancedContext).user?.upkeepCompanyId,
    });

    const durationMs = Date.now() - start;

    if ((result as { ok: boolean }).ok) {
      logger.info('tRPC call completed', {
        path,
        type,
        durationMs,
        userId: (ctx as EnhancedContext).user?.id,
        upkeepCompanyId: (ctx as EnhancedContext).user?.upkeepCompanyId,
      });
    } else {
      // Log errors with full context
      const errorResult = result as { error?: { code?: string; message?: string; cause?: unknown; stack?: string } };
      logger.error('tRPC call failed', {
        path,
        type,
        durationMs,
        userId: (ctx as EnhancedContext).user?.id,
        upkeepCompanyId: (ctx as EnhancedContext).user?.upkeepCompanyId,
        error: {
          code: errorResult.error?.code,
          message: errorResult.error?.message,
          cause: errorResult.error?.cause,
          stack: errorResult.error?.stack,
        },
      });
    }

    return result;
  });
};
