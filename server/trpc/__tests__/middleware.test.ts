import { describe, it, expect, vi, beforeEach } from 'vitest';
import { IncomingMessage } from 'http';
import { TRPCError } from '@trpc/server';
import { createMockUser } from '@server/trpc/router/__tests__/test-utils';

// Mock user service using the exact path from middleware
const mockGetCurrentUser = vi.fn();
vi.mock('@server/services/user.service', () => ({
  getCurrentUser: mockGetCurrentUser,
}));

// Mock the logger using the exact path from middleware
const mockLogger = {
  error: vi.fn(),
  warn: vi.fn(),
};
vi.mock('server/utils/logger', () => ({
  logger: mockLogger,
}));

// Mock @trpc-limiter/memory
const mockCreateTRPCStoreLimiter = vi.fn();
const mockDefaultFingerPrint = vi.fn((req) => `fingerprint-${req.socket?.remoteAddress || 'unknown'}`);
vi.mock('@trpc-limiter/memory', () => ({
  createTRPCStoreLimiter: mockCreateTRPCStoreLimiter,
  defaultFingerPrint: mockDefaultFingerPrint,
}));

// Mock environment variables
vi.mock('../../env', () => ({
  env: {
    RATE_LIMIT_WINDOW_MS: 60000,
    RATE_LIMIT_MAX_REQUESTS: 5,
  },
}));

describe('TRPC Middleware', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('addUserContext', () => {
    it('should enrich context with user data', async () => {
      const { addUserContext } = await import('../middleware');

      const mockUser = createMockUser();
      mockGetCurrentUser.mockResolvedValue(mockUser);

      const mockReq = { headers: {} } as IncomingMessage;
      const result = await addUserContext(mockReq);

      expect(mockGetCurrentUser).toHaveBeenCalledWith(mockReq);
      expect(result).toEqual({ user: mockUser });
    });

    it('should handle null user data', async () => {
      const { addUserContext } = await import('../middleware');

      mockGetCurrentUser.mockResolvedValue(null);

      const mockReq = { headers: {} } as IncomingMessage;
      const result = await addUserContext(mockReq);

      expect(mockGetCurrentUser).toHaveBeenCalledWith(mockReq);
      expect(result).toEqual({ user: null });
    });

    it('should handle getCurrentUser errors', async () => {
      const { addUserContext } = await import('../middleware');

      mockGetCurrentUser.mockRejectedValue(new Error('Auth failed'));

      const mockReq = { headers: {} } as IncomingMessage;
      const result = await addUserContext(mockReq);

      expect(mockGetCurrentUser).toHaveBeenCalledWith(mockReq);
      expect(result).toEqual({ user: null });
    });
  });

  describe('createRateLimiter', () => {
    it('should create a rate limiter with correct configuration', async () => {
      const { createRateLimiter } = await import('../middleware');

      createRateLimiter();

      expect(mockCreateTRPCStoreLimiter).toHaveBeenCalledWith({
        fingerprint: expect.any(Function),
        windowMs: 60000,
        max: 5,
        message: expect.any(Function),
        onLimit: expect.any(Function),
      });
    });

    it('should generate correct rate limit message', async () => {
      const { createRateLimiter } = await import('../middleware');

      createRateLimiter();

      const callArgs = mockCreateTRPCStoreLimiter.mock.calls[0]?.[0];
      const messageFunction = callArgs?.message;

      if (typeof messageFunction === 'function') {
        const result = messageFunction(30000, {} as any, 'test-fingerprint'); // 30 seconds
        expect(result).toBe('Too many requests from this IP. Please try again in 30 seconds.');
      } else {
        expect.fail('Message function should be callable');
      }
    });

    it('should handle onLimit callback correctly', async () => {
      const { createRateLimiter } = await import('../middleware');

      createRateLimiter();

      const callArgs = mockCreateTRPCStoreLimiter.mock.calls[0]?.[0];
      const onLimitFunction = callArgs?.onLimit;

      const mockCtx = {
        req: {
          headers: { 'x-forwarded-for': '***********' },
          url: '/api/test',
          socket: { remoteAddress: '***********' },
        },
      };

      if (typeof onLimitFunction === 'function') {
        // The function should log first, then throw
        try {
          onLimitFunction(15000, mockCtx, 'test-fingerprint');
          expect.fail('Should have thrown TRPCError');
        } catch (error) {
          expect(error).toBeInstanceOf(TRPCError);
        }

        // Check that the logger was called before the error was thrown
        expect(mockLogger.warn).toHaveBeenCalledWith('Rate limit exceeded for public endpoint', {
          fingerprint: 'test-fingerprint',
          retryAfter: 15000,
          windowMs: 60000,
          maxRequests: 5,
          ip: '***********',
          path: '/api/test',
        });
      } else {
        expect.fail('onLimit function should be callable');
      }
    });

    it('should throw TRPCError with correct code when rate limit exceeded', async () => {
      const { createRateLimiter } = await import('../middleware');

      createRateLimiter();

      const callArgs = mockCreateTRPCStoreLimiter.mock.calls[0]?.[0];
      const onLimitFunction = callArgs?.onLimit;

      const mockCtx = {
        req: {
          headers: {},
          url: '/api/test',
          socket: { remoteAddress: '***********' },
        },
      };

      if (typeof onLimitFunction === 'function') {
        try {
          onLimitFunction(10000, mockCtx, 'test-fingerprint');
          expect.fail('Should have thrown TRPCError');
        } catch (error) {
          expect(error).toBeInstanceOf(TRPCError);
          expect((error as TRPCError).code).toBe('TOO_MANY_REQUESTS');
          expect((error as TRPCError).message).toBe('Too many requests. Please try again in 10 seconds.');
        }
      } else {
        expect.fail('onLimit function should be callable');
      }
    });

    it('should use defaultFingerPrint for fingerprinting', async () => {
      const { createRateLimiter } = await import('../middleware');

      createRateLimiter();

      const callArgs = mockCreateTRPCStoreLimiter.mock.calls[0]?.[0];
      const fingerprintFunction = callArgs?.fingerprint;

      const mockCtx = {
        req: {
          socket: { remoteAddress: '***********' },
        },
      };

      if (typeof fingerprintFunction === 'function') {
        const result = fingerprintFunction(mockCtx, 'additional-param' as any);

        expect(mockDefaultFingerPrint).toHaveBeenCalledWith(mockCtx.req);
        expect(result).toBe('fingerprint-***********');
      } else {
        expect.fail('Fingerprint function should be callable');
      }
    });
  });
});
