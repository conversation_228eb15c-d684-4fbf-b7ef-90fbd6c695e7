import { describe, it, expect, vi, beforeEach, Mock } from 'vitest';

// Mock logger
vi.mock('@server/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

describe('createLoggingMiddleware', () => {
  const mockMiddleware = vi.fn((fn) => fn);
  const mockTrpc = {
    middleware: mockMiddleware,
  };

  let mockLoggerInfo: ReturnType<typeof vi.fn>;
  let mockLoggerError: ReturnType<typeof vi.fn>;
  let createLoggingMiddleware: Function;

  const createContext = (user: { id: string; upkeepCompanyId: string } | null = null) => ({
    req: {},
    user,
    permissionLevel: 'none' as const,
    needPartialCheck: false,
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  describe('Basic functionality', () => {
    beforeEach(async () => {
      vi.resetModules();
      process.env.NODE_ENV = 'development';

      const middleware = await import('../logging-middleware');
      createLoggingMiddleware = middleware.createLoggingMiddleware;
    });

    it('should create middleware using the tRPC instance', () => {
      createLoggingMiddleware(mockTrpc);
      expect(mockTrpc.middleware).toHaveBeenCalledOnce();
      expect(mockTrpc.middleware).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('Development environment', () => {
    beforeEach(async () => {
      vi.resetModules();
      process.env.NODE_ENV = 'development';

      const middleware = await import('../logging-middleware');
      createLoggingMiddleware = middleware.createLoggingMiddleware;

      const { logger } = await import('@server/utils/logger');
      mockLoggerInfo = logger.info as ReturnType<typeof vi.fn>;
      mockLoggerError = logger.error as ReturnType<typeof vi.fn>;

      vi.spyOn(Date, 'now').mockReturnValueOnce(1000).mockReturnValueOnce(1500);
    });

    it('should skip logging in development mode', async () => {
      const loggingMiddleware = createLoggingMiddleware(mockTrpc) as (opts: any) => Promise<any>;
      const mockNext = vi.fn().mockResolvedValue({ ok: true, data: 'test' });

      const result = await loggingMiddleware({
        path: 'test.dev',
        type: 'query',
        next: mockNext,
        ctx: createContext(),
      });

      expect(mockLoggerInfo).not.toHaveBeenCalled();
      expect(mockLoggerError).not.toHaveBeenCalled();
      expect(result).toEqual({ ok: true, data: 'test' });
    });
  });

  describe('Production environment', () => {
    beforeEach(async () => {
      vi.resetModules();
      vi.doMock('../../env', () => ({
        env: { NODE_ENV: 'production' },
      }));

      const middleware = await import('../logging-middleware');
      createLoggingMiddleware = middleware.createLoggingMiddleware;

      const { logger } = await import('@server/utils/logger');
      mockLoggerInfo = logger.info as ReturnType<typeof vi.fn>;
      mockLoggerError = logger.error as ReturnType<typeof vi.fn>;

      vi.spyOn(Date, 'now').mockReturnValueOnce(1000).mockReturnValueOnce(1500);
    });

    it('should log successful completion with timing', async () => {
      const loggingMiddleware = createLoggingMiddleware(mockTrpc) as (opts: any) => Promise<any>;
      const mockNext = vi.fn().mockResolvedValue({ ok: true });
      const ctx = createContext({ id: 'user123', upkeepCompanyId: 'company456' });

      process.env.NODE_ENV = 'production';

      await loggingMiddleware({
        path: 'test.procedure',
        type: 'query',
        next: mockNext,
        ctx,
      });

      expect(mockLoggerInfo).toHaveBeenCalledTimes(2);
      expect(mockLoggerInfo).toHaveBeenNthCalledWith(1, 'tRPC call started', {
        path: 'test.procedure',
        type: 'query',
        userId: 'user123',
        upkeepCompanyId: 'company456',
      });

      expect(mockLoggerInfo).toHaveBeenNthCalledWith(2, 'tRPC call completed', {
        path: 'test.procedure',
        type: 'query',
        durationMs: expect.any(Number),
        userId: 'user123',
        upkeepCompanyId: 'company456',
      });
    });
  });
});
