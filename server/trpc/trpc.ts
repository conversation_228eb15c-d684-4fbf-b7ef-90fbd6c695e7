import { hasPermission } from '@server/services/user.service';
import { createLoggingMiddleware } from '@server/trpc/logging-middleware';
import { addUserContext, createRateLimiter } from '@server/trpc/middleware';
import {
  AllowedActions,
  Modules,
  PERMISSION_LEVELS,
  PermissionLevel,
  USER_ACCOUNT_TYPES,
} from '@shared/user-permissions';
import { initTRPC, TRPCError } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import superjson from 'superjson';
import { ZodError } from 'zod';

const ERROR_MESSAGES = {
  unauthorized: 'You must be logged in to access this resource',
  forbidden: (action: AllowedActions, module: Modules) => `Insufficient permissions for ${action} on ${module}`,
  adminOnly: 'Admin access required',
};

// Create TRPC context
export const createTrpcContext = async ({ req }: CreateExpressContextOptions) => ({
  req,
  ...(await addUserContext(req)),
});

// Extended context with permission details
export type EnhancedContext = Awaited<ReturnType<typeof createTrpcContext>> & {
  permissionLevel?: PermissionLevel;
  needPartialCheck?: boolean;
};

// Initialize TRPC with custom transformer and error handler
export const trpc = initTRPC.context<EnhancedContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

// Create logging middleware instance
const loggingMiddleware = createLoggingMiddleware(trpc) as ReturnType<typeof trpc.middleware>;

// Middleware to enforce logged-in users
const requireUser = trpc.middleware(({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED', message: ERROR_MESSAGES.unauthorized });
  }

  return next({
    ctx: { ...ctx, user: ctx.user },
  });
});

// Middleware for permission checks
const withPermission = (module: Modules, action: AllowedActions) =>
  trpc.middleware(({ ctx, next }) => {
    const user = ctx.user;
    if (!user) throw new TRPCError({ code: 'UNAUTHORIZED', message: ERROR_MESSAGES.unauthorized });

    const allowed = hasPermission(user, module, action, false);
    if (!allowed) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: ERROR_MESSAGES.forbidden(action, module),
      });
    }

    const level = user.permissions[module]?.[action] ?? PERMISSION_LEVELS.NONE;
    return next({
      ctx: {
        ...ctx,
        permissionLevel: level,
        needPartialCheck: level === PERMISSION_LEVELS.PARTIAL,
      },
    });
  });

// Middleware for admin-only routes
const adminOnlyMiddleware = trpc.middleware(({ ctx, next }) => {
  if (!ctx.user || ctx.user.role !== USER_ACCOUNT_TYPES.ADMIN) {
    throw new TRPCError({ code: 'FORBIDDEN', message: ERROR_MESSAGES.adminOnly });
  }
  return next();
});

// Base procedure with logging
const baseProcedure = trpc.procedure.use(loggingMiddleware);

// Base procedure requiring authentication
const basePrivateProcedure = baseProcedure.use(requireUser);

// Extend with permission helpers
type LoggedProcedure = typeof basePrivateProcedure & {
  hasPermission: (module: Modules, action: AllowedActions) => typeof basePrivateProcedure;
  adminOnly: () => typeof basePrivateProcedure;
};

const createPrivateProcedure = (): LoggedProcedure => {
  const procedure = basePrivateProcedure as LoggedProcedure;

  procedure.hasPermission = (module, action) => basePrivateProcedure.use(withPermission(module, action));

  procedure.adminOnly = () => basePrivateProcedure.use(adminOnlyMiddleware);

  return procedure;
};

// Exported procedures
export const privateProcedure = createPrivateProcedure();
export const publicProcedure = baseProcedure.use(createRateLimiter());
export type TRPCServerType = typeof trpc;
