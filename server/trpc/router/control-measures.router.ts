import { ControlMeasuresCreateSchema, BulkControlMeasuresCreateSchema } from '@shared/settings.types';
import {
  createControlMeasure,
  getControlMeasures,
  toggleArchive,
  bulkCreateControlMeasures,
} from '@server/services/control-measures.service';
import { privateProcedure, trpc } from '../trpc';
import { IdSchema } from '@shared/schema.types';
import { MODULES } from '@shared/user-permissions';
import { ALLOWED_ACTIONS } from '@shared/user-permissions';
import { CONTROL_MEASURES } from '@shared/seeds/control-measures';
import { TRPCError } from '@trpc/server';

export const controlMeasuresRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE)
    .input(ControlMeasuresCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const controlMeasure = await createControlMeasure(input, ctx.user);
      if (!controlMeasure) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create control measure' });
      }
      return controlMeasure;
    }),
  list: privateProcedure.hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.VIEW).query(async ({ ctx }) => {
    return getControlMeasures(ctx.user);
  }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ ctx, input }) => {
      return toggleArchive(input.id, ctx.user);
    }),
  listDefault: privateProcedure.hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.VIEW).query(async () => {
    return CONTROL_MEASURES;
  }),
  bulkCreate: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE)
    .input(BulkControlMeasuresCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return bulkCreateControlMeasures(input, ctx.user);
    }),
});
