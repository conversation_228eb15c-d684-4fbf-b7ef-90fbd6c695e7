import { getFiles, getPresignedReadUrl, getPresignedUrl, removeFiles, updateFile } from '@server/services/file.service';
import {
  GetPresignedUrlInputPublicSchema,
  GetPresignedUrlInputSchema,
  IdArraySchema,
  ListFilesSchema,
  UpdateFilePublicSchema,
  UpdateFileSchema,
} from '@shared/schema.types';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';
import { privateProcedure, publicProcedure, trpc } from '../trpc';

export const fileRouter = trpc.router({
  getPresignedUrlPublic: publicProcedure.input(GetPresignedUrlInputPublicSchema).mutation(async ({ input }) => {
    return await getPresignedUrl(
      {
        fileName: input.fileName,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        entityType: input.entityType,
        entityId: input.entityId,
      },
      { upkeepCompanyId: input.upkeepCompanyId },
    );
  }),

  getPresignedUrl: privateProcedure.input(GetPresignedUrlInputSchema).mutation(async ({ ctx, input }) => {
    return await getPresignedUrl(
      {
        fileName: input.fileName,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        entityType: input.entityType,
        entityId: input.entityId,
      },
      ctx.user,
    );
  }),

  getPresignedReadUrl: publicProcedure.input(z.object({ s3Key: z.string() })).query(async ({ input }) => {
    return await getPresignedReadUrl(input.s3Key);
  }),

  updatePublic: publicProcedure.input(UpdateFilePublicSchema).mutation(async ({ input }) => {
    const { id, upkeepCompanyId, ...rest } = input;

    if (!id || !upkeepCompanyId || !rest.s3Key) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'File ID, upkeep company ID & s3 key is required',
      });
    }

    return await updateFile(id, rest, { upkeepCompanyId });
  }),

  update: privateProcedure.input(UpdateFileSchema).mutation(async ({ ctx, input }) => {
    const { id, ...rest } = input;

    if (!id || !rest.s3Key) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'File ID & s3 key is required',
      });
    }

    return await updateFile(id, rest, ctx.user);
  }),

  getFiles: privateProcedure.input(ListFilesSchema).query(async ({ ctx, input }) => {
    return await getFiles(input, ctx.user);
  }),

  removeFilesPublic: publicProcedure.input(IdArraySchema).mutation(async ({ input }) => {
    return await removeFiles(input);
  }),

  removeFiles: privateProcedure.input(IdArraySchema).mutation(async ({ input }) => {
    return await removeFiles(input);
  }),
});
