import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { addJobToQueue } from '@server/queue/queue-utils';
import { getAssets } from '@server/services/asset.service';
import {
  createEvent,
  createEventPublic,
  exportEvents,
  getEventById,
  listEvents,
  toggleArchiveEvent,
  updateEvent,
} from '@server/services/event.service';
import { getLocationById, getLocations } from '@server/services/location.service';
import { getUserById, getUserPublic, getUsersPublic } from '@server/services/user.service';
import { privateProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import {
  CreateEventFormPublicSchema,
  CreateEventFormSchema,
  EditEventFormSchema,
  EventCreateNotificationJobPayload,
  EventPublicCreateNotificationJobPayload,
  EventUpdateNotificationJobPayload,
  ExportEventsSchema,
  IdSchema,
  ListEventSchema,
  Location,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const eventRouter = trpc.router({
  createPublic: publicProcedure.input(CreateEventFormPublicSchema).mutation(async ({ input, ctx }) => {
    const [accessPointCreatedBy, possibleReporter] = await Promise.all([
      getUserPublic({
        upkeepCompanyId: input.upkeepCompanyId,
        id: input.accessPointCreatedBy,
      }),
      getUserPublic({
        upkeepCompanyId: input.upkeepCompanyId,
        email: input.email,
      }),
    ]);

    const createdEvent = await createEventPublic(input, {
      email: possibleReporter?.email ?? input.email,
      fullName: possibleReporter?.fullName ?? input.name,
      id: possibleReporter?.id,
    });

    if (!createdEvent) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Failed to create public event',
      });
    }

    // Create job payload for queue
    const jobPayload: EventPublicCreateNotificationJobPayload = {
      createdEvent,
      headers: ctx.req.headers,
      upkeepCompanyId: input.upkeepCompanyId,
      reporterInfo: {
        email: possibleReporter?.email ?? input.email,
        fullName: possibleReporter?.fullName ?? input.name,
        id: possibleReporter?.id,
      },
      teamMembersToNotify: [
        {
          email: accessPointCreatedBy?.email ?? '',
          fullName: accessPointCreatedBy?.fullName ?? '',
          id: accessPointCreatedBy?.id,
        },
      ],
    };

    // Generate idempotent job ID using event ID and creation timestamp
    const jobId = `public-event-${createdEvent.id}-create-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.PUBLIC_EVENT_CREATE_NOTIFICATION, jobPayload, { jobId });

    return createdEvent;
  }),
  create: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.CREATE)
    .input(CreateEventFormSchema)
    .mutation(async ({ input, ctx }) => {
      const createdEvent = await createEvent(input, ctx.user);

      if (!createdEvent) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create event',
        });
      }

      // Create job payload for queue
      const jobPayload: EventCreateNotificationJobPayload = {
        createdEvent,
        user: ctx.user,
        headers: ctx.req.headers,
        teamMembersToNotify: input?.teamMembersToNotify || undefined,
      };

      // Generate idempotent job ID using event ID and creation timestamp
      const jobId = `event-${createdEvent.id}-create-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.EVENT_CREATE_NOTIFICATION, jobPayload, { jobId });

      return createdEvent;
    }),

  getById: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const event = await getEventById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Safety event with ID ${input.id} not found`,
        });
      }

      const [assets, location, reportedByUser, usersToNotify] = await Promise.all([
        event?.assetIds && event?.assetIds.length > 0
          ? getAssets(
              {
                objectId: event.assetIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [] },
        event?.locationId ? getLocationById(event?.locationId, ctx.req.headers) : undefined,
        event?.reportedBy ? getUserById(event?.reportedBy, ctx.req.headers) : undefined,
        event?.teamMembersToNotify && event?.teamMembersToNotify?.length > 0
          ? getUsersPublic({
              upkeepCompanyId: event.upkeepCompanyId,
              objectId: event.teamMembersToNotify,
            })
          : { noResults: true, result: [], nextCursor: undefined },
      ]);

      return {
        ...event,
        reportedByUser,
        teamMembersToNotify: usersToNotify.result,
        assets: assets.result,
        location,
      };
    }),

  getByIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const event = await getEventById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Safety event with ID ${input.id} not found`,
        });
      }

      return event;
    }),

  list: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.VIEW)
    .input(
      ListEventSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ ctx, input }) => {
      const paginatedEvents = await listEvents(input, ctx.user, ctx.needPartialCheck || false);

      const locationIds = Array.from(
        new Set(paginatedEvents.result.map((event) => event.locationId).filter(Boolean)),
      ) as string[];

      const locationsResponse = await getLocations(
        {
          objectId: locationIds,
          cursor: 0,
          limit: 100,
        },
        ctx.req.headers,
      );

      const mappedLocations = locationsResponse.result.reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, Location>,
      );

      const eventsWithLocation = paginatedEvents.result.map((event) => {
        const location = mappedLocations[event.locationId || ''];

        if (!location) {
          return {
            ...event,
            location: undefined,
          };
        }

        return {
          ...event,
          location,
        };
      });

      return {
        ...paginatedEvents,
        result: eventsWithLocation,
      };
    }),

  export: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EXPORT)
    .input(ExportEventsSchema)
    .mutation(async ({ input, ctx }) => {
      const events = await exportEvents(input, ctx.user);

      const locationIds = Array.from(new Set(events.map((event) => event.locationId).filter(Boolean))) as string[];

      const locations = await getLocations(
        {
          objectId: locationIds,
          cursor: 0,
          limit: 100,
          search: '',
        },
        ctx.req.headers,
      );

      const mappedLocations = locations.result.reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, Location>,
      );

      return events.map((event) => {
        const location = mappedLocations[event.locationId || ''];

        if (!location) {
          return {
            ...event,
            location: undefined,
          };
        }

        return {
          ...event,
          location,
        };
      });
    }),

  minimalList: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.VIEW)
    .input(
      ListEventSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ ctx, input }) => {
      return await listEvents(input, ctx.user, ctx.needPartialCheck || false);
    }),

  update: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT)
    .input(EditEventFormSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedEvent = await updateEvent(input.id, input, ctx.user, ctx.needPartialCheck || false);

      if (!updatedEvent) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to update event',
        });
      }

      // Create job payload for queue
      const jobPayload: EventUpdateNotificationJobPayload = {
        updatedEvent,
        user: ctx.user,
        headers: ctx.req.headers,
        actionPrefix: 'Updated',
      };

      // Generate idempotent job ID using event ID and update timestamp
      const jobId = `event-${updatedEvent.id}-update-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.EVENT_UPDATE_NOTIFICATION, jobPayload, { jobId });

      return updatedEvent;
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await toggleArchiveEvent(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to delete event' });
      }

      // Create job payload for queue
      const jobPayload: EventUpdateNotificationJobPayload = {
        updatedEvent: result,
        user: ctx.user,
        headers: ctx.req.headers,
        actionPrefix: result.archived ? 'Archived' : 'Unarchived',
      };

      // Generate idempotent job ID using event ID and action
      const jobId = `event-${result.id}-${result.archived ? 'archive' : 'unarchive'}-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.EVENT_UPDATE_NOTIFICATION, jobPayload, { jobId });

      return result;
    }),
});
