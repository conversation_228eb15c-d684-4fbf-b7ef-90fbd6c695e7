import {
  createWorkOrderFromCapa,
  searchWorkOrdersByCapaId,
  getWorkOrdersCountByCapa,
} from '@server/services/work-order.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import {
  CreateWorkOrderFromCapaSchema,
  WorkOrderSearchInputSchema,
  CountWorkOrdersByCapaIdSchema,
  WorkOrder,
  PaginatedResponse,
} from '@shared/schema.types';

export const workOrderRouter = trpc.router({
  createFromCapa: privateProcedure.input(CreateWorkOrderFromCapaSchema).mutation(async ({ input, ctx }) => {
    return await createWorkOrderFromCapa(input, ctx.req.headers);
  }),

  getByCapa: privateProcedure
    .input(WorkOrderSearchInputSchema)
    .query(async ({ input, ctx }): Promise<PaginatedResponse<WorkOrder>> => {
      const { cursor = 0, limit = 10, capaId, sort } = input;

      // Get data with one extra item to determine if there's a next page
      const workOrders = await searchWorkOrdersByCapaId(
        {
          capaId,
          limit: limit + 1,
          offset: cursor,
          sort,
        },
        ctx.req.headers,
      );

      // Check if we got more results than requested (indicating more pages available)
      const hasMore = workOrders.length > limit;
      const result = hasMore ? workOrders.slice(0, limit) : workOrders;

      // Return cursor paginated response
      return {
        noResults: result.length === 0,
        result,
        nextCursor: hasMore ? cursor + limit : undefined,
      };
    }),

  getCountByCapa: privateProcedure
    .input(CountWorkOrdersByCapaIdSchema)
    .query(async ({ input, ctx }): Promise<number> => {
      return await getWorkOrdersCountByCapa(input, ctx.req.headers);
    }),
});
