import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TRPCError } from '@trpc/server';
import { workOrderRouter } from '../work-order.router';
import {
  createWorkOrderFromCapa,
  searchWorkOrdersByCapaId,
  getWorkOrdersCountByCapa,
} from '../../../services/work-order.service';
import { createAdminUser, createMockContext } from './test-utils';
import type { User } from '../../../../shared/schema.types';
import type { inferProcedureInput } from '@trpc/server';
import type { AppRouter } from '../index';

// Mock work order service
vi.mock('../../../services/work-order.service', () => ({
  createWorkOrderFromCapa: vi.fn(),
  searchWorkOrdersByCapaId: vi.fn(),
  getWorkOrdersCountByCapa: vi.fn(),
}));

// Type for the createFromCapa input
type CreateFromCapaInput = inferProcedureInput<AppRouter['workOrder']['createFromCapa']>;

describe('Work Order Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  const validCapaInput = {
    id: 'capa-123',
    title: 'Fix hydraulic system leak',
    slug: 'CAPA-0063',
    actionsToAddress: 'Replace the hydraulic seal on Pump 42 immediately',
    priority: 'high' as const,
    dueDate: '2024-12-31T23:59:59.999Z',
    locationId: 'loc-456',
    assetId: 'asset-789',
    userAssignedTo: 'user-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createFromCapa procedure', () => {
    it('should successfully create work order from CAPA', async () => {
      vi.mocked(createWorkOrderFromCapa).mockResolvedValue(true);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.createFromCapa(validCapaInput);

      expect(createWorkOrderFromCapa).toHaveBeenCalledWith(validCapaInput, mockContext.req.headers);
      expect(result).toBe(true);
    });

    it('should handle service errors', async () => {
      vi.mocked(createWorkOrderFromCapa).mockRejectedValue(new Error('Work order creation failed'));

      const caller = workOrderRouter.createCaller(mockContext);

      await expect(caller.createFromCapa(validCapaInput)).rejects.toThrow('Work order creation failed');
    });

    it('should handle service returning false', async () => {
      vi.mocked(createWorkOrderFromCapa).mockResolvedValue(false);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.createFromCapa(validCapaInput);

      expect(result).toBe(false);
    });

    it('should validate required fields', async () => {
      const caller = workOrderRouter.createCaller(mockContext);

      await expect(caller.createFromCapa({ ...validCapaInput, id: '' })).rejects.toThrow();
      await expect(caller.createFromCapa({ ...validCapaInput, title: undefined as any })).rejects.toThrow();
      await expect(caller.createFromCapa({ ...validCapaInput, priority: 'invalid' as any })).rejects.toThrow();
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(workOrderRouter.createCaller(nullUserContext).createFromCapa(validCapaInput)).rejects.toThrow(
        TRPCError,
      );
    });
  });

  describe('getByCapa procedure', () => {
    it('should get work orders by CAPA ID with cursor pagination', async () => {
      const mockWorkOrders = [
        { id: 'wo-1', title: 'Work Order 1' },
        { id: 'wo-2', title: 'Work Order 2' },
      ];

      vi.mocked(searchWorkOrdersByCapaId).mockResolvedValue(mockWorkOrders as any);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.getByCapa({
        capaId: ['capa-123'],
        cursor: 0,
        limit: 10,
      });

      expect(result).toEqual({
        noResults: false,
        result: mockWorkOrders,
        nextCursor: undefined,
      });

      expect(searchWorkOrdersByCapaId).toHaveBeenCalledWith(
        {
          capaId: ['capa-123'],
          limit: 11, // limit + 1 to check for next page
          offset: 0,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );
    });

    it('should handle empty search results', async () => {
      vi.mocked(searchWorkOrdersByCapaId).mockResolvedValue([]);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.getByCapa({
        capaId: ['nonexistent'],
        cursor: 0,
        limit: 10,
      });

      expect(result).toEqual({
        noResults: true,
        result: [],
        nextCursor: undefined,
      });
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(
        workOrderRouter.createCaller(nullUserContext).getByCapa({
          capaId: ['capa-123'],
          cursor: 0,
          limit: 10,
        }),
      ).rejects.toThrow(TRPCError);
    });
  });
});
