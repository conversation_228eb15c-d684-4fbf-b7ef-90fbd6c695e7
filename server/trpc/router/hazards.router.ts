import { bulkCreateHazards, createHazard, getHazards, toggleArchive } from '@server/services/hazards.service';
import { IdSchema } from '@shared/schema.types';
import { HAZARDS } from '@shared/seeds/hazards';
import { BulkHazardsCreateSchema, HazardsCreateSchema } from '@shared/settings.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { privateProcedure, trpc } from '../trpc';

export const hazardsRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE)
    .input(HazardsCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const hazard = await createHazard(input, ctx.user);
      if (!hazard) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create hazard' });
      }
      return hazard;
    }),
  list: privateProcedure.hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.VIEW).query(async ({ ctx }) => {
    return getHazards(ctx.user);
  }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ ctx, input }) => {
      return toggleArchive(input.id, ctx.user);
    }),
  listDefault: privateProcedure.hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.VIEW).query(async () => {
    return HAZARDS;
  }),
  bulkCreate: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE)
    .input(BulkHazardsCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return bulkCreateHazards(input, ctx.user);
    }),
});
