import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { addJobToQueue } from '@server/queue/queue-utils';
import { createComment, deleteComment, fetchCommentById, fetchComments } from '@server/services/comment.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import {
  CommentMentionNotificationJobPayload,
  CreateCommentFormSchema,
  IdSchema,
  ListCommentsSchema,
} from '@shared/schema.types';
import { TRPCError } from '@trpc/server';

export const commentRouter = trpc.router({
  create: privateProcedure.input(CreateCommentFormSchema).mutation(async ({ input, ctx }) => {
    const result = await createComment(input, ctx.user);

    if (!result) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Failed to create comment. Please try again.',
      });
    }

    const { newComment, savedMentions } = result;

    // Only send notification if there are mentions
    if (savedMentions && savedMentions.length > 0) {
      // Create job payload for queue
      const jobPayload: CommentMentionNotificationJobPayload = {
        newComment,
        savedMentions,
        user: ctx.user,
        headers: ctx.req.headers,
        input,
      };

      // Generate idempotent job ID using comment ID and creation timestamp
      const jobId = `comment-${newComment.id}-mention-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.COMMENT_MENTION_NOTIFICATION, jobPayload, { jobId });
    }

    return newComment;
  }),
  list: privateProcedure.input(ListCommentsSchema).query(async ({ input, ctx }) => {
    return await fetchComments({ entityId: input.entityId, entityType: input.entityType }, ctx.user);
  }),
  getById: privateProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    return await fetchCommentById({ id: input.id }, ctx.user);
  }),
  delete: privateProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    return await deleteComment({ id: input.id }, ctx.user);
  }),
});
