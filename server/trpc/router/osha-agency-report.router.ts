import {
  create<PERSON>haAgencyReport,
  exportOshaAgencyReports,
  getOshaAgencyReport,
  listOshaAgencyReports,
  toggleArchiveOshaAgencyReport,
  updateOshaAgencyReport,
} from '@server/services/osha-agency-report.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { getIpAddress } from '@server/utils/get-ip-address';
import {
  CreateOshaAgencyReportFormSchema,
  EditOshaAgencyReportFormSchema,
  ExportOshaAgencyReportsSchema,
  ListOshaAgencyReportsSchema,
} from '@shared/osha.types';
import { IdSchema } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';

export const oshaAgencyReportRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(CreateOshaAgencyReportFormSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      return await createOshaAgencyReport({ ...input, ipAddress, userAgent: ctx.req.headers['user-agent'] }, ctx.user);
    }),
  update: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT)
    .input(EditOshaAgencyReportFormSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      return await updateOshaAgencyReport(
        {
          ...input,
          ipAddress,
          userAgent: ctx.req.headers['user-agent'],
        },
        ctx.user,
      );
    }),
  list: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(ListOshaAgencyReportsSchema.default({ cursor: 0, limit: 10 }))
    .query(async ({ input, ctx }) => {
      return await listOshaAgencyReports(input, ctx.user);
    }),

  export: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EXPORT)
    .input(ExportOshaAgencyReportsSchema)
    .mutation(async ({ input, ctx }) => {
      return await exportOshaAgencyReports(input, ctx.user);
    }),

  getById: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const report = await getOshaAgencyReport(input.id, ctx.user);

      if (!report) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `OSHA agency report with ID ${input.id} not found`,
        });
      }

      return report;
    }),

  getByIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT)
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return await getOshaAgencyReport(input.id, ctx.user);
    }),

  toggleArchive: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const userAgent = ctx.req.headers['user-agent'] ?? 'unknown';
      return await toggleArchiveOshaAgencyReport({ id: input.id, ipAddress, userAgent }, ctx.user);
    }),
});
