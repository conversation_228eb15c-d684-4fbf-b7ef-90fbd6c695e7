import { getUsersPublic, getUsers, getUserPublic } from '@server/services/user.service';
import { privateProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import { PublicSearchSchema, UpkeepCompanyIdSchema } from '@shared/schema.types';
import { UserAccountTypeSchema } from '@shared/user-permissions';
import { z } from 'zod';

export const userRouter = trpc.router({
  me: privateProcedure.query(async ({ ctx }) => {
    return ctx.user;
  }),

  getUsers: privateProcedure
    .input(
      z.object({
        search: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return await getUsers({ headers: ctx.req.headers, search: input.search });
    }),

  getUsersPublic: publicProcedure.input(PublicSearchSchema).query(async ({ input }) => {
    if (!input.upkeepCompanyId) {
      return {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };
    }

    return await getUsersPublic({
      upkeepCompanyId: input.upkeepCompanyId,
      search: input?.search,
      objectId: Array.isArray(input?.objectId) ? input?.objectId : input?.objectId ? [input?.objectId] : undefined,
      limit: input?.limit,
      cursor: input?.cursor,
      mustIncludeObjectIds: input?.mustIncludeObjectIds,
      userAccountType: input?.userAccountType,
    });
  }),

  getUserPublic: publicProcedure
    .input(z.object({ id: z.string(), userAccountType: UserAccountTypeSchema.optional() }).and(UpkeepCompanyIdSchema))
    .query(async ({ input }) => {
      return await getUserPublic({
        id: input.id,
        upkeepCompanyId: input.upkeepCompanyId,
        userAccountType: input.userAccountType,
      });
    }),
});
