import { privateProcedure, trpc } from '@server/trpc/trpc';
import { MODULES, ALLOWED_ACTIONS } from '@shared/user-permissions';
import { CreateFullJhaSchema } from '@shared/jha.types';
import { createJha } from '@server/services/jha.service';
import { TRPCError } from '@trpc/server';

export const jhaRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE)
    .input(CreateFullJhaSchema)
    .mutation(async ({ input, ctx }) => {
      const jha = await createJha(input, ctx.user);
      if (!jha) {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Failed to create J<PERSON>' });
      }
      return jha;
    }),
});
