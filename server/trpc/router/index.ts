import { accessPointRouter } from '@server/trpc/router/access-point.router';
import { aiRouter } from '@server/trpc/router/ai.router';
import { assetRouter } from '@server/trpc/router/asset.router';
import { auditTrailRouter } from '@server/trpc/router/audit-trail.router';
import { commentRouter } from '@server/trpc/router/comment.router';
import { configRouter } from '@server/trpc/router/config.router';
import { controlMeasuresRouter } from '@server/trpc/router/control-measures.router';
import { eventRouter } from '@server/trpc/router/event.router';
import { fileRouter } from '@server/trpc/router/file.router';
import { hazardsRouter } from '@server/trpc/router/hazards.router';
import { jhaRouter } from '@server/trpc/router/jha.router';
import { locationRouter } from '@server/trpc/router/location.router';
import { oshaAgencyReportRouter } from '@server/trpc/router/osha-agency-report.router';
import { oshaAuditTrailRouter } from '@server/trpc/router/osha-audit-trail.router';
import { oshaLocationRouter } from '@server/trpc/router/osha-location.router';
import { oshaReportRouter } from '@server/trpc/router/osha-report.router';
import { oshaSummaryRouter } from '@server/trpc/router/osha-summary.router';
import { userRouter } from '@server/trpc/router/user.router';
import { workOrderRouter } from '@server/trpc/router/work-order.router';
import { trpc } from '@server/trpc/trpc';
import { capaRouter } from 'server/trpc/router/capa.router';

export const router = trpc.router({
  event: eventRouter,
  capa: capaRouter,
  comment: commentRouter,
  auditTrail: auditTrailRouter,
  user: userRouter,
  ai: aiRouter,
  file: fileRouter,
  asset: assetRouter,
  location: locationRouter,
  accessPoint: accessPointRouter,
  workOrder: workOrderRouter,
  config: configRouter,
  oshaReport: oshaReportRouter,
  oshaAgencyReport: oshaAgencyReportRouter,
  oshaSummary: oshaSummaryRouter,
  oshaLocation: oshaLocationRouter,
  oshaAuditTrail: oshaAuditTrailRouter,
  jha: jhaRouter,
  hazards: hazardsRouter,
  controlMeasures: controlMeasuresRouter,
});

export type AppRouter = typeof router;
