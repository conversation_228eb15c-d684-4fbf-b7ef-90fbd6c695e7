import { getAuditTrail } from '@server/services/audit-trail.service';
import { getUsers } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { GetAuditTrailSchema, User } from '@shared/schema.types';

export const auditTrailRouter = trpc.router({
  get: privateProcedure.input(GetAuditTrailSchema).query(async ({ input, ctx }) => {
    const auditTrail = await getAuditTrail(input.entityId, ctx.user);

    const userIds = Array.from(new Set(auditTrail.map((event) => event.userId).filter(Boolean))) as string[];

    const users = await getUsers({
      headers: ctx.req.headers,
      objectId: Array.from(userIds),
    });

    const mappedUsers = users.reduce(
      (acc, user) => {
        acc[user.id] = user;
        return acc;
      },
      {} as Record<string, User>,
    );

    const auditTrailWithUser = auditTrail.map((audit) => {
      return {
        ...audit,
        user: mappedUsers[audit.userId || ''],
      };
    });

    return auditTrailWithUser;
  }),
});
