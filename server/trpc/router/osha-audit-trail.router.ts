import { createOshaAuditTrail, getAuditTrail } from '@server/services/osha-audit-trail.service';
import { getUsersPublic } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { getIpAddress } from '@server/utils/get-ip-address';
import { OshaAuditTrailProcedureSchema } from '@shared/osha.types';

import { IdSchema, UserPublic } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';

export const oshaAuditTrailRouter = trpc.router({
  get: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const auditTrail = await getAuditTrail(input.id, ctx.user);

      const usersToFetch = Array.from(new Set(auditTrail.map((event) => event.createdBy)));

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: usersToFetch });

      const mappedUsers = users.result.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return auditTrail.map((event) => ({
        ...event,
        createdBy: mappedUsers[event.createdBy],
      }));
    }),
  create: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(OshaAuditTrailProcedureSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req) || 'unknown';
      const userAgent = ctx.req.headers['user-agent'] || 'unknown';

      return createOshaAuditTrail({ ...input, ipAddress, userAgent }, ctx.user);
    }),
});
