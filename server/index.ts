/* 
  Note: In<PERSON><PERSON> must be the first module loaded to properly trace all calls. 
  see: https://www.ibm.com/docs/en/instana-observability/current?topic=nodejs-collector-installation#review-common-installation-considerations
  for additional details
*/
import '@server/utils/instana';

import { router as trpcRouter } from '@server/trpc/router';
import { createTrpcContext } from '@server/trpc/trpc';
import { logger } from '@server/utils/logger';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import express, { Express } from 'express';
import { createHash } from 'crypto';
import { readFile, stat } from 'fs/promises';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import ViteExpress from 'vite-express';
import { env } from '../env';
import { shutdown } from './redis/client';
import { registerWorkers, shutdownWorkers } from './workers/worker-manager';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

await registerWorkers();

const app: Express = express();

app.get('/health', (_, res) => {
  res.status(200).json({
    status: 'ok',
    githubRef: env.GITHUB_REF,
    buildTag: env.BUILD_TAG,
    commitSha: env.COMMIT_SHA,
  });
});

app.use(
  '/ehs/trpc',
  createExpressMiddleware({
    router: trpcRouter,
    createContext: createTrpcContext,
    maxBodySize: 10 * 1024 * 1024, // 10MB
  }),
);

// Development mode: ViteExpress will handle serving the client
// Production mode: Serve static files from the dist directory
app.use((_, res, next) => {
  res.set('Content-Security-Policy', `frame-ancestors 'self' ${process.env.UPKEEP_APP_URL};`);
  next();
});

if (env.NODE_ENV === 'production' || env.NODE_ENV === 'staging') {
  // Serve static files under /ehs path
  app.use('/ehs', express.static(join(__dirname, '../dist/public')));

  // Handle all other routes by serving index.html with ETag caching
  let cachedETag: string | null = null;
  let cachedMTime: number | null = null;

  // Catch-all handler for SPA routes - this should come after static files
  app.get(/^\/ehs/, async (req, res) => {
    const htmlPath = join(__dirname, '../dist/public/index.html');

    try {
      const stats = await stat(htmlPath);
      const mtime = stats.mtime.getTime();

      // Generate ETag if file changed or not cached
      if (cachedMTime !== mtime || !cachedETag) {
        const content = await readFile(htmlPath, 'utf8');
        cachedETag = createHash('md5').update(content).digest('hex');
        cachedMTime = mtime;
      }

      // Check if client has matching ETag
      let clientETag = req.headers['if-none-match'];
      if (Array.isArray(clientETag)) {
        clientETag = clientETag[0];
      }
      clientETag = clientETag?.replace(/^"(.*)"$/, '$1'); // Strip surrounding quotes
      if (clientETag === cachedETag) {
        res.status(304).end();
        return;
      }

      // Send file with ETag for future validation
      res.set('ETag', cachedETag);
      res.set('Cache-Control', 'no-cache'); // Allow caching but require validation
      res.sendFile(htmlPath);
    } catch (error) {
      logger.error('Error serving index.html', { error });
      res.status(500).send('Internal Server Error');
    }
  });
}

ViteExpress.listen(app, env.PORT, () => {
  logger.info('Server started', { port: env.PORT });
});

// Graceful shutdown handler for both SIGINT (Ctrl+C) and SIGTERM (Kubernetes)
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);

  try {
    await shutdown();
    await shutdownWorkers();
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', { error });
    process.exit(1);
  }
};

// Handle SIGINT (Ctrl+C in development)
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle SIGTERM (Kubernetes pod termination)
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
