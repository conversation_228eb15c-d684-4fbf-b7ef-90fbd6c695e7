import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { auditTrail, controlMeasures } from '@shared/schema';
import { controlMeasuresRouter } from '@server/trpc/router/control-measures.router';
import { and, eq } from 'drizzle-orm';
import { CONTROL_MEASURES } from '@shared/seeds/control-measures';

describe('controlMeasuresRouter', () => {
  const mockContext = createMockContext(mockUser);
  // Use the router directly since it's already wrapped in trpc.router()
  const caller = controlMeasuresRouter.createCaller(mockContext);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await db.delete(controlMeasures);
    await db.delete(auditTrail);
  });

  describe('create', () => {
    it('should create a new JHA control measure', async () => {
      const input = { name: 'Test Control Measure', type: 'personal_protective_equipment' as const };
      const result = await caller.create(input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);
      expect(result.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(result.createdBy).toBe(mockUser.id);
      expect(result.id).toBeDefined();

      // Verify in database
      const dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, result.id),
      });

      expect(dbControlMeasure).toBeDefined();
      expect(dbControlMeasure?.name).toBe(input.name);
      expect(dbControlMeasure?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(dbControlMeasure?.createdBy).toBe(mockUser.id);
    });

    it('should handle creation with empty name', async () => {
      await expect(caller.create({ name: '', type: 'personal_protective_equipment' as const })).rejects.toThrow();
    });

    it('should allow creating multiple control measures with different names', async () => {
      const controlMeasure1 = await caller.create({
        name: 'First Control Measure',
        type: 'personal_protective_equipment',
      });
      const controlMeasure2 = await caller.create({
        name: 'Second Control Measure',
        type: 'personal_protective_equipment',
      });

      expect(controlMeasure1.name).toBe('First Control Measure');
      expect(controlMeasure2.name).toBe('Second Control Measure');
      expect(controlMeasure1.id).not.toBe(controlMeasure2.id);
    });
  });

  describe('list', () => {
    it('should return company-specific control measures', async () => {
      // Create a company-specific control measure
      const createdControlMeasure = await caller.create({
        name: 'Company Test Control Measure',
        type: 'personal_protective_equipment',
      });

      const result = await caller.list();

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);

      // Find our company-specific control measure
      const companyControlMeasure = result.find((cm) => cm.id === createdControlMeasure.id);
      expect(companyControlMeasure).toBeDefined();
      expect(companyControlMeasure?.name).toBe('Company Test Control Measure');
      expect(companyControlMeasure?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
    });

    it('should not include archived control measures', async () => {
      // Create and archive a control measure
      const created = await caller.create({ name: 'To Be Archived', type: 'personal_protective_equipment' });
      await caller.toggleArchive({ id: created.id });

      const result = await caller.list();

      // Should not include the archived control measure
      const archivedControlMeasure = result.find((cm) => cm.id === created.id);
      expect(archivedControlMeasure).toBeUndefined();
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active control measure', async () => {
      // Create a control measure
      const controlMeasure = await caller.create({
        name: 'Archive Test Control Measure',
        type: 'personal_protective_equipment' as const,
      });
      expect(controlMeasure.archivedAt).toBeNull();

      // Archive the control measure
      await caller.toggleArchive({ id: controlMeasure.id });

      // Verify in database
      const dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, controlMeasure.id),
      });

      expect(dbControlMeasure?.archivedAt).not.toBeNull();

      // Check audit trail
      const auditEntries = await db
        .select()
        .from(auditTrail)
        .where(and(eq(auditTrail.entityId, controlMeasure.id), eq(auditTrail.action, 'archived')));

      expect(auditEntries.length).toBeGreaterThan(0);
      expect(auditEntries[0].entityType).toBe('control_measure');
      expect(auditEntries[0].action).toBe('archived');
      expect(auditEntries[0].upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
    });

    it('should unarchive an archived control measure', async () => {
      // Create a control measure
      const controlMeasure = await caller.create({
        name: 'Unarchive Test Control Measure',
        type: 'personal_protective_equipment' as const,
      });

      // Archive it first
      await caller.toggleArchive({ id: controlMeasure.id });

      // Verify it's archived
      let dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, controlMeasure.id),
      });
      expect(dbControlMeasure?.archivedAt).not.toBeNull();

      // Unarchive the control measure
      await caller.toggleArchive({ id: controlMeasure.id });

      // Verify in database it's unarchived
      dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, controlMeasure.id),
      });

      expect(dbControlMeasure?.archivedAt).toBeNull();
    });

    it('should handle non-existent control measure gracefully', async () => {
      // Try to toggle archive on non-existent control measure (using valid CUID2 format)
      const result = await caller.toggleArchive({ id: 'clwqxzj8a0001d6mjzx6k4n2x' });

      // Should return undefined when control measure doesn't exist
      expect(result).toBeUndefined();
    });

    it('should preserve control measure data when toggling archive', async () => {
      // Create a control measure
      const originalControlMeasure = await caller.create({
        name: 'Preserve Data Test',
        type: 'personal_protective_equipment',
      });

      // Archive the control measure
      await caller.toggleArchive({ id: originalControlMeasure.id });

      // Verify data is preserved
      let dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, originalControlMeasure.id),
      });

      expect(dbControlMeasure?.name).toBe('Preserve Data Test');
      expect(dbControlMeasure?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(dbControlMeasure?.createdBy).toBe(mockUser.id);
      expect(dbControlMeasure?.archivedAt).not.toBeNull();

      // Unarchive the control measure
      await caller.toggleArchive({ id: originalControlMeasure.id });

      // Verify data is still preserved
      dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, originalControlMeasure.id),
      });

      expect(dbControlMeasure?.name).toBe('Preserve Data Test');
      expect(dbControlMeasure?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(dbControlMeasure?.createdBy).toBe(mockUser.id);
      expect(dbControlMeasure?.archivedAt).toBeNull();
    });

    it('should handle multiple archive/unarchive cycles', async () => {
      // Create a control measure
      const controlMeasure = await caller.create({
        name: 'Multiple Cycles Test',
        type: 'personal_protective_equipment' as const,
      });

      // Multiple archive/unarchive cycles
      for (let i = 0; i < 3; i++) {
        // Archive
        await caller.toggleArchive({ id: controlMeasure.id });
        let dbControlMeasure = await db.query.controlMeasures.findFirst({
          where: eq(controlMeasures.id, controlMeasure.id),
        });
        expect(dbControlMeasure?.archivedAt).not.toBeNull();

        // Unarchive
        await caller.toggleArchive({ id: controlMeasure.id });
        dbControlMeasure = await db.query.controlMeasures.findFirst({
          where: eq(controlMeasures.id, controlMeasure.id),
        });
        expect(dbControlMeasure?.archivedAt).toBeNull();
      }
    });

    it('should validate required id parameter', async () => {
      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();

      // Test invalid id format
      await expect(caller.toggleArchive({ id: 'invalid-id' })).rejects.toThrow();
    });
  });

  describe('integration workflow', () => {
    it('should handle complete workflow: create, list, archive, list again, unarchive', async () => {
      // Step 1: Create a control measure
      const controlMeasure = await caller.create({
        name: 'Workflow Test Control Measure',
        type: 'personal_protective_equipment' as const,
      });
      expect(controlMeasure.name).toBe('Workflow Test Control Measure');

      // Step 2: List control measures (should include our control measure)
      let controlMeasures = await caller.list();
      let ourControlMeasure = controlMeasures.find((cm) => cm.id === controlMeasure.id);
      expect(ourControlMeasure).toBeDefined();

      // Step 3: Archive the control measure
      await caller.toggleArchive({ id: controlMeasure.id });

      // Step 4: List control measures (should not include archived control measure)
      controlMeasures = await caller.list();
      ourControlMeasure = controlMeasures.find((cm) => cm.id === controlMeasure.id);
      expect(ourControlMeasure).toBeUndefined();

      // Step 5: Unarchive the control measure
      await caller.toggleArchive({ id: controlMeasure.id });

      // Step 6: List control measures (should include our control measure again)
      controlMeasures = await caller.list();
      ourControlMeasure = controlMeasures.find((cm) => cm.id === controlMeasure.id);
      expect(ourControlMeasure).toBeDefined();
      expect(ourControlMeasure?.name).toBe('Workflow Test Control Measure');
    });

    it('should maintain data consistency with multiple control measures and operations', async () => {
      // Create multiple control measures
      const controlMeasure1 = await caller.create({
        name: 'Consistency Test 1',
        type: 'personal_protective_equipment',
      });
      const controlMeasure2 = await caller.create({
        name: 'Consistency Test 2',
        type: 'personal_protective_equipment',
      });
      const controlMeasure3 = await caller.create({
        name: 'Consistency Test 3',
        type: 'personal_protective_equipment',
      });

      // Archive one control measure
      await caller.toggleArchive({ id: controlMeasure2.id });

      // List control measures
      const controlMeasures = await caller.list();

      // Should include controlMeasure1 and controlMeasure3, but not controlMeasure2
      const controlMeasure1Found = controlMeasures.find((cm) => cm.id === controlMeasure1.id);
      const controlMeasure2Found = controlMeasures.find((cm) => cm.id === controlMeasure2.id);
      const controlMeasure3Found = controlMeasures.find((cm) => cm.id === controlMeasure3.id);

      expect(controlMeasure1Found).toBeDefined();
      expect(controlMeasure2Found).toBeUndefined(); // archived
      expect(controlMeasure3Found).toBeDefined();
    });
  });

  describe('listDefault', () => {
    it('should return the default list of control measures and types', async () => {
      const result = await caller.listDefault();
      expect(result).toEqual(CONTROL_MEASURES);
    });
  });

  describe('bulkCreate', () => {
    it('should create multiple control measures at once', async () => {
      const input = [
        { name: 'Bulk Control Measure 1', type: 'engineering_controls' as const },
        { name: 'Bulk Control Measure 2', type: 'administrative_controls' as const },
        { name: 'Bulk Control Measure 3', type: 'personal_protective_equipment' as const },
      ];

      const result = await caller.bulkCreate(input);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(3);

      // Verify each created control measure
      for (let i = 0; i < input.length; i++) {
        expect(result[i].name).toBe(input[i].name);
        expect(result[i].type).toBe(input[i].type);
        expect(result[i].upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(result[i].createdBy).toBe(mockUser.id);
        expect(result[i].id).toBeDefined();
      }

      // Query database to validate all control measures were created
      const dbControlMeasures = await db
        .select()
        .from(controlMeasures)
        .where(
          and(
            eq(controlMeasures.upkeepCompanyId, mockUser.upkeepCompanyId),
            eq(controlMeasures.createdBy, mockUser.id),
          ),
        );

      expect(dbControlMeasures.length).toBeGreaterThanOrEqual(3);

      // Verify each control measure exists in database
      for (const createdControlMeasure of result) {
        const dbControlMeasure = dbControlMeasures.find((cm) => cm.id === createdControlMeasure.id);
        expect(dbControlMeasure).toBeDefined();
        expect(dbControlMeasure?.name).toBe(createdControlMeasure.name);
        expect(dbControlMeasure?.type).toBe(createdControlMeasure.type);
        expect(dbControlMeasure?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(dbControlMeasure?.createdBy).toBe(mockUser.id);
      }
    });

    it('should reject empty array input', async () => {
      await expect(caller.bulkCreate([])).rejects.toThrow('At least one control measure is required');
    });

    it('should handle single control measure in bulk create', async () => {
      const input = [{ name: 'Single Bulk Control Measure', type: 'specialized_advanced_controls' as const }];
      const result = await caller.bulkCreate(input);

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0].name).toBe('Single Bulk Control Measure');
      expect(result[0].type).toBe('specialized_advanced_controls');
      expect(result[0].upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(result[0].createdBy).toBe(mockUser.id);

      // Verify in database
      const dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, result[0].id),
      });

      expect(dbControlMeasure).toBeDefined();
      expect(dbControlMeasure?.name).toBe('Single Bulk Control Measure');
      expect(dbControlMeasure?.type).toBe('specialized_advanced_controls');
    });

    it('should handle duplicate control measure names with different types as separate control measures', async () => {
      // First, create a control measure
      const originalControlMeasure = await caller.create({
        name: 'Duplicate Test Control Measure',
        type: 'engineering_controls',
      });

      // Try to bulk create with the same name but different type - should create new control measure
      const input = [{ name: 'Duplicate Test Control Measure', type: 'administrative_controls' as const }];
      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(1);
      expect(result[0].name).toBe('Duplicate Test Control Measure');
      expect(result[0].type).toBe('administrative_controls');
      expect(result[0].id).not.toBe(originalControlMeasure.id); // Should be a different record

      // Verify both control measures exist in database
      const dbControlMeasures = await db
        .select()
        .from(controlMeasures)
        .where(
          and(
            eq(controlMeasures.name, 'Duplicate Test Control Measure'),
            eq(controlMeasures.upkeepCompanyId, mockUser.upkeepCompanyId),
          ),
        );

      expect(dbControlMeasures.length).toBe(2);
      const engineeringControlMeasure = dbControlMeasures.find((cm) => cm.type === 'engineering_controls');
      const administrativeControlMeasure = dbControlMeasures.find((cm) => cm.type === 'administrative_controls');

      expect(engineeringControlMeasure).toBeDefined();
      expect(administrativeControlMeasure).toBeDefined();
      expect(engineeringControlMeasure?.id).toBe(originalControlMeasure.id);
      expect(administrativeControlMeasure?.id).toBe(result[0].id);
    });

    it('should unarchive previously archived control measure when creating exact duplicate', async () => {
      // Create and archive a control measure
      const originalControlMeasure = await caller.create({
        name: 'Archive Test Control Measure',
        type: 'engineering_controls',
      });
      await caller.toggleArchive({ id: originalControlMeasure.id });

      // Verify it's archived
      let dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, originalControlMeasure.id),
      });
      expect(dbControlMeasure?.archivedAt).not.toBeNull();

      // Try to bulk create with the same name AND type (exact duplicate)
      const input = [{ name: 'Archive Test Control Measure', type: 'engineering_controls' as const }];
      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(1);
      expect(result[0].id).toBe(originalControlMeasure.id);
      expect(result[0].type).toBe('engineering_controls');

      // Verify in database that it's unarchived
      dbControlMeasure = await db.query.controlMeasures.findFirst({
        where: eq(controlMeasures.id, originalControlMeasure.id),
      });

      expect(dbControlMeasure?.archivedAt).toBeNull(); // Should be unarchived
      expect(dbControlMeasure?.type).toBe('engineering_controls'); // Should remain same type
      expect(dbControlMeasure?.updatedAt).not.toBeNull();
    });

    it('should maintain company isolation in bulk create', async () => {
      const input = [
        { name: 'Company A Bulk Control Measure 1', type: 'engineering_controls' as const },
        { name: 'Company A Bulk Control Measure 2', type: 'administrative_controls' as const },
      ];

      const result = await caller.bulkCreate(input);

      // Verify all control measures belong to the correct company
      for (const controlMeasure of result) {
        expect(controlMeasure.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(controlMeasure.createdBy).toBe(mockUser.id);
      }

      // Query database and verify company isolation
      const dbControlMeasures = await db
        .select()
        .from(controlMeasures)
        .where(eq(controlMeasures.upkeepCompanyId, mockUser.upkeepCompanyId));

      const bulkCreatedControlMeasures = dbControlMeasures.filter((cm) =>
        input.some((inputControlMeasure) => inputControlMeasure.name === cm.name),
      );

      expect(bulkCreatedControlMeasures.length).toBe(2);
      for (const dbControlMeasure of bulkCreatedControlMeasures) {
        expect(dbControlMeasure.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(dbControlMeasure.createdBy).toBe(mockUser.id);
      }
    });
  });
});
