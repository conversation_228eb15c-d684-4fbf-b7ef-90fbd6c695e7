import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { capaRouter } from '@server/trpc/router/capa.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { capaTypeEnum, statusEnum, capaPriorityEnum, auditTrail } from '@shared/schema';
import { db } from '@server/db';
import { capas, files } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { mockTechnicianUser, mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasEventPermission: vi.fn().mockReturnValue(true),
  canEditEvent: vi.fn().mockReturnValue(true),
  canExportEvent: vi.fn().mockReturnValue(true),
}));

// Mock the queue system
vi.mock('@server/queue/queue-utils', () => ({
  addJobToQueue: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@server/queue/job-names', () => ({
  QUEUE_JOB_NAMES: {
    UPDATE_CAPA_NOTIFICATION: 'update-capa-notification',
    ASSIGNED_CAPA_NOTIFICATION: 'assigned-capa-notification',
  },
}));

// Mock the notification service (this is still used for create operations)
vi.mock('@server/services/capa-notification.service', () => ({
  sendCapaAssignedNotification: vi.fn().mockResolvedValue(undefined),
  sendCapaUpdateNotification: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Asset' }]),
}));

vi.mock('@server/services/ai.service', () => ({
  createCapaSummary: vi.fn().mockResolvedValue('Test summary'),
}));

describe('capa router', () => {
  const mockContext = createMockContext(mockUser) as any;
  let createdCapaId: string;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Setup default mock implementations
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [
        {
          id: mockUser.id,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          username: mockUser.email,
          email: mockUser.email,
        },
      ],
      nextCursor: undefined,
    });
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    // Clean up any created CAPAs and files
    await db.delete(files);
    await db.delete(capas);
    await db.delete(auditTrail);
  });

  describe('create', () => {
    it('should create a new capa and fetch a summary', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      // Store the ID for cleanup
      createdCapaId = capa!.id;

      // Verify the CAPA was created
      expect(capa).toBeDefined();
      expect(capa!.title).toBe(mockCapaInput.title);
      expect(capa!.slug).toBeDefined(); // this is generated by the database sequence
      expect(capa!.type).toBe(mockCapaInput.type);
      expect(capa!.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa!.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa!.status).toBe(mockCapaInput.status);
      expect(capa!.priority).toBe(mockCapaInput.priority);
      expect(capa!.summary).toBe('Test summary');

      // Verify we can find it in the database
      const dbCapa = await db.query.capas.findFirst({
        where: eq(capas.id, capa!.id),
      });

      expect(dbCapa).toBeDefined();
      expect(dbCapa?.title).toBe(mockCapaInput.title);

      // Verify permission check was called
      expect(hasPermission).toHaveBeenCalled();

      // Verify queue job was added for assigned notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            title: mockCapaInput.title,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-assign-\\d+`)),
        }),
      );
    });

    it('should create a new capa and not fetch a summary', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      const mockCapaInput = {
        title: `Test CAPA with summary ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        summary: 'Summary provided in the request',
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      expect(capa).toBeDefined();
      expect(capa!.title).toBe(mockCapaInput.title);
      expect(capa!.slug).toBeDefined(); // this is generated by the database sequence
      expect(capa!.type).toBe(mockCapaInput.type);
      expect(capa!.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa!.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa!.status).toBe(mockCapaInput.status);
      expect(capa!.priority).toBe(mockCapaInput.priority);
      expect(capa!.summary).toBe(mockCapaInput.summary);

      // Verify queue job was added for assigned notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            title: mockCapaInput.title,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(/capa-.*-assign-\d+/),
        }),
      );
    });

    it('should validate required fields', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test missing required fields
      await expect(caller.create({} as any)).rejects.toThrow();

      // Test missing title
      await expect(
        caller.create({
          type: capaTypeEnum.enumValues[0],
          rcaFindings: 'Test findings',
          ownerId: mockUser.id,
          actionsToAddress: 'Test actions',
          status: statusEnum.enumValues[0],
          priority: capaPriorityEnum.enumValues[0],
        } as any),
      ).rejects.toThrow();
    });
  });

  describe('getById', () => {
    it('should get a capa by id without files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now get it by ID
      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);
      expect(capa.slug).toBeDefined();
      expect(capa.type).toBe(mockCapaInput.type);
      expect(capa.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa.status).toBe(mockCapaInput.status);
      expect(capa.priority).toBe(mockCapaInput.priority);
      expect(capa.attachments).toBeNull();

      // Verify user service was called
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should get a capa with its associated files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Create an associated file
      await db.insert(files).values({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        s3Key: 'test/test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'capa',
        entityId: createdCapaId,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      // Now get the CAPA by ID
      const capa = await caller.getById({ id: createdCapaId });

      // Verify CAPA data
      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.slug).toBeDefined();

      // Verify file data
      expect(capa.attachments).toBeDefined();
      expect(capa.attachments).toHaveLength(1);
      expect(capa.attachments[0]).toMatchObject({
        name: 'test.pdf',
        url: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        type: 'application/pdf',
        size: 1024,
      });
    });

    it('should allow admin to fetch private capa', async () => {
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);
      expect(capa.slug).toBeDefined();
    });

    it('should throw error for non-admin fetching private capa', async () => {
      const mockContext = createMockContext(mockTechnicianUser);

      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      await expect(caller.getById({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should throw error for non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);
      await expect(caller.getById({ id: 'non-existent-id' })).rejects.toThrow();
    });
  });

  describe('list', () => {
    it('should privateToAdmins CAPAs to a technician', async () => {
      await db.insert(capas).values({
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      await db.insert(capas).values({
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: false,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      const mockContext = createMockContext(mockTechnicianUser);

      const caller = capaRouter.createCaller(mockContext);
      const result = await caller.list({});

      expect(result.result.length).toBe(1);
    });

    it('should list capas with default pagination', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now list CAPAs
      const result = await caller.list({});

      expect(result).toBeDefined();
      expect(result.result).toBeDefined();
      expect(Array.isArray(result.result)).toBe(true);
      expect(result.result.length).toBeGreaterThan(0);
      expect(result.result[0].id).toBe(createdCapaId);

      // Verify user service was called
      expect(getUsersPublic).toHaveBeenCalledWith(
        expect.objectContaining({
          objectId: [mockUser.id],
        }),
      );
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should list capas with filters', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test with status filter
      const statusResult = await caller.list({
        status: [statusEnum.enumValues[0]],
      });
      expect(statusResult.result.every((capa) => capa.status === statusEnum.enumValues[0])).toBe(true);

      // Test with type filter
      const typeResult = await caller.list({
        type: [capaTypeEnum.enumValues[0]],
      });
      expect(typeResult.result.every((capa) => capa.type === capaTypeEnum.enumValues[0])).toBe(true);

      // Test with priority filter
      const priorityResult = await caller.list({
        priority: [capaPriorityEnum.enumValues[0]],
      });
      expect(priorityResult.result.every((capa) => capa.priority === capaPriorityEnum.enumValues[0])).toBe(true);

      // Verify user service was called for each filter
      expect(getUsersPublic).toHaveBeenCalledTimes(3);
      expect(hasPermission).toHaveBeenCalledTimes(3);
    });
  });

  describe('update', () => {
    it('should update a capa', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now update it
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA Title',
        status: statusEnum.enumValues[1],
        priority: capaPriorityEnum.enumValues[1],
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);
      expect(updatedCapa.title).toBe(updateInput.title);
      expect(updatedCapa.status).toBe(updateInput.status);
      expect(updatedCapa.priority).toBe(updateInput.priority);

      // Verify the update in the database
      const dbCapa = await db.query.capas.findFirst({
        where: eq(capas.id, createdCapaId),
      });

      expect(dbCapa).toBeDefined();
      expect(dbCapa?.title).toBe(updateInput.title);
      expect(dbCapa?.status).toBe(updateInput.status);
      expect(dbCapa?.priority).toBe(updateInput.priority);

      // Verify permission check was called
      expect(hasPermission).toHaveBeenCalled();

      // Verify queue job was added for notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            title: 'Updated CAPA Title',
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
          actionPrefix: 'Updated',
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-update-\\d+`)),
        }),
      );
    });

    it('should throw error when updating non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);
      await expect(
        caller.update({
          id: 'non-existent-id',
          title: 'Updated Title',
        }),
      ).rejects.toThrow();
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active capa', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Verify it's initially not archived
      const initialCapa = await caller.getById({ id: createdCapaId });
      expect(initialCapa?.archived).toBe(false);

      // Archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });

      expect(archivedResult).toBeDefined();
      expect(archivedResult?.id).toBe(createdCapaId);
      expect(archivedResult?.archived).toBe(true);

      // Verify queue job was added for archive notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            archived: true,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
          actionPrefix: 'Archived',
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-archive-\\d+`)),
        }),
      );
    });

    it('should unarchive an archived capa', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // First archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(archivedResult?.archived).toBe(true);

      // Clear previous mock calls
      vi.mocked(addJobToQueue).mockClear();

      // Then unarchive it
      const unarchivedResult = await caller.toggleArchive({ id: createdCapaId });

      expect(unarchivedResult).toBeDefined();
      expect(unarchivedResult?.id).toBe(createdCapaId);
      expect(unarchivedResult?.archived).toBe(false);

      // Verify queue job was added for unarchive notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            archived: false,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
          actionPrefix: 'Unarchived',
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-unarchive-\\d+`)),
        }),
      );
    });

    it('should preserve capa data when toggling archive status', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(archivedResult?.archived).toBe(true);

      // Verify other fields remain unchanged by getting the full CAPA
      const archivedCapa = await caller.getById({ id: createdCapaId });
      expect(archivedCapa?.title).toBe(mockCapaInput.title);
      expect(archivedCapa?.type).toBe(mockCapaInput.type);
      expect(archivedCapa?.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(archivedCapa?.ownerId).toBe(mockCapaInput.ownerId);
      expect(archivedCapa?.status).toBe(mockCapaInput.status);
      expect(archivedCapa?.priority).toBe(mockCapaInput.priority);

      // Unarchive and verify data is still preserved
      const unarchivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(unarchivedResult?.archived).toBe(false);

      const unarchivedCapa = await caller.getById({ id: createdCapaId });
      expect(unarchivedCapa?.title).toBe(mockCapaInput.title);
      expect(unarchivedCapa?.type).toBe(mockCapaInput.type);
      expect(unarchivedCapa?.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(unarchivedCapa?.ownerId).toBe(mockCapaInput.ownerId);
      expect(unarchivedCapa?.status).toBe(mockCapaInput.status);
      expect(unarchivedCapa?.priority).toBe(mockCapaInput.priority);
    });

    it('should handle toggle archive for capa with files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Add a file to the CAPA
      await db.insert(files).values({
        id: 'test-file-capa-archive',
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'capa-archive-test.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/capa-archive-test.pdf',
        s3Key: 'test/capa-archive-test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'capa',
        entityId: createdCapaId,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      // Archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(archivedResult?.archived).toBe(true);

      // Verify the CAPA can still be retrieved with its files
      const capaWithFiles = await caller.getById({ id: createdCapaId });
      expect(capaWithFiles?.attachments).toBeDefined();
      expect(capaWithFiles?.attachments?.length).toBe(1);
      expect(capaWithFiles?.archived).toBe(true);

      // Unarchive and verify files are still accessible
      const unarchivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(unarchivedResult?.archived).toBe(false);

      const finalCapa = await caller.getById({ id: createdCapaId });
      expect(finalCapa?.attachments?.length).toBe(1);
      expect(finalCapa?.archived).toBe(false);
    });

    it('should handle non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent CAPA
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should affect list results based on includeArchived filter', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Create two CAPAs
      const mockCapaInput1 = {
        title: 'Active CAPA',
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const mockCapaInput2 = {
        title: 'To Be Archived CAPA',
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const capa1 = await caller.create(mockCapaInput1);
      const capa2 = await caller.create(mockCapaInput2);

      // Archive one CAPA
      await caller.toggleArchive({ id: capa2!.id });

      // List without includeArchived should only show active CAPA
      const activeList = await caller.list({ includeArchived: false });
      expect(activeList.result.length).toBe(1);
      expect(activeList.result[0].title).toBe('Active CAPA');

      // List with includeArchived should show both CAPAs
      const allList = await caller.list({ includeArchived: true });
      expect(allList.result.length).toBe(2);

      const titles = allList.result.map((c) => c.title).sort();
      expect(titles).toEqual(['Active CAPA', 'To Be Archived CAPA']);
    });
  });
});
