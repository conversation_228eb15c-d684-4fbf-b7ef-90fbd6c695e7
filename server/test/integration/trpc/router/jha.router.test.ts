import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { auditTrail, jha, controlMeasures, hazards, jhaSteps, oshaLocations } from '@shared/schema';
import { jhaRouter } from '@server/trpc/router/jha.router';
import { hazardsRouter } from '@server/trpc/router/hazards.router';
import { controlMeasuresRouter } from '@server/trpc/router/control-measures.router';
import { CreateFullJhaSchema } from '@shared/jha.types';
import { z } from 'zod';
import { hasPermission } from '@server/services/user.service';
import { trpc } from '@server/trpc/trpc';

vi.mock('@server/services/user.service', () => ({
  hasPermission: vi.fn().mockReturnValue(true),
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
}));

let createdHazardId: string;
let createdControlMeasureId: string;

describe('jha router', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(auditTrail);
    await db.delete(jhaSteps);
    await db.delete(jha);
  });

  beforeAll(async () => {
    const hazardCaller = hazardsRouter.createCaller(mockContext);
    const controlMeasureCaller = controlMeasuresRouter.createCaller(mockContext);
    const createdHazard = await hazardCaller.create({
      name: 'Custom Test Hazard',
      type: 'physical' as const,
    });
    createdHazardId = createdHazard.id;
    const createdControlMeasure = await controlMeasureCaller.create({
      name: 'Custom Test Control Measure',
      type: 'personal_protective_equipment' as const,
    });
    createdControlMeasureId = createdControlMeasure.id;
  });

  afterAll(async () => {
    await db.delete(hazards);
    await db.delete(controlMeasures);
  });

  describe('create', () => {
    it('should create a new JHA with created hazards and control measures', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateFullJhaSchema> = {
        jha: {
          title: 'Test JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Inspect Area',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const result = await jhaCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^JHA-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));

      // Verify JHA was created
      const createdJha = await db.query.jha.findFirst({
        where: (j, { eq }) => eq(j.id, result!.id),
      });
      expect(createdJha).toBeDefined();
      expect(createdJha?.title).toBe(mockInput.jha.title);
      expect(createdJha?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(createdJha?.createdBy).toBe(mockUser.id);
      // Verify highest severity calculation (single step with severity 2)
      expect(createdJha?.highestSeverity).toBe(2);

      // Verify step was created with correct IDs
      const createdStep = await db.query.jhaSteps.findFirst({
        where: (s, { eq }) => eq(s.jhaId, result!.id),
      });
      expect(createdStep).toBeDefined();
      expect(createdStep?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(createdStep?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));

      // Verify the hazards and control measures used in the step exist
      expect(createdStep?.hazardIds).toHaveLength(1);
      expect(createdStep?.controlMeasureIds).toHaveLength(1);

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'jha'), eq(a.entityId, result!.id), eq(a.action, 'created')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();

      // Verify that our created entities are properly referenced
      expect(createdStep?.hazardIds).toContain(createdHazardId);
      expect(createdStep?.controlMeasureIds).toContain(createdControlMeasureId);
    });

    it('should create a new JHA and a create a revision after it is created', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateFullJhaSchema> = {
        jha: {
          title: 'Test JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Inspect Area',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const result = await jhaCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^JHA-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));
      expect(result?.version).toEqual('1.0');

      // Create revision with multiple steps and different severities
      const revision = await jhaCallerRouter.create({
        jha: {
          title: 'Test JHA Revision',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          instanceId: result?.instanceId,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Low Risk Task',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 2,
          },
          {
            serial: 2,
            title: 'Step 2: High Risk Task',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 5,
            likelihood: 4,
          },
          {
            serial: 3,
            title: 'Step 3: Medium Risk Task',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 3,
          },
        ],
      });

      expect(revision).toBeDefined();
      expect(revision?.id).not.toEqual(result?.id);
      expect(revision?.version).toEqual('2.0');
      expect(revision?.instanceId).toEqual(result?.instanceId);
      expect(revision?.slug).toMatch(/^JHA-\d{4}-2\.0$/);

      // Verify highest severity calculation in revision (should be 5 from multiple steps)
      const revisionJha = await db.query.jha.findFirst({
        where: (j, { eq }) => eq(j.id, revision!.id),
      });
      expect(revisionJha).toBeDefined();
      expect(revisionJha?.highestSeverity).toBe(5);
    });

    it('should create a new JHA with bulk created hazards and control measures within the same transaction', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateFullJhaSchema> = {
        jha: {
          title: 'Test JHA with Bulk Creation',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Handle Chemical Spill',
            hazardIds: [createdHazardId], // Use existing hazard
            controlMeasureIds: [createdControlMeasureId], // Use existing control measure
            hazardsToCreate: [
              {
                name: 'Chemical Exposure Risk',
                type: 'chemical' as const,
              },
              {
                name: 'Slip and Fall Risk',
                type: 'physical' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Chemical-resistant Gloves',
                type: 'personal_protective_equipment' as const,
              },
              {
                name: 'Emergency Evacuation Protocol',
                type: 'administrative_controls' as const,
              },
            ],
            severity: 4,
            likelihood: 3,
          },
          {
            serial: 2,
            title: 'Step 2: Clean Up Area',
            hazardsToCreate: [
              {
                name: 'Contaminated Surface Risk',
                type: 'environmental' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Proper Disposal Procedures',
                type: 'administrative_controls' as const,
              },
            ],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const result = await jhaCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^JHA-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));

      // Verify JHA was created
      const createdJha = await db.query.jha.findFirst({
        where: (j, { eq }) => eq(j.id, result!.id),
      });
      expect(createdJha).toBeDefined();
      expect(createdJha?.title).toBe(mockInput.jha.title);
      expect(createdJha?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(createdJha?.createdBy).toBe(mockUser.id);
      // Verify highest severity calculation (max severity from steps is 4)
      expect(createdJha?.highestSeverity).toBe(4);

      // Verify steps were created
      const createdSteps = await db.query.jhaSteps.findMany({
        where: (s, { eq }) => eq(s.jhaId, result!.id),
        orderBy: (s, { asc }) => [asc(s.serial)],
      });
      expect(createdSteps).toHaveLength(2);

      // Verify first step has both existing and newly created hazards/control measures
      const firstStep = createdSteps[0];
      expect(firstStep?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(firstStep?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));

      // Should have 3 hazards total: 1 existing + 2 created
      expect(firstStep?.hazardIds).toHaveLength(3);
      // Should have 3 control measures total: 1 existing + 2 created
      expect(firstStep?.controlMeasureIds).toHaveLength(3);

      // Verify second step has only newly created hazards/control measures
      const secondStep = createdSteps[1];
      expect(secondStep?.hazardIds).toHaveLength(1);
      expect(secondStep?.controlMeasureIds).toHaveLength(1);

      // Verify that the new hazards were actually created in the database
      const allHazards = await db.query.hazards.findMany({
        where: (h, { eq }) => eq(h.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newHazardNames = ['Chemical Exposure Risk', 'Slip and Fall Risk', 'Contaminated Surface Risk'];
      const createdHazardNames = allHazards.filter((h) => newHazardNames.includes(h.name)).map((h) => h.name);

      expect(createdHazardNames).toEqual(expect.arrayContaining(newHazardNames));

      // Verify that the new control measures were actually created in the database
      const allControlMeasures = await db.query.controlMeasures.findMany({
        where: (cm, { eq }) => eq(cm.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newControlMeasureNames = [
        'Chemical-resistant Gloves',
        'Emergency Evacuation Protocol',
        'Proper Disposal Procedures',
      ];
      const createdControlMeasureNames = allControlMeasures
        .filter((cm) => newControlMeasureNames.includes(cm.name))
        .map((cm) => cm.name);

      expect(createdControlMeasureNames).toEqual(expect.arrayContaining(newControlMeasureNames));

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'jha'), eq(a.entityId, result!.id), eq(a.action, 'created')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();

      // Verify the audit trail contains the processed JHA result (with mapped IDs)
      const auditDetails = JSON.parse(auditLog!.details!);
      expect(auditDetails.steps).toHaveLength(2);
      expect(auditDetails.steps[0].hazardIds).toHaveLength(3);
      expect(auditDetails.steps[0].controlMeasureIds).toHaveLength(3);
      expect(auditDetails.steps[1].hazardIds).toHaveLength(1);
      expect(auditDetails.steps[1].controlMeasureIds).toHaveLength(1);
    });
  });
});
