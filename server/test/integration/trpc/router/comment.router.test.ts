import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsersPublic, getUsers, hasPermission } from '@server/services/user.service';
import { commentRouter } from '@server/trpc/router/comment.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { capaPriorityEnum, capas, capaTypeEnum, comments, statusEnum } from '@shared/schema';
import { getCompany } from '@server/services/company.service';
import { createId } from '@paralleldrive/cuid2';
import { and, isNull, eq } from 'drizzle-orm';

// Mock the queue system
vi.mock('@server/queue/queue-utils', () => ({
  addJobToQueue: vi.fn(),
}));

vi.mock('@server/queue/job-names', () => ({
  QUEUE_JOB_NAMES: {
    COMMENT_MENTION_NOTIFICATION: 'comment-mention-notification',
  },
}));

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasEventPermission: vi.fn().mockReturnValue(true),
  canEditEvent: vi.fn().mockReturnValue(true),
  canExportEvent: vi.fn().mockReturnValue(true),
}));

vi.mock('@server/services/company.service', () => ({
  getCompany: vi.fn().mockResolvedValue({
    clientTimezone: 'America/New_York',
  }),
}));

// Mock the comment service
vi.mock('@server/services/comment.service', () => ({
  createComment: vi.fn(),
  fetchComments: vi.fn(),
  fetchCommentById: vi.fn(),
  deleteComment: vi.fn(),
}));

const createMockCapaInput = () => ({
  title: `Test CAPA ${Math.random().toString(36).substring(2, 8)}`,
  type: capaTypeEnum.enumValues[0],
  rcaFindings: 'Test findings',
  ownerId: mockUser.id,
  actionsToAddress: 'Test actions',
  status: statusEnum.enumValues[0],
  priority: capaPriorityEnum.enumValues[0],
  dueDate: new Date('2024-12-31'),
  upkeepCompanyId: mockUser.upkeepCompanyId,
  createdBy: mockUser.id,
});

describe('commentRouter', () => {
  const mockContext = createMockContext(mockUser);

  beforeEach(async () => {
    const { addJobToQueue } = await import('@server/queue/queue-utils');
    const { createComment, fetchComments, fetchCommentById, deleteComment } = await import(
      '@server/services/comment.service'
    );

    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [
        {
          id: mockUser.id,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          username: mockUser.email,
          email: mockUser.email,
        },
      ],
      nextCursor: undefined,
    });
    vi.mocked(getCompany).mockResolvedValue({
      clientTimezone: 'America/New_York',
    });
    vi.mocked(addJobToQueue).mockResolvedValue(undefined);

    // Set up default implementations for comment service mocks
    // These will be overridden in specific tests as needed
    vi.mocked(createComment).mockImplementation(async (input, user) => {
      const commentId = createId();

      // Actually insert the comment into the database for tests that need persistence
      const inserted = await db
        .insert(comments)
        .values({
          id: commentId,
          ...(input.entityType === 'event' ? { eventId: input.entityId } : { capaId: input.entityId }),
          content: input.content,
          userId: user.id,
          upkeepCompanyId: user.upkeepCompanyId,
        })
        .returning();

      const newComment = inserted[0];

      return {
        newComment,
        savedMentions: undefined,
      };
    });

    vi.mocked(fetchComments).mockImplementation(async ({ entityId, entityType }) => {
      // Return comments from database for these tests with proper GroupedComment structure
      const result = await db
        .select()
        .from(comments)
        .where(
          entityType === 'event'
            ? and(eq(comments.eventId, entityId), isNull(comments.capaId))
            : and(eq(comments.capaId, entityId), isNull(comments.eventId)),
        );

      return result.map((comment) => ({
        ...comment,
        mentions: [], // Empty mentions array for test simplicity
      }));
    });

    vi.mocked(fetchCommentById).mockImplementation(async ({ id }) => {
      const result = await db.select().from(comments).where(eq(comments.id, id));
      if (result.length === 0) {
        return null;
      }

      return {
        ...result[0],
        mentions: [], // Empty mentions array for test simplicity
      };
    });

    vi.mocked(deleteComment).mockImplementation(async ({ id }) => {
      await db.delete(comments).where(eq(comments.id, id));
      return { success: true, id };
    });
  });

  afterEach(async () => {
    // Clean up in the correct order to avoid foreign key constraint violations
    await db.delete(comments);
    await db.delete(capas);
  });

  describe('create', () => {
    it('should create a new comment and add queue job when mentions exist', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // Mock createComment for this specific test
      const { createComment } = await import('@server/services/comment.service');
      const originalImplementation = createComment;

      const commentId = createId();
      vi.mocked(createComment).mockImplementationOnce(async (input) => {
        const newComment = {
          id: commentId,
          content: input.content,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          userId: mockUser.id,
          createdAt: new Date(),
          updatedAt: new Date(),
          capaId: input.entityType === 'capa' ? input.entityId : null,
          eventId: input.entityType === 'event' ? input.entityId : null,
        };

        const savedMentions = [
          {
            id: createId(),
            createdAt: new Date(),
            userId: 'mentioned-user-123',
            commentId: commentId,
            position: input.content.indexOf('@'),
            mentionText: '@mentioned-user-123',
          },
        ];

        return {
          newComment,
          savedMentions,
        };
      });

      // Create a CAPA for this test
      const mockCapaInput = createMockCapaInput();
      const capa = await db.insert(capas).values(mockCapaInput).returning();
      const createdCapaId = capa[0].id;

      const mockCommentInput = {
        content: 'Test comment with @mention',
        entityType: 'capa' as const,
        entityId: createdCapaId,
        entitySlug: 'test-capa',
        entityTitle: 'Test CAPA Title',
        status: statusEnum.enumValues[0],
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);

      expect(comment).toBeDefined();
      expect(comment.content).toBe(mockCommentInput.content);

      // Verify queue job was added
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.COMMENT_MENTION_NOTIFICATION,
        expect.objectContaining({
          newComment: expect.objectContaining({
            content: mockCommentInput.content,
          }),
          savedMentions: expect.arrayContaining([
            expect.objectContaining({
              userId: 'mentioned-user-123',
              mentionText: '@mentioned-user-123',
            }),
          ]),
          user: mockUser,
          headers: mockContext.req.headers,
          input: mockCommentInput,
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(/^comment-.+-mention-\d+$/),
        }),
      );
    });

    it('should create a new comment without adding queue job when no mentions exist', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');

      // Mock createComment for this specific test
      const { createComment } = await import('@server/services/comment.service');

      const commentId = createId();
      vi.mocked(createComment).mockImplementationOnce(async (input) => {
        const newComment = {
          id: commentId,
          content: input.content,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          userId: mockUser.id,
          createdAt: new Date(),
          updatedAt: new Date(),
          capaId: input.entityType === 'capa' ? input.entityId : null,
          eventId: input.entityType === 'event' ? input.entityId : null,
        };

        return {
          newComment,
          savedMentions: undefined, // No mentions
        };
      });

      // Create a CAPA for this test
      const mockCapaInput = createMockCapaInput();
      const capa = await db.insert(capas).values(mockCapaInput).returning();
      const createdCapaId = capa[0].id;

      const mockCommentInput = {
        content: 'Test comment without mentions',
        entityType: 'capa' as const,
        entityId: createdCapaId,
        entitySlug: 'test-capa',
        entityTitle: 'Test CAPA Title',
        status: statusEnum.enumValues[0],
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);

      expect(comment).toBeDefined();
      expect(comment.content).toBe(mockCommentInput.content);

      // Verify no queue job was added
      expect(addJobToQueue).not.toHaveBeenCalled();
    });

    it('should create a new comment and work with database operations', async () => {
      // This test uses the real createComment service for database operations
      const mockCapaInput = createMockCapaInput();
      const capa = await db.insert(capas).values(mockCapaInput).returning();
      const createdCapaId = capa[0].id;

      const mockCommentInput = {
        content: 'Test comment for database operations',
        entityType: 'capa' as const,
        entityId: createdCapaId,
        entitySlug: 'test-capa',
        entityTitle: 'Test CAPA Title',
        status: statusEnum.enumValues[0],
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);

      expect(comment).toBeDefined();
      expect(comment.content).toBe(mockCommentInput.content);
      expect(comment.id).toMatch(/^[a-z0-9]+$/); // CUID2 format
    });
  });

  describe('list', () => {
    it('should list comments', async () => {
      // Create a CAPA for this test
      const mockCapaInput = createMockCapaInput();
      const capa = await db.insert(capas).values(mockCapaInput).returning();
      const createdCapaId = capa[0].id;

      const mockCommentInput = {
        content: 'Test comment for list',
        entityType: 'capa' as const,
        entityId: createdCapaId,
        entitySlug: 'test-capa',
        entityTitle: 'Test CAPA Title',
        status: statusEnum.enumValues[0],
      };

      const caller = commentRouter.createCaller(mockContext);
      await caller.create(mockCommentInput);
      const commentsList = await caller.list({ entityId: createdCapaId, entityType: 'capa' });

      expect(commentsList).toBeDefined();
      expect(commentsList.length).toBe(1);
      expect(commentsList[0].content).toBe(mockCommentInput.content);
    });
  });

  describe('getById', () => {
    it('should get a comment by id', async () => {
      // Create a CAPA for this test
      const mockCapaInput = createMockCapaInput();
      const capa = await db.insert(capas).values(mockCapaInput).returning();
      const createdCapaId = capa[0].id;

      const mockCommentInput = {
        content: 'Test comment for get',
        entityType: 'capa' as const,
        entityId: createdCapaId,
        entitySlug: 'test-capa',
        entityTitle: 'Test CAPA Title',
        status: statusEnum.enumValues[0],
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);
      const commentById = await caller.getById({ id: comment.id });

      expect(commentById).toBeDefined();
      expect(commentById?.id).toBe(comment.id);
      expect(commentById?.content).toBe(comment.content);
    });
  });

  describe('delete', () => {
    it('should delete a comment', async () => {
      // Create a CAPA for this test
      const mockCapaInput = createMockCapaInput();
      const capa = await db.insert(capas).values(mockCapaInput).returning();
      const createdCapaId = capa[0].id;

      const mockCommentInput = {
        content: 'Test comment for delete',
        entityType: 'capa' as const,
        entityId: createdCapaId,
        entitySlug: 'test-capa',
        entityTitle: 'Test CAPA Title',
        status: statusEnum.enumValues[0],
      };

      const caller = commentRouter.createCaller(mockContext);
      const comment = await caller.create(mockCommentInput);
      await caller.delete({ id: comment.id });
      const commentById = await caller.getById({ id: comment.id });

      expect(commentById).toBeNull();
    });
  });
});
