import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { accessPointRouter } from '@server/trpc/router/access-point.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { accessPoints } from '@shared/schema';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUserPublic, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';
import { getLocations, getAllLocations, searchLocationsPublic } from '@server/services/location.service';
import { mockLocations } from '@server/test/fixtures/location';
import { matchLocationsWithAI } from '@server/services/ai.service';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasEventPermission: vi.fn().mockReturnValue(true),
  canEditEvent: vi.fn().mockReturnValue(true),
  canExportEvent: vi.fn().mockReturnValue(true),
}));

vi.mock('@server/services/location.service', () => ({
  getLocations: vi.fn(),
  getAllLocations: vi.fn(),
  searchLocationsPublic: vi.fn(),
}));

vi.mock('@server/services/ai.service', () => ({
  matchLocationsWithAI: vi.fn(),
}));

describe('accessPointRouter', () => {
  const mockContext = createMockContext(mockUser);

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [mockUser],
      nextCursor: undefined,
    });
    vi.mocked(getUserPublic).mockResolvedValue(mockUser);
    vi.mocked(getLocations).mockResolvedValue({
      noResults: false,
      result: mockLocations,
      nextCursor: undefined,
    });
    vi.mocked(getAllLocations).mockResolvedValue(mockLocations);
    vi.mocked(searchLocationsPublic).mockResolvedValue({
      noResults: false,
      nextCursor: undefined,
      result: mockLocations,
    });
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(accessPoints);
  });

  describe('create', () => {
    it('should create a new access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
        status: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify the access point was created
      expect(accessPoint).toBeDefined();
      expect(accessPoint.name).toBe(mockAccessPointInput.name);
    });

    it('should validate required fields', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Test missing required fields
      await expect(caller.create({} as any)).rejects.toThrow();
    });
  });

  describe('update', () => {
    it('should update an access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      const updatedAccessPointInput = {
        id: accessPoint.id,
        name: 'Updated Access Point',
        description: 'Updated description',
        locationId: '456',
        type: '456',
      };

      const updatedAccessPoint = await caller.update(updatedAccessPointInput);

      expect(updatedAccessPoint).toBeDefined();
      expect(updatedAccessPoint.id).toBe(accessPoint.id);
      expect(updatedAccessPoint.name).toBe(updatedAccessPointInput.name);
      expect(updatedAccessPoint.description).toBe(updatedAccessPointInput.description);
      expect(updatedAccessPoint.locationId).toBe(updatedAccessPointInput.locationId);
    });
  });

  describe('list', () => {
    it('should list access points', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: 'abc123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const accessPoints = await caller.list({});

      expect(accessPoints).toBeDefined();

      expect(accessPoints.result.length).toBe(1);
    });
  });

  describe('getById', () => {
    it('should get an access point by id', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      const accessPointById = await caller.getById({ id: accessPoint.id });

      expect(accessPointById).toBeDefined();

      expect(accessPointById.id).toBe(accessPoint.id);
      expect(accessPointById.name).toBe(mockAccessPointInput.name);
    });
  });

  describe('bulkCreate', () => {
    it('should create access points in bulk with AI fallback', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      const mockInput = [
        {
          name: 'Test AP 1',
          location: 'Kamino Sector', // Should match directly
          type: 'type-1',
          status: 'active',
        },
        {
          name: 'Test AP 2',
          location: 'Unknown Planet', // Should fallback to AI
          type: 'type-2',
          status: 'inactive',
        },
      ];

      // Mock AI response for fallback case
      vi.mocked(matchLocationsWithAI).mockImplementation(async (locationName) => {
        if (locationName === 'Unknown Planet') {
          return {
            locationId: 'ai-matched',
            locationName: 'AI Location',
          };
        }
        return { locationId: '', locationName: '' };
      });

      const result = await caller.bulkCreate(mockInput);

      expect(result.success).toBe(true);
      expect(result.created).toBe(2);
      expect(result.failed).toBe(0);
      expect(result.failedItems).toEqual([]);
    });

    it('should return failed items if AI also fails', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      const mockInput = [
        {
          name: 'Test AP Failed',
          location: 'Totally Unknown Planet',
          type: 'type-x',
          status: 'archived',
        },
      ];

      vi.mocked(matchLocationsWithAI).mockResolvedValue({
        locationId: '',
        locationName: '',
      });

      const result = await caller.bulkCreate(mockInput);

      expect(result.success).toBe(true);
      expect(result.created).toBe(0);
      expect(result.failed).toBe(1);
      expect(result.failedItems[0].location).toBe('Totally Unknown Planet');
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify it's initially not archived
      expect(accessPoint.archivedAt).toBeNull();

      // Archive the access point
      const archivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });

      expect(archivedAccessPoint).toBeDefined();
      expect(archivedAccessPoint.id).toBe(accessPoint.id);
      expect(archivedAccessPoint.archivedAt).toBeDefined();
      expect(archivedAccessPoint.archivedAt).not.toBeNull();
    });

    it('should unarchive an archived access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // First archive the access point
      const archivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });
      expect(archivedAccessPoint.archivedAt).toBeDefined();

      // Then unarchive it
      const unarchivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });

      expect(unarchivedAccessPoint).toBeDefined();
      expect(unarchivedAccessPoint.id).toBe(accessPoint.id);
      expect(unarchivedAccessPoint.archivedAt).toBeNull();
    });

    it('should toggle status correctly when archiving and unarchiving', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Get the initial access point to check its status
      const initialAccessPoint = await caller.getById({ id: accessPoint.id });
      const initialStatus = initialAccessPoint.status;

      // Archive the access point - status should change
      const archivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });
      expect(archivedAccessPoint.archivedAt).toBeDefined();

      // Get the archived access point to verify status change
      const archivedDetails = await caller.getById({ id: accessPoint.id });
      expect(archivedDetails.status).not.toBe(initialStatus);

      // Unarchive the access point - status should revert
      const unarchivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });
      expect(unarchivedAccessPoint.archivedAt).toBeNull();

      // Get the unarchived access point to verify status reverted
      const unarchivedDetails = await caller.getById({ id: accessPoint.id });
      expect(unarchivedDetails.status).toBe(initialStatus);
    });

    it('should handle non-existent access point', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent access point
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });
  });
});
