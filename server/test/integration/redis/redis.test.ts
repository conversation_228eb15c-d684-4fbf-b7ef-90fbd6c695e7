import { clearCacheByPattern, getCache, setCache } from '@server/redis/cache';
import { redisClient, redisClient as redisClient2 } from '@server/redis/client';
import { describe, it, expect, afterEach } from 'vitest';

const connectionName = redisClient.options.connectionName;

describe('Redis', () => {
  afterEach(async () => {
    await clearCacheByPattern('test:*');
  });

  it('should use the same instance', async () => {
    expect(redisClient).toBe(redisClient2);

    expect(redisClient.options.connectionName).toBe(connectionName);
    expect(redisClient2.options.connectionName).toBe(connectionName);
  });

  it('should set and get a string value', async () => {
    await setCache('test:string', 'test');
    const test = await getCache('test:string');
    expect(test).toBe('test');
    expect(redisClient.options.connectionName).toBe(connectionName);
  });

  it('should set and get a number value', async () => {
    await setCache('test:number', 1);
    const test = await getCache('test:number');
    expect(test).toBe(1);
    expect(redisClient.options.connectionName).toBe(connectionName);
  });

  it('should set and get a boolean value', async () => {
    await setCache('test:boolean', true);
    const test = await getCache('test:boolean');
    expect(test).toBe(true);
    expect(redisClient.options.connectionName).toBe(connectionName);
  });

  it('should set and get an object value', async () => {
    await setCache('test:object', { test: 'test' });
    const test = await getCache<{ test: string }>('test:object');
    expect(test).toEqual({ test: 'test' });
    expect(redisClient.options.connectionName).toBe(connectionName);
  });
});
