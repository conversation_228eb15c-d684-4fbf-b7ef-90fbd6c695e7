import { PaginatedResponse } from '@shared/schema.types';

export interface PaginationParams {
  cursor: number;
  limit: number;
}

export function calculateOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}

export function createPaginatedResponse<T>(data: T[], { cursor, limit }: PaginationParams): PaginatedResponse<T> {
  return {
    noResults: data.length === 0,
    result: data,
    nextCursor: data.length < limit ? undefined : cursor + data.length,
  };
}
