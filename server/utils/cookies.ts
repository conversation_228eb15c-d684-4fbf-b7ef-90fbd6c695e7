/**
 * Extracts a specific cookie value from a cookie string
 * @param cookieString - The full cookie string (e.g., from headers.cookie)
 * @param cookieKey - The key of the cookie to extract (e.g., 'upkeepsess_development')
 * @returns The decoded cookie value or undefined if not found
 */
export const extractCookieValue = (cookieString: string | undefined, cookieKey: string): string | undefined => {
  if (!cookieString || !cookieKey) {
    return undefined;
  }

  // Split cookies by semicolon and space
  const cookies = cookieString.split('; ');

  // Find the cookie with the matching key
  for (const cookie of cookies) {
    const [key, value] = cookie.split('=');
    if (key === cookieKey && value) {
      // Decode the URL-encoded value
      return decodeURIComponent(value);
    }
  }

  return undefined;
};

/**
 * Parses all cookies from a cookie string into a key-value object
 * @param cookieString - The full cookie string (e.g., from headers.cookie)
 * @returns Object with cookie keys and decoded values
 */
export const parseCookies = (cookieString: string): Record<string, string> => {
  if (!cookieString) {
    return {};
  }

  const cookies: Record<string, string> = {};
  const cookieArray = cookieString.split('; ');

  for (const cookie of cookieArray) {
    const [key, value] = cookie.split('=');
    if (key && value !== undefined) {
      cookies[key] = decodeURIComponent(value);
    }
  }

  return cookies;
};
