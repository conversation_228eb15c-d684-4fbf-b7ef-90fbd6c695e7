import { Request } from 'express';

/**
 * Extracts the client IP address from an Express request object.
 * Checks various headers in order of reliability:
 * 1. X-Forwarded-For (when behind a proxy/load balancer)
 * 2. X-Real-IP
 * 3. Request's remote address
 *
 * @param req Express Request object
 * @returns The client IP address as a string
 */
export const getIpAddress = (req: Request): string => {
  // Check X-Forwarded-For header
  const forwardedFor = req.headers['x-forwarded-for'];
  if (forwardedFor) {
    // Get the first IP if multiple are present (closest to the client)
    const ips = Array.isArray(forwardedFor) ? forwardedFor[0].split(',')[0] : forwardedFor.split(',')[0];
    return ips.trim();
  }

  // Check X-Real-IP header
  const realIp = req.headers['x-real-ip'];
  if (realIp) {
    return Array.isArray(realIp) ? realIp[0] : realIp;
  }

  // Fallback to remote address
  return req.socket?.remoteAddress || '0.0.0.0';
};
