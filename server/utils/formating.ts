export const formatNoteWithBreakLines = (text: string): string => {
  // Convert various line break formats to <br />
  return text
    .replace(/\r\n/g, '<br />') // Windows line breaks
    .replace(/\r/g, '<br />') // Mac line breaks
    .replace(/\n/g, '<br />'); // Unix line breaks
};

export const pluralize = (count: number, singular: string, plural?: string) => {
  return `${count} ${count === 1 ? singular : plural || singular + 's'}`;
};
