import { describe, it, expect } from 'vitest';
import { extractCookieValue, parseCookies } from '../cookies';

describe('Cookie utilities', () => {
  const sampleCookieString =
    'intercom-device-id-c8ise6cp=64955acb-880d-44cc-babb-df3e9426f82c; OptanonAlertBoxClosed=2025-05-20T13:19:08.365Z; _afid=1089e6d6-497a-48fd-b92d-7b4ccfb920f9; aid=1089e6d6-497a-48fd-b92d-7b4ccfb920f9; ajs_group_id=null; ajs_anonymous_id=%224f199adb-5c28-4585-8f05-ec3d5b9a15d7%22; __ssid=eaeb212e8e95e48005ea01ad67c18fe; ajs_user_id=%22118458277%22; _tt_enable_cookie=1; _ttp=01JW9KTRKCZMDNRDSG3GPQ39Y4_.tt.1; intercom-id-c8ise6cp=b66c3fff-c727-4572-b8af-9dd2903b0cdc; _hp2_props.318805607=%7B%22template_name%22%3A%22course_sales_page%22%7D; cf_clearance=uIguNIraKiD_XCdumzRyFxcUyqggtF2Ox6ON0lIyNaU-1749505544-*******-xTq2SrcDActWQLops7ycVChnKaMEB5EuRAWMDgudTG0JcGKlCM6.nZWo8Nj.1n6QTtLhuxLd.sLZgENcbjB9HZb4w4JR.rmP4sSKZvjyUBfizMBv.bXDQiPEbB_q3nHjNE1U75M.I8PpBEEPkJ2VneUoHTVeNpvaaTDLFpjWaVC5K2.OijSOylwIrmA_Enc7EnWUlX1LdPQny.4q75qQbUiadydgbnEF73S2vKtrUX9jQySETvomReIV.4Usf.mlqgRADr3u8OkpgTezDFjwBnXwjmqsxAAUOXnm4KzrABiLWW1qGFSkoUVT6Aw2GiwbORpRR9zbAxJKTJaoh4ykV1ShmmsCuUb5mLLs502VGzk; _hp2_id.318805607=%7B%22userId%22%3A%227495314921599402%22%2C%22pageviewId%22%3A%227506644426978416%22%2C%22sessionId%22%3A%225610150509610186%22%2C%22identity%22%3A%22118458277%22%2C%22trackerVersion%22%3A%224.0%22%2C%22identityField%22%3Anull%2C%22isIdentified%22%3A1%7D; _ga_SL8LSCXHSV=GS2.1.s1749560856$o9$g0$t1749560856$j60$l0$h0; language=en; upkeepsess=r%3A1548f139984492d07bb05c3278e29cf9; upkeepsess_dev07=r%3A16e84bb65d1a7dbf1fdbb0042a02ed04; upkeepsess_development=r%3A26559631698d65d5293988e33f0d33c1; _ga=GA1.1.1953228957.1747746175; _ga_VZVP5K7LW1=GS2.1.s1752793650$o30$g1$t1752793651$j59$l0$h0; _gcl_au=1.1.259241657.1747836897.1080522115.1752864186.1752864549; upkeepsess_staging3=r%3A620562e5de3be2d1ac410862d5c63eb4; mp_fc42a5c2b0b52a5908a92d21471634d1_mixpanel=%7B%22distinct_id%22%3A%20%2234sw7zjaHV%22%2C%22%24device_id%22%3A%20%221980a6e5a4216e7-043fba80a16b0b-18525636-1fa400-1980a6e5a4216e7%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fapp.onupkeep.com%2Fweb%2Fwork-orders%2Flist%3Fstatus%3Dopen%252CinProgress%252ConHold%26archivedDisplay%3DhideArchived%22%2C%22%24initial_referring_domain%22%3A%20%22app.onupkeep.com%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fapp.onupkeep.com%2Fweb%2Fwork-orders%2Flist%3Fstatus%3Dopen%252CinProgress%252ConHold%26archivedDisplay%3DhideArchived%22%2C%22%24initial_referring_domain%22%3A%20%22app.onupkeep.com%22%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22%24user_id%22%3A%20%2234sw7zjaHV%22%2C%22UserRole%22%3A%20%22Admin%22%2C%22Email%22%3A%20%22gustavo.reichelt.c%40upkeep.com%22%2C%22CompanyName%22%3A%20%22Gustavo%20Handyman%22%7D; _rdt_uuid=1748374479333.6140026e-6489-4e44-87cf-8180c89f95e5; _ga_4XCTC3D1W0=GS2.1.s1753123717$o35$g0$t1753123717$j60$l0$h130827398; OptanonConsent=isGpcEnabled=0&datestamp=Mon+Jul+21+2025+15%3A48%3A38+GMT-0300+(Brasilia+Standard+Time)&version=202211.2.0&isIABGlobal=false&hosts=&landingPath=NotLandingPage&groups=C0001%3A1%2CC0002%3A1%2CC0003%3A1%2CC0004%3A1&AwaitingReconsent=false&geolocation=BR%3BRS; ttcsid_COG0MOBC77UC70DIP9R0=1753123717132::YryzaFiXjKnQ1lT6YXYC.56.1753123719165; ttcsid=1753123717133::9qdHNbRXmvXcSQBcvfRu.56.1753123719165; _ga_ETY4MMFGJP=GS2.1.s1753123717$o119$g1$t1753123720$j57$l0$h0';

  describe('extractCookieValue', () => {
    it('should extract the correct cookie value for upkeepsess_development', () => {
      const result = extractCookieValue(sampleCookieString, 'upkeepsess_development');
      expect(result).toBe('r:26559631698d65d5293988e33f0d33c1');
    });

    it('should extract the correct cookie value for upkeepsess_dev07', () => {
      const result = extractCookieValue(sampleCookieString, 'upkeepsess_dev07');
      expect(result).toBe('r:16e84bb65d1a7dbf1fdbb0042a02ed04');
    });

    it('should extract the correct cookie value for upkeepsess_staging3', () => {
      const result = extractCookieValue(sampleCookieString, 'upkeepsess_staging3');
      expect(result).toBe('r:620562e5de3be2d1ac410862d5c63eb4');
    });

    it('should return undefined for non-existent cookie', () => {
      const result = extractCookieValue(sampleCookieString, 'non_existent_cookie');
      expect(result).toBeUndefined();
    });

    it('should return undefined for empty cookie string', () => {
      const result = extractCookieValue('', 'upkeepsess_development');
      expect(result).toBeUndefined();
    });

    it('should return undefined for empty cookie key', () => {
      const result = extractCookieValue(sampleCookieString, '');
      expect(result).toBeUndefined();
    });

    it('should decode URL-encoded values correctly', () => {
      const result = extractCookieValue(sampleCookieString, 'ajs_anonymous_id');
      expect(result).toBe('"4f199adb-5c28-4585-8f05-ec3d5b9a15d7"');
    });
  });

  describe('parseCookies', () => {
    it('should parse all cookies into an object', () => {
      const result = parseCookies(sampleCookieString);

      expect(result['upkeepsess_development']).toBe('r:26559631698d65d5293988e33f0d33c1');
      expect(result['upkeepsess_dev07']).toBe('r:16e84bb65d1a7dbf1fdbb0042a02ed04');
      expect(result['language']).toBe('en');
      expect(result['ajs_user_id']).toBe('"118458277"');
    });

    it('should return empty object for empty cookie string', () => {
      const result = parseCookies('');
      expect(result).toEqual({});
    });

    it('should handle cookies with no value', () => {
      const cookieString = 'valid_cookie=value; empty_cookie=; another_cookie=another_value';
      const result = parseCookies(cookieString);

      expect(result['valid_cookie']).toBe('value');
      expect(result['empty_cookie']).toBe('');
      expect(result['another_cookie']).toBe('another_value');
    });
  });
});
