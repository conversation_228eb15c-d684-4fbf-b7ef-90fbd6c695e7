import { describe, it, expect } from 'vitest';
import {
  matchSingleLocation,
  NO_LOCATION_MATCHED,
  MORE_THAN_ONE_LOCATION_MATCHED,
} from '@server/utils/match-single-location';
import { prepareLocationContext } from '@server/utils/prepare-location-context';

const makeInput = (name: string, location: string) => ({ name, location });

describe('matchSingleLocation', () => {
  const baseCandidates = [
    { id: '1', name: 'Kamino Sector' },
    { id: '2', name: 'Vault 42' },
    { id: '3', name: 'Rust Town Foundry' },
  ];

  it('should match exact name', () => {
    const context = prepareLocationContext(baseCandidates);
    const input = makeInput('My Device', 'Kamino Sector');

    const result = matchSingleLocation(input, context);

    expect(result).toEqual({
      name: 'My Device',
      location: 'Kamino Sector',
      locationId: '1',
      locationName: 'Kamino Sector',
    });
  });

  it('should return "more than one location matched" if name is duplicated', () => {
    const context = prepareLocationContext([...baseCandidates, { id: '4', name: 'Vault 42' }]);

    const input = makeInput('Sensor', 'Vault 42');

    const result = matchSingleLocation(input, context);
    expect(result).toEqual({
      name: 'Sensor',
      location: 'Vault 42',
      reason: MORE_THAN_ONE_LOCATION_MATCHED,
    });
  });

  it('should return "no location matched" if no candidate is close enough', () => {
    const context = prepareLocationContext(baseCandidates);
    const input = makeInput('Unknown', 'Hoth Planet Base');

    const result = matchSingleLocation(input, context);
    expect(result).toEqual({
      name: 'Unknown',
      location: 'Hoth Planet Base',
      reason: NO_LOCATION_MATCHED,
    });
  });

  it('should return the location with the lowest distance', () => {
    const context = prepareLocationContext([
      { id: '1', name: 'Kamino Sector' }, // 2 distance
      { id: '2', name: 'Kamino Sektor' }, // 1 distance
    ]);

    const input = makeInput('My Device', 'Kamino Sektr');

    const result = matchSingleLocation(input, context);
    expect(result).toEqual({
      name: 'My Device',
      location: 'Kamino Sektr',
      locationName: 'Kamino Sektor',
      locationId: '2',
    });
  });

  it('should return a fuzzy match if distance is within threshold', () => {
    const context = prepareLocationContext(baseCandidates);
    const input = makeInput('Device', 'kamino secter'); // typo

    const result = matchSingleLocation(input, context);
    expect(result).toEqual({
      name: 'Device',
      location: 'kamino secter',
      locationId: '1',
      locationName: 'Kamino Sector',
    });
  });

  it('should return "more than one location matched" if multiple candidates have the same distance', () => {
    const context = prepareLocationContext([
      { id: '1', name: 'Base One' },
      { id: '2', name: 'Base Two' },
    ]);

    const input = makeInput('Sensor', 'Base o');

    const result = matchSingleLocation(input, context);

    expect(result).toEqual({
      name: 'Sensor',
      location: 'Base o',
      reason: MORE_THAN_ONE_LOCATION_MATCHED,
    });
  });

  it('should return "no location matched" if fuzzy distance is too high', () => {
    const context = prepareLocationContext(baseCandidates);
    const input = makeInput('Scanner', 'Coruscant Outpost');

    const result = matchSingleLocation(input, context, 2); // stricter threshold
    expect(result).toEqual({
      name: 'Scanner',
      location: 'Coruscant Outpost',
      reason: NO_LOCATION_MATCHED,
    });
  });
});
