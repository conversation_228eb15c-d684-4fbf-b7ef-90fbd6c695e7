import { describe, it, expect } from 'vitest';
import { getIpAddress } from '../get-ip-address';
import type { Request } from 'express';

describe('getIpAddress', () => {
  // Helper to create a mock request object
  const createMockRequest = (headers: Record<string, string | string[]> = {}, remoteAddress?: string): Request => {
    return {
      headers,
      socket: {
        remoteAddress,
      },
    } as Request;
  };

  it('should extract IP from X-Forwarded-For header (single IP)', () => {
    const req = createMockRequest({
      'x-forwarded-for': '***********',
    });
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should extract first IP from X-Forwarded-For header (multiple IPs)', () => {
    const req = createMockRequest({
      'x-forwarded-for': '***********, ********, **********',
    });
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should handle X-Forwarded-For header as array', () => {
    const req = createMockRequest({
      'x-forwarded-for': ['***********, ********'],
    });
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should extract IP from X-Real-IP header', () => {
    const req = createMockRequest({
      'x-real-ip': '***********',
    });
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should handle X-Real-IP header as array', () => {
    const req = createMockRequest({
      'x-real-ip': ['***********'],
    });
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should use remote address if no proxy headers present', () => {
    const req = createMockRequest({}, '***********');
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should return fallback IP if no IP address can be determined', () => {
    const req = createMockRequest();
    expect(getIpAddress(req)).toBe('0.0.0.0');
  });

  it('should prefer X-Forwarded-For over X-Real-IP', () => {
    const req = createMockRequest({
      'x-forwarded-for': '***********',
      'x-real-ip': '***********',
    });
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should prefer X-Real-IP over remote address', () => {
    const req = createMockRequest(
      {
        'x-real-ip': '***********',
      },
      '***********',
    );
    expect(getIpAddress(req)).toBe('***********');
  });

  it('should handle whitespace in X-Forwarded-For header', () => {
    const req = createMockRequest({
      'x-forwarded-for': '  ***********  ,  ********  ',
    });
    expect(getIpAddress(req)).toBe('***********');
  });
});
