import { describe, it, expect } from 'vitest';
import { matchManyLocations, createInputKey } from '@server/utils/match-many-locations';
import { prepareLocationContext } from '@server/utils/prepare-location-context';

const makeInput = (name: string, location: string) => ({ name, location });

describe('matchManyLocations', () => {
  const candidates = [
    { id: '1', name: 'Kamino Sector' },
    { id: '2', name: 'Vault 42' },
    { id: '3', name: 'Jedha Base' },
  ];
  const context = prepareLocationContext(candidates);

  it('should match all locations', () => {
    const inputs = [makeInput('Sensor A', 'Kamino Sector'), makeInput('Sensor B', 'Vault 42')];

    const result = matchManyLocations(inputs, context);

    expect(result.successMap.size).toBe(2);
    expect(result.failMap.size).toBe(0);
    expect(result.unresolved.length).toBe(0);
  });

  it('should split matched and unmatched', () => {
    const inputs = [makeInput('Sensor A', 'Kamino Sector'), makeInput('Sensor B', 'Unknown Base')];

    const result = matchManyLocations(inputs, context);

    expect(result.successMap.size).toBe(1);
    expect(result.failMap.size).toBe(1);
    expect(result.unresolved.length).toBe(1);
  });

  it('should handle all unmatched', () => {
    const inputs = [makeInput('Sensor A', 'Ghost Town'), makeInput('Sensor B', 'Planet Z')];

    const result = matchManyLocations(inputs, context);

    expect(result.successMap.size).toBe(0);
    expect(result.failMap.size).toBe(2);
    expect(result.unresolved.length).toBe(2);
  });

  it('should overwrite duplicate keys (last one wins)', () => {
    const inputs = [
      makeInput('Sensor A', 'Kamino Sector'),
      makeInput('Sensor A', 'Kamino Sector'), // same key
    ];

    const result = matchManyLocations(inputs, context);
    expect(result.successMap.size).toBe(1); // only one survives
  });

  it('createInputKey should lowercase and trim properly', () => {
    const input = makeInput('  MySensor  ', '  Jedha Base  ');
    const key = createInputKey(input);
    expect(key).toBe('mysensor::jedha base');
  });
});
