// api.ts
import axios from 'axios';
import axiosRetry from 'axios-retry';
import { env } from '../../env';
import { Request } from 'express';

const api = axios.create({
  baseURL: env.UPKEEP_API_URL,
  withCredentials: true, // this applies globally
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosRetry(api, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: (error) => {
    return axiosRetry.isNetworkOrIdempotentRequestError(error) || (error.response?.status ?? 0) >= 500;
  },
});

export const buildDefaultHeaders = (headers: Request['headers']) => {
  return {
    'Content-Type': 'application/json',
    // Forward headers
    'x-user-token': headers['x-user-token'] as string,
    Cookie: headers.cookie,
  };
};

export type Headers = Request['headers'];

export default api;
