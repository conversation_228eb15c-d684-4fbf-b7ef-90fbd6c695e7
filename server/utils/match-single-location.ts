import { distance } from 'fastest-levenshtein';
import type { prepareLocationContext } from '@server/utils/prepare-location-context';
import { BulkCreateAccessPointInput } from '@shared/schema.types';

export const NO_LOCATION_MATCHED = 'no location matched';
export const MORE_THAN_ONE_LOCATION_MATCHED = 'more than one location matched';

export type MatchSuccess = {
  name: string;
  location: string;
  locationId: string;
  locationName: string;
};

export type MatchFail = {
  name: string;
  location: string;
  reason: string;
};

export function matchSingleLocation(
  input: BulkCreateAccessPointInput,
  context: ReturnType<typeof prepareLocationContext>,
  threshold = 3,
): MatchSuccess | MatchFail {
  const normalizedInput = input.location.trim().toLowerCase();

  // 1. Direct match
  if (context.searchableSet.has(normalizedInput)) {
    const match = context.canonicalMap.get(normalizedInput)!;

    if (match.length === 1) {
      return {
        name: input.name,
        location: input.location,
        locationId: match[0].id,
        locationName: match[0].name,
      };
    } else {
      return {
        name: input.name,
        location: input.location,
        reason: MORE_THAN_ONE_LOCATION_MATCHED,
      };
    }
  }

  // 2. Fuzzy match
  const distances = context.fuzzyList.map((candidate) => ({
    candidate,
    dist: distance(normalizedInput, candidate.normalizedName),
  }));

  const sorted = distances.sort((a, b) => a.dist - b.dist);
  const best = sorted[0];

  if (!best || best.dist > threshold) {
    return {
      name: input.name,
      location: input.location,
      reason: NO_LOCATION_MATCHED,
    };
  }

  const sameScore = sorted.filter((x) => x.dist === best.dist);
  if (sameScore.length > 1) {
    return {
      name: input.name,
      location: input.location,
      reason: MORE_THAN_ONE_LOCATION_MATCHED,
    };
  }

  // 3. Single best fuzzy match
  return {
    name: input.name,
    location: input.location,
    locationId: best.candidate.id,
    locationName: best.candidate.name,
  };
}
