-- clean up global hazards and control measures
TRUNCATE TABLE "jha_hazards";
TRUNCATE TABLE "jha_control_measures";

CREATE TYPE "public"."control_measure_category" AS ENUM('engineering_controls', 'administrative_controls', 'personal_protective_equipment', 'specialized_advanced_controls', 'other');--> statement-breakpoint
CREATE TYPE "public"."hazard_category" AS ENUM('chemical', 'electrical', 'physical', 'environmental', 'ergonomic', 'fall', 'biological', 'fire', 'mechanical', 'radiation', 'noise', 'thermal', 'atmospheric', 'spill', 'transportation', 'violence', 'other');--> statement-breakpoint
ALTER TABLE "events" ALTER COLUMN "category" SET DATA TYPE "public"."hazard_category" USING "category"::text::"public"."hazard_category";--> statement-breakpoint
ALTER TABLE "jha_control_measures" ADD COLUMN "type" "control_measure_category" NOT NULL;--> statement-breakpoint
ALTER TABLE "jha_hazards" ADD COLUMN "type" "hazard_category" NOT NULL;--> statement-breakpoint
DROP TYPE "public"."event_category";