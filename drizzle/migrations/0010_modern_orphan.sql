ALTER TYPE "public"."primary_recordable_outcome" RENAME TO "osha_type";--> statement-breakpoint
ALTER TABLE "osha_reports" RENAME COLUMN "primary_recordable_outcome" TO "type";--> statement-breakpoint
DROP INDEX "osha_reports_company_year_status_index";--> statement-breakpoint
ALTER TABLE "osha_company_information" ALTER COLUMN "created_by" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ALTER COLUMN "employee_department" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ALTER COLUMN "employee_date_of_hire" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ALTER COLUMN "employee_shift" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ALTER COLUMN "treatment_location" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ALTER COLUMN "prescribed_medication" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD COLUMN "archived" boolean DEFAULT false;--> statement-breakpoint
CREATE INDEX "osha_reports_company_year_status_index" ON "osha_reports" USING btree ("upkeep_company_id","archived_at","created_at");--> statement-breakpoint
ALTER TABLE "osha_company_information" DROP COLUMN "executive_user_id";--> statement-breakpoint
ALTER TABLE "osha_reports" DROP COLUMN "employee_user_id";--> statement-breakpoint
ALTER TABLE "osha_reports" DROP COLUMN "reported_by";--> statement-breakpoint
ALTER TABLE "osha_reports" DROP COLUMN "prepared_by_user_id";--> statement-breakpoint
ALTER TABLE "osha_reports" DROP COLUMN "prepared_at";--> statement-breakpoint
ALTER TABLE "osha_reports" DROP COLUMN "status";--> statement-breakpoint
DROP TYPE "public"."osha_report_status";