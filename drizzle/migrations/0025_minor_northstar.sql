ALTER TABLE "global_locations" RENAME TO "osha_locations";--> statement-breakpoint
ALTER TABLE "osha_agency_report" RENAME COLUMN "global_location_id" TO "osha_location_id";--> statement-breakpoint
ALTER TABLE "osha_company_information" RENAME COLUMN "global_location_id" TO "osha_location_id";--> statement-breakpoint
ALTER TABLE "osha_reports" RENAME COLUMN "global_location_id" TO "osha_location_id";--> statement-breakpoint
ALTER TABLE "osha_company_information" DROP CONSTRAINT "osha_company_information_unique_year_company_global_location_index";--> statement-breakpoint
ALTER TABLE "osha_agency_report" DROP CONSTRAINT "osha_agency_report_global_location_id_global_locations_id_fk";
--> statement-breakpoint
ALTER TABLE "osha_company_information" DROP CONSTRAINT "osha_company_information_global_location_id_global_locations_id_fk";
--> statement-breakpoint
ALTER TABLE "osha_reports" DROP CONSTRAINT "osha_reports_global_location_id_global_locations_id_fk";
--> statement-breakpoint
DROP INDEX "global_locations_upkeep_company_id_index";--> statement-breakpoint
DROP INDEX "global_locations_name_upkeep_company_id_index";--> statement-breakpoint
DROP INDEX "osha_agency_report_company_year_global_location_index";--> statement-breakpoint
DROP INDEX "osha_reports_company_year_global_location_index";--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD CONSTRAINT "osha_agency_report_osha_location_id_osha_locations_id_fk" FOREIGN KEY ("osha_location_id") REFERENCES "public"."osha_locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "osha_company_information" ADD CONSTRAINT "osha_company_information_osha_location_id_osha_locations_id_fk" FOREIGN KEY ("osha_location_id") REFERENCES "public"."osha_locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD CONSTRAINT "osha_reports_osha_location_id_osha_locations_id_fk" FOREIGN KEY ("osha_location_id") REFERENCES "public"."osha_locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "osha_locations_upkeep_company_id_index" ON "osha_locations" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "osha_locations_name_upkeep_company_id_index" ON "osha_locations" USING btree ("name","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "osha_agency_report_company_year_osha_location_index" ON "osha_agency_report" USING btree ("upkeep_company_id","archived","created_at","osha_location_id");--> statement-breakpoint
CREATE INDEX "osha_reports_company_year_osha_location_index" ON "osha_reports" USING btree ("upkeep_company_id","archived_at","created_at","osha_location_id");--> statement-breakpoint
ALTER TABLE "osha_company_information" ADD CONSTRAINT "osha_company_information_unique_year_company_global_location_index" UNIQUE("year","upkeep_company_id","osha_location_id");