CREATE TYPE "public"."access_point_status" AS ENUM('active', 'inactive');--> statement-breakpoint
CREATE TYPE "public"."audit_trail_action" AS ENUM('created', 'updated', 'deleted', 'commented', 'submitted', 'closed', 'in_review', 'reopened', 'archived', 'unarchived');--> statement-breakpoint
CREATE TYPE "public"."audit_trail_entity_type" AS ENUM('event', 'capa', 'access_point');--> statement-breakpoint
CREATE TYPE "public"."capa_effectiveness_status" AS ENUM('effective', 'partial', 'not_effective', 'not_evaluated');--> statement-breakpoint
CREATE TYPE "public"."capa_priority" AS ENUM('low', 'medium', 'high');--> statement-breakpoint
CREATE TYPE "public"."capa_tags" AS ENUM('training', 'policy', 'hazard', 'equipment', 'procedure', 'personnel');--> statement-breakpoint
CREATE TYPE "public"."capa_type" AS ENUM('corrective', 'preventive', 'both');--> statement-breakpoint
CREATE TYPE "public"."event_category" AS ENUM('chemical', 'electrical', 'ergonomic', 'fall', 'fire', 'mechanical', 'radiation', 'spill', 'transportation', 'violence', 'other');--> statement-breakpoint
CREATE TYPE "public"."file_status" AS ENUM('pending', 'completed', 'failed', 'expired');--> statement-breakpoint
CREATE TYPE "public"."rca_method" AS ENUM('5_whys', 'fishbone', 'fault_tree', 'other', 'not_selected');--> statement-breakpoint
CREATE TYPE "public"."report_type" AS ENUM('incident', 'near_miss');--> statement-breakpoint
CREATE TYPE "public"."role" AS ENUM('admin', 'technician');--> statement-breakpoint
CREATE TYPE "public"."root_cause" AS ENUM('human_error', 'equipment_failure', 'environmental', 'procedural', 'other');--> statement-breakpoint
CREATE TYPE "public"."severity_level" AS ENUM('low', 'medium', 'high', 'critical');--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('open', 'in_review', 'closed');--> statement-breakpoint
CREATE TABLE "access_points" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"location_id" varchar(10) NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"status" "access_point_status" DEFAULT 'active' NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"archived" boolean DEFAULT false,
	"archived_at" timestamp,
	"updated_at" timestamp,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "audit_trail" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"entity_type" "audit_trail_entity_type" NOT NULL,
	"entity_id" varchar(24) NOT NULL,
	"action" "audit_trail_action" NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"details" text,
	"user_id" varchar(10)
);
--> statement-breakpoint
CREATE TABLE "capas" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"slug" text,
	"title" varchar(255) NOT NULL,
	"summary" text,
	"type" "capa_type" NOT NULL,
	"location_id" varchar(10),
	"asset_id" varchar(10),
	"rca_method" "rca_method" DEFAULT 'not_selected',
	"rca_findings" text NOT NULL,
	"root_cause" "root_cause",
	"other_root_cause" text,
	"ai_suggested_action" text,
	"ai_confidence_score" real,
	"actions_to_address" text,
	"owner_id" varchar(10) NOT NULL,
	"due_date" timestamp,
	"priority" "capa_priority" DEFAULT 'medium' NOT NULL,
	"tags" text[],
	"private_to_admins" boolean DEFAULT false,
	"status" "status" DEFAULT 'open' NOT NULL,
	"team_members_to_notify" varchar(10)[],
	"archived" boolean DEFAULT false,
	"event_id" varchar(24),
	"created_by" varchar(10) NOT NULL,
	"trigger_work_order" boolean DEFAULT false,
	"actions_implemented" text,
	"implementation_date" timestamp,
	"implemented_by" varchar(10),
	"voe_due_date" timestamp,
	"verification_findings" text,
	"voe_performed_by" varchar(10),
	"voe_date" timestamp,
	"effectiveness_status" "capa_effectiveness_status" DEFAULT 'not_evaluated',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "comment_mentions" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"comment_id" varchar(24) NOT NULL,
	"user_id" varchar(10) NOT NULL,
	"position" integer,
	"mention_text" varchar(100),
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "comments" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"event_id" varchar(24),
	"capa_id" varchar(24),
	"content" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"user_id" varchar(10) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "events" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"slug" text,
	"title" varchar(255) NOT NULL,
	"description" text,
	"reported_at" timestamp DEFAULT now() NOT NULL,
	"reported_by" varchar(10),
	"reported_by_name" varchar(255),
	"reported_by_email" varchar(255),
	"type" "report_type" NOT NULL,
	"location_id" varchar(10),
	"asset_ids" varchar(10)[],
	"category" "event_category" NOT NULL,
	"severity" "severity_level" NOT NULL,
	"status" "status" NOT NULL,
	"archived" boolean DEFAULT false,
	"immediate_actions" text,
	"osha_reportable" boolean DEFAULT false,
	"ai_confidence_score" real,
	"team_members_to_notify" varchar(10)[],
	"updated_at" timestamp,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "events_osha" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"event_id" varchar(24) NOT NULL,
	"osha_log_id" varchar(24) NOT NULL,
	"submitted_by" varchar(10) NOT NULL,
	"submitted_at" timestamp,
	"submitted" boolean DEFAULT false,
	"data" jsonb
);
--> statement-breakpoint
CREATE TABLE "events_users" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"event_id" varchar(24) NOT NULL,
	"user_id" varchar(10) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "files" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"file_name" varchar(255) NOT NULL,
	"file_size" integer NOT NULL,
	"mime_type" varchar(127) NOT NULL,
	"presigned_url" text NOT NULL,
	"s3_key" text NOT NULL,
	"s3_bucket" varchar(255) NOT NULL,
	"status" "file_status" DEFAULT 'pending' NOT NULL,
	"entity_type" varchar(255),
	"entity_id" varchar(24),
	"uploaded_by" varchar(10),
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "osha_logs" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"assessment_year" integer NOT NULL,
	"generated_at" timestamp DEFAULT now() NOT NULL,
	"generated_by" text NOT NULL,
	"file_url" text NOT NULL
);
--> statement-breakpoint
ALTER TABLE "capas" ADD CONSTRAINT "capas_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "comment_mentions" ADD CONSTRAINT "comment_mentions_comment_id_comments_id_fk" FOREIGN KEY ("comment_id") REFERENCES "public"."comments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "comments" ADD CONSTRAINT "comments_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "comments" ADD CONSTRAINT "comments_capa_id_capas_id_fk" FOREIGN KEY ("capa_id") REFERENCES "public"."capas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "events_osha" ADD CONSTRAINT "events_osha_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "events_osha" ADD CONSTRAINT "events_osha_osha_log_id_osha_logs_id_fk" FOREIGN KEY ("osha_log_id") REFERENCES "public"."osha_logs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "events_users" ADD CONSTRAINT "events_users_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "capa_slug_index" ON "capas" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "capa_created_by_index" ON "capas" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX "capa_archived_index" ON "capas" USING btree ("archived");--> statement-breakpoint
CREATE INDEX "comment_mention_comment_id_index" ON "comment_mentions" USING btree ("comment_id");--> statement-breakpoint
CREATE INDEX "comment_mention_user_id_index" ON "comment_mentions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "comment_created_at_index" ON "comments" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "event_title_index" ON "events" USING btree ("title");--> statement-breakpoint
CREATE INDEX "event_slug_index" ON "events" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "event_reported_by_index" ON "events" USING btree ("reported_by");--> statement-breakpoint
CREATE INDEX "event_type_index" ON "events" USING btree ("type");--> statement-breakpoint
CREATE INDEX "event_archived_index" ON "events" USING btree ("archived");--> statement-breakpoint
CREATE INDEX "event_deleted_at_index" ON "events" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX "file_status_index" ON "files" USING btree ("status");--> statement-breakpoint
CREATE INDEX "file_entity_index" ON "files" USING btree ("entity_type","entity_id");--> statement-breakpoint
CREATE INDEX "file_uploaded_by_index" ON "files" USING btree ("uploaded_by");--> statement-breakpoint
CREATE INDEX "generated_by_index" ON "osha_logs" USING btree ("generated_by");

CREATE OR REPLACE FUNCTION generate_capa_slug(p_upkeep_company_id TEXT)
RETURNS TEXT AS $$
DECLARE
    seq_name TEXT := format('capa_slug_seq_%s', LOWER(p_upkeep_company_id));
    next_slug INTEGER;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN 'CAPA-' || LPAD(next_slug::TEXT, 4, '0');
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION capas_slug_trigger()
RETURNS TRIGGER AS $$
BEGIN
    NEW.slug := generate_capa_slug(NEW.upkeep_company_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER capas_slug_before_insert
BEFORE INSERT ON capas
FOR EACH ROW
EXECUTE FUNCTION capas_slug_trigger();

CREATE OR REPLACE FUNCTION generate_event_slug(p_upkeep_company_id TEXT)
RETURNS TEXT AS $$
DECLARE
    seq_name TEXT := format('event_slug_seq_%s', LOWER(p_upkeep_company_id));
    next_slug INTEGER;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN 'SE-' || LPAD(next_slug::TEXT, 4, '0');
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION events_slug_trigger()
RETURNS TRIGGER AS $$
BEGIN
    NEW.slug := generate_event_slug(NEW.upkeep_company_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER events_slug_before_insert
BEFORE INSERT ON events
FOR EACH ROW
EXECUTE FUNCTION events_slug_trigger();
