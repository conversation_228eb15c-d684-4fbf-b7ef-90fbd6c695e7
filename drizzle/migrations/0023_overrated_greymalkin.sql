CREATE TABLE "control_measures" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "control_measure_category" NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "hazards" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "hazard_category" NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
DROP TABLE "jha_control_measures" CASCADE;--> statement-breakpoint
DROP TABLE "jha_hazards" CASCADE;--> statement-breakpoint
CREATE INDEX "jha_control_measures_upkeep_company_id_index" ON "control_measures" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_control_measures_name_index" ON "control_measures" USING btree ("name","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_hazards_upkeep_company_id_index" ON "hazards" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_hazards_name_index" ON "hazards" USING btree ("name","upkeep_company_id");