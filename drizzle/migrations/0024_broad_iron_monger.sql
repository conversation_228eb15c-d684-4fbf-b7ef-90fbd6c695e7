ALTER TYPE "public"."entity_type" ADD VALUE 'jha';--> statement-breakpoint
ALTER TYPE "public"."entity_type" ADD VALUE 'hazard';--> statement-breakpoint
ALTER TYPE "public"."entity_type" ADD VALUE 'control_measure';--> statement-breakpoint
ALTER TABLE "audit_trail" ALTER COLUMN "entity_type" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."audit_trail_entity_type";--> statement-breakpoint
CREATE TYPE "public"."audit_trail_entity_type" AS ENUM('event', 'capa', 'access_point', 'global_location', 'jha', 'hazard', 'control_measure');--> statement-breakpoint
ALTER TABLE "audit_trail" ALTER COLUMN "entity_type" SET DATA TYPE "public"."audit_trail_entity_type" USING "entity_type"::"public"."audit_trail_entity_type";