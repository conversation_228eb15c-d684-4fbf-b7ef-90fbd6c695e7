CREATE TYPE "public"."jha_status" AS ENUM('draft', 'review', 'approved', 'revision', 'archived');--> statement-breakpoint
CREATE TABLE "jha" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"slug" text,
	"version" varchar(10) DEFAULT '1.0' NOT NULL,
	"instance_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"owner_id" varchar(10) NOT NULL,
	"approver_id" varchar(10) NOT NULL,
	"status" "jha_status" DEFAULT 'draft' NOT NULL,
	"review_date" timestamp,
	"location_id" varchar(10),
	"asset_ids" varchar(10)[],
	"highest_severity" integer DEFAULT 0 NOT NULL,
	"notes" text,
	"is_public" boolean DEFAULT false NOT NULL,
	"work_order_id" varchar(10),
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp,
	CONSTRAINT "jha_slug_index" UNIQUE("slug","upkeep_company_id","version")
);
--> statement-breakpoint
CREATE TABLE "jha_control_measures" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10),
	"name" varchar(255) NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "jha_hazards" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10),
	"name" varchar(255) NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "jha_steps" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"jha_id" varchar(24) NOT NULL,
	"serial" integer DEFAULT 1 NOT NULL,
	"title" varchar(255) NOT NULL,
	"hazard_ids" varchar(24)[],
	"control_measure_ids" varchar(24)[],
	"severity" integer DEFAULT 0 NOT NULL,
	"likelihood" integer DEFAULT 0 NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "jha_steps" ADD CONSTRAINT "jha_steps_jha_id_jha_id_fk" FOREIGN KEY ("jha_id") REFERENCES "public"."jha"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "jha_upkeep_company_id_index" ON "jha" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_instance_id_index" ON "jha" USING btree ("instance_id","upkeep_company_id","archived_at");--> statement-breakpoint
CREATE INDEX "jha_owner_id_index" ON "jha" USING btree ("owner_id","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_search_index" ON "jha" USING btree ("upkeep_company_id","title","status","archived_at");--> statement-breakpoint
CREATE INDEX "jha_work_order_id_index" ON "jha" USING btree ("work_order_id","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_highest_severity_index" ON "jha" USING btree ("highest_severity","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_control_measures_upkeep_company_id_index" ON "jha_control_measures" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_control_measures_name_index" ON "jha_control_measures" USING btree ("name","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_hazards_upkeep_company_id_index" ON "jha_hazards" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_hazards_name_index" ON "jha_hazards" USING btree ("name","upkeep_company_id");--> statement-breakpoint
CREATE INDEX "jha_steps_upkeep_company_id_index" ON "jha_steps" USING btree ("upkeep_company_id","jha_id");--> statement-breakpoint
CREATE INDEX "jha_steps_serial_index" ON "jha_steps" USING btree ("serial","upkeep_company_id","jha_id");

-- Add pgcrypto extension to automatically generate UUIDs on instanceId for JHA
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Add function to generate JHA slug
CREATE OR REPLACE FUNCTION generate_jha_slug(p_upkeep_company_id TEXT, p_version TEXT)
RETURNS TEXT AS $$
DECLARE
    seq_name TEXT := format('jha_slug_seq_%s', LOWER(p_upkeep_company_id));
    next_slug INTEGER;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN format('JHA-%s-%s', LPAD(next_slug::TEXT, 4, '0'), p_version);
END;
$$ LANGUAGE plpgsql;

-- Add function to get next JHA version
CREATE OR REPLACE FUNCTION get_next_jha_version(p_instance_id UUID)
RETURNS TEXT AS $$
DECLARE
    count_versions INTEGER;
BEGIN
    SELECT COUNT(*) INTO count_versions
    FROM jha
    WHERE instance_id = p_instance_id;

    RETURN (count_versions + 1)::TEXT || '.0';
END;
$$ LANGUAGE plpgsql;

-- Add trigger to generate slug, version and instance_id on insert
CREATE OR REPLACE FUNCTION jha_before_insert_trigger()
RETURNS TRIGGER AS $$
DECLARE
    resolved_instance_id UUID;
    resolved_version TEXT;
    base_slug TEXT;
    base_number TEXT;
BEGIN
    -- Set instance_id if missing
    IF NEW.instance_id IS NULL THEN
        NEW.instance_id := gen_random_uuid();
    END IF;
    resolved_instance_id := NEW.instance_id;

    -- Calculate version
    resolved_version := get_next_jha_version(resolved_instance_id);
    NEW.version := resolved_version;

    -- Set updated_at for current insert
    NEW.updated_at := now();

    -- New instance → generate new slug
    IF resolved_version = '1.0' THEN
        NEW.slug := generate_jha_slug(NEW.upkeep_company_id::TEXT, resolved_version::TEXT);
    ELSE
        -- Existing instance → reuse base number for slug
        SELECT slug INTO base_slug
        FROM jha
        WHERE instance_id = resolved_instance_id AND version = '1.0'
        LIMIT 1;

        base_number := regexp_replace(base_slug, '^JHA-(\d{4})-.*$', '\1');
        NEW.slug := format('JHA-%s-%s', base_number, resolved_version);

        -- Archive all previous versions
        UPDATE jha
        SET archived_at = now(), updated_at = now()
        WHERE instance_id = resolved_instance_id
          AND id != NEW.id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to generate slug, version and instance_id on insert
CREATE TRIGGER jha_before_insert
BEFORE INSERT ON jha
FOR EACH ROW
EXECUTE FUNCTION jha_before_insert_trigger();

-- Insert global control measures
INSERT INTO jha_control_measures (id, upkeep_company_id, name, created_by, created_at)
VALUES 
  ('ctrlm000000000000000001', NULL, 'Engineering controls', 'system', now()),
  ('ctrlm000000000000000002', NULL, 'Administrative controls', 'system', now()),
  ('ctrlm000000000000000003', NULL, 'Personal protective equipment', 'system', now()),
  ('ctrlm000000000000000004', NULL, 'Elimination', 'system', now()),
  ('ctrlm000000000000000005', NULL, 'Substitution', 'system', now()),
  ('ctrlm000000000000000006', NULL, 'Use Lockout Tagout', 'system', now()),
  ('ctrlm000000000000000007', NULL, 'Wear thermal gloves', 'system', now()),
  ('ctrlm000000000000000008', NULL, 'Wear arc-rated gloves', 'system', now()),
  ('ctrlm000000000000000009', NULL, 'Use respirator', 'system', now()),
  ('ctrlm000000000000000010', NULL, 'Use portable fan', 'system', now()),
  ('ctrlm000000000000000011', NULL, 'Gas meter required', 'system', now()),
  ('ctrlm000000000000000012', NULL, 'Use fall protection harness', 'system', now()),
  ('ctrlm000000000000000013', NULL, 'Lockout Tagout, Notify occupants', 'system', now()),
  ('ctrlm000000000000000014', NULL, 'Ladder safety, PPE (gloves)', 'system', now()),
  ('ctrlm000000000000000015', NULL, 'N95 mask, Gloves, Waste bag', 'system', now()),
  ('ctrlm000000000000000016', NULL, 'Follow manufacturer guide', 'system', now()),
  ('ctrlm000000000000000017', NULL, 'LOTO removal procedure, Test', 'system', now());

-- Insert global hazards
INSERT INTO jha_hazards (id, upkeep_company_id, name, created_by, created_at)
VALUES
  ('hazrd000000000000000001', NULL, 'Slip, trip, or fall', 'system', now()),
  ('hazrd000000000000000002', NULL, 'Struck by object', 'system', now()),
  ('hazrd000000000000000003', NULL, 'Caught in/between', 'system', now()),
  ('hazrd000000000000000004', NULL, 'Fall from height', 'system', now()),
  ('hazrd000000000000000005', NULL, 'Ergonomic strain', 'system', now()),
  ('hazrd000000000000000006', NULL, 'Chemical exposure', 'system', now()),
  ('hazrd000000000000000007', NULL, 'Fire/explosion', 'system', now()),
  ('hazrd000000000000000008', NULL, 'Noise exposure', 'system', now()),
  ('hazrd000000000000000009', NULL, 'Heat stress', 'system', now()),
  ('hazrd000000000000000010', NULL, 'Cold stress', 'system', now()),
  ('hazrd000000000000000011', NULL, 'Vibration', 'system', now()),
  ('hazrd000000000000000012', NULL, 'Radiation exposure', 'system', now()),
  ('hazrd000000000000000013', NULL, 'Electrical shock', 'system', now()),
  ('hazrd000000000000000014', NULL, 'Hot fluid spray', 'system', now()),
  ('hazrd000000000000000015', NULL, 'High pressure', 'system', now()),
  ('hazrd000000000000000016', NULL, 'Arc flash', 'system', now()),
  ('hazrd000000000000000017', NULL, 'Toxic gases', 'system', now()),
  ('hazrd000000000000000018', NULL, 'Oxygen deficiency', 'system', now()),
  ('hazrd000000000000000019', NULL, 'Incorrect installation', 'system', now()),
  ('hazrd000000000000000020', NULL, 'Eye injury', 'system', now()),
  ('hazrd000000000000000021', NULL, 'Electrocution, Unit startup', 'system', now()),
  ('hazrd000000000000000022', NULL, 'Fall from height, Sharp edges', 'system', now()),
  ('hazrd000000000000000023', NULL, 'Airborne dust, Biohazards, Cuts', 'system', now()),
  ('hazrd000000000000000024', NULL, 'Unit malfunction', 'system', now());
