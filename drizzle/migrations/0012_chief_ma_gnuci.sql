-- Migration 0012: Agency Report Schema Updates + Slug Prefix Fix
-- 
-- SCHEMA CHANGES:
-- - Add description, affected_count, date_prepared, archived columns to osha_agency_report
-- - Alter employees_involved column to osha_agency_report
--
-- FUNCTION CHANGES: 
-- - Update generate_osha_agency_report_slug() to use AR- prefix instead of SE-
-- - NOTE: Function changes are not tracked by Drizzle snapshots
--
ALTER TABLE "osha_agency_report" ALTER COLUMN "employees_involved" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD COLUMN "description" text;--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD COLUMN "affected_count" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD COLUMN "date_prepared" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD COLUMN "archived" boolean DEFAULT false;
-- Fix agency report slug generation to use AR- prefix instead of SE-
CREATE OR REPLACE FUNCTION generate_osha_agency_report_slug(p_upkeep_company_id TEXT)
RETURNS TEXT AS $$
DECLARE
    current_year TEXT := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    seq_name TEXT := format('osha_agency_report_slug_seq_%s_%s', LOWER(p_upkeep_company_id), current_year);
    next_slug INTEGER;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN format('AR-%s-%s', current_year, LPAD(next_slug::TEXT, 4, '0'));
END;
$$ LANGUAGE plpgsql;