CREATE TYPE "public"."primary_recordable_outcome" AS ENUM('fatality', 'days_away_from_work', 'job_restriction', 'medical_treatment_beyond_first_aid', 'loss_of_consciousness', 'significant_injury');--> statement-breakpoint
ALTER TABLE "osha_audit_trail" RENAME COLUMN "user_device" TO "user_agent";--> statement-breakpoint
ALTER TABLE "osha_reports" ADD COLUMN "employee_work_location" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD COLUMN "reason_for_privacy_case" text;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD COLUMN "primary_recordable_outcome" "primary_recordable_outcome" NOT NULL;