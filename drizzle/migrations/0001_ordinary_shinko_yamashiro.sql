CREATE TYPE "public"."osha_agency_report_type" AS ENUM('fatality', 'amputation', 'hospitalization', 'eye_loss');--> statement-breakpoint
CREATE TYPE "public"."osha_audit_trail_action" AS ENUM('created', 'updated', 'submitted', 'downloaded', 'signed', 'archived');--> statement-breakpoint
CREATE TYPE "public"."osha_entity_type" AS ENUM('osha_report', 'osha_company_information', 'osha_agency_report');--> statement-breakpoint
CREATE TYPE "public"."osha_report_reason" AS ENUM('medical_treatment_beyond_first_aid', 'days_away_from_work', 'restricted_from_work', 'loss_of_consciousness', 'significant_injury', 'fatality');--> statement-breakpoint
CREATE TYPE "public"."shifts" AS ENUM('day', 'evening', 'night', 'rotating', 'other');--> statement-breakpoint
CREATE TYPE "public"."type_of_medical_care" AS ENUM('first_aid', 'medical_treatment', 'emergency_room', 'overnight_hospital_stay');--> statement-breakpoint
CREATE TABLE "osha_agency_report" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"slug" text,
	"date_of_incident" timestamp NOT NULL,
	"location_id" varchar(10) NOT NULL,
	"type_of_incident" "osha_agency_report_type" NOT NULL,
	"employees_involved" varchar(10)[],
	"company_contact_person" varchar(255) NOT NULL,
	"contact_person_phone" varchar(255) NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "osha_audit_trail" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"entity_id" varchar(24) NOT NULL,
	"entity_type" "osha_entity_type" NOT NULL,
	"action" "osha_audit_trail_action" NOT NULL,
	"ip_address" varchar(255) NOT NULL,
	"user_device" varchar(255) NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"details" text
);
--> statement-breakpoint
CREATE TABLE "osha_company_information" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"company_name" varchar(255) NOT NULL,
	"company_facility_id" varchar(255) NOT NULL,
	"company_naics_code" varchar(255) NOT NULL,
	"company_ein" varchar(255) NOT NULL,
	"year" integer NOT NULL,
	"company_annual_average_number_of_employees" integer NOT NULL,
	"company_total_hours_worked" integer NOT NULL,
	"executive_user_id" varchar(10),
	"executive_name" varchar(255),
	"executive_title" varchar(255),
	"date_certified" timestamp DEFAULT now() NOT NULL,
	"archived" boolean DEFAULT false,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp,
	"archived_by" varchar(10)
);
--> statement-breakpoint
CREATE TABLE "osha_reports" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"slug" text,
	"employee_user_id" varchar(10),
	"employee_name" varchar(255),
	"employee_department" varchar(255) NOT NULL,
	"employee_job_title" varchar(255) NOT NULL,
	"employee_date_of_hire" timestamp NOT NULL,
	"employee_shift" "shifts" NOT NULL,
	"employee_supervisor" varchar(255),
	"privacy_case" boolean DEFAULT false NOT NULL,
	"event_id" varchar(24) NOT NULL,
	"body_part_injured" varchar(255) NOT NULL,
	"type_of_injury" varchar(255) NOT NULL,
	"treatment_location" varchar(255) NOT NULL,
	"type_of_medical_care" "type_of_medical_care" NOT NULL,
	"prescribed_medication" boolean DEFAULT false NOT NULL,
	"was_hospitalized" boolean DEFAULT false NOT NULL,
	"was_deceased" boolean DEFAULT false NOT NULL,
	"days_away_from_work" integer DEFAULT 0 NOT NULL,
	"days_restricted_from_work" integer DEFAULT 0 NOT NULL,
	"witness_name" varchar(255),
	"reported_by" varchar(255) NOT NULL,
	"prepared_by_user_id" varchar(10) NOT NULL,
	"prepared_at" timestamp DEFAULT now() NOT NULL,
	"root_cause_analysis" text,
	"corrective_actions" text,
	"reason_for_report" "osha_report_reason" NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp,
	"archived_by" varchar(10)
);
--> statement-breakpoint
DROP TABLE "events_osha" CASCADE;--> statement-breakpoint
DROP TABLE "osha_logs" CASCADE;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD CONSTRAINT "osha_reports_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE no action ON UPDATE no action;


CREATE OR REPLACE FUNCTION generate_osha_slug(p_upkeep_company_id TEXT)
RETURNS TEXT AS $$
DECLARE
    seq_name TEXT := format('osha_slug_seq_%s_%s', LOWER(p_upkeep_company_id), EXTRACT(YEAR FROM CURRENT_DATE)::TEXT);
    next_slug INTEGER;
    current_year TEXT := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN format('OSHA-301-%s-%s', current_year, LPAD(next_slug::TEXT, 4, '0'));
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION osha_slug_trigger()
RETURNS TRIGGER AS $$
BEGIN
    NEW.slug := generate_osha_slug(NEW.upkeep_company_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER osha_slug_before_insert
BEFORE INSERT ON osha_reports
FOR EACH ROW
EXECUTE FUNCTION osha_slug_trigger();

CREATE OR REPLACE FUNCTION generate_osha_agency_report_slug(p_upkeep_company_id TEXT)
RETURNS TEXT AS $$
DECLARE
    current_year TEXT := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    seq_name TEXT := format('osha_agency_report_slug_seq_%s_%s', LOWER(p_upkeep_company_id), current_year);
    next_slug INTEGER;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN format('SE-%s-%s', current_year, LPAD(next_slug::TEXT, 4, '0'));
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION osha_agency_report_slug_trigger()
RETURNS TRIGGER AS $$
BEGIN
    NEW.slug := generate_osha_agency_report_slug(NEW.upkeep_company_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER osha_agency_report_slug_before_insert
BEFORE INSERT ON osha_agency_report
FOR EACH ROW
EXECUTE FUNCTION osha_agency_report_slug_trigger();

