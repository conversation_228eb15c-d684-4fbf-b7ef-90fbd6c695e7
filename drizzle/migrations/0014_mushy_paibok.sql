ALTER TABLE "osha_agency_report" ADD COLUMN "global_location_id" varchar(24) NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_company_information" ADD COLUMN "global_location_id" varchar(24) NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD COLUMN "global_location_id" varchar(24) NOT NULL;--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD CONSTRAINT "osha_agency_report_global_location_id_global_locations_id_fk" FOREIGN KEY ("global_location_id") REFERENCES "public"."global_locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "osha_company_information" ADD CONSTRAINT "osha_company_information_global_location_id_global_locations_id_fk" FOREIGN KEY ("global_location_id") REFERENCES "public"."global_locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "osha_reports" ADD CONSTRAINT "osha_reports_global_location_id_global_locations_id_fk" FOREIGN KEY ("global_location_id") REFERENCES "public"."global_locations"("id") ON DELETE no action ON UPDATE no action;