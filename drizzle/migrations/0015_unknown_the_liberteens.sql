ALTER TABLE "osha_company_information" DROP CONSTRAINT "osha_company_information_unique_year_company_index";--> statement-breakpoint
CREATE INDEX "osha_agency_report_company_year_global_location_index" ON "osha_agency_report" USING btree ("upkeep_company_id","archived","created_at","global_location_id");--> statement-breakpoint
CREATE INDEX "osha_reports_company_year_global_location_index" ON "osha_reports" USING btree ("upkeep_company_id","archived_at","created_at","global_location_id");--> statement-breakpoint
ALTER TABLE "osha_company_information" ADD CONSTRAINT "osha_company_information_unique_year_company_global_location_index" UNIQUE("year","upkeep_company_id","global_location_id");