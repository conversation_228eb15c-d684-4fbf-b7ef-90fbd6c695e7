{"id": "ce9cadd8-180e-4e9f-8356-33af29bd9ebd", "prevId": "c43d1a2f-1157-48fa-8cf4-46b92c15cb58", "version": "7", "dialect": "postgresql", "tables": {"public.access_points": {"name": "access_points", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "access_point_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_trail": {"name": "audit_trail", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "audit_trail_entity_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "audit_trail_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.capas": {"name": "capas", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "capa_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "asset_id": {"name": "asset_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "rca_method": {"name": "rca_method", "type": "rca_method", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'not_selected'"}, "rca_findings": {"name": "rca_findings", "type": "text", "primaryKey": false, "notNull": true}, "root_causes": {"name": "root_causes", "type": "text[]", "primaryKey": false, "notNull": false}, "other_root_cause": {"name": "other_root_cause", "type": "text", "primaryKey": false, "notNull": false}, "ai_suggested_action": {"name": "ai_suggested_action", "type": "text", "primaryKey": false, "notNull": false}, "ai_confidence_score": {"name": "ai_confidence_score", "type": "real", "primaryKey": false, "notNull": false}, "actions_to_address": {"name": "actions_to_address", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "capa_priority", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'medium'"}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "private_to_admins": {"name": "private_to_admins", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'open'"}, "team_members_to_notify": {"name": "team_members_to_notify", "type": "<PERSON><PERSON><PERSON>(10)[]", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "trigger_work_order": {"name": "trigger_work_order", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "actions_implemented": {"name": "actions_implemented", "type": "text", "primaryKey": false, "notNull": false}, "implementation_date": {"name": "implementation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "implemented_by": {"name": "implemented_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "voe_due_date": {"name": "voe_due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_findings": {"name": "verification_findings", "type": "text", "primaryKey": false, "notNull": false}, "voe_performed_by": {"name": "voe_performed_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "voe_date": {"name": "voe_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "effectiveness_status": {"name": "effectiveness_status", "type": "capa_effectiveness_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'not_evaluated'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"capa_slug_index": {"name": "capa_slug_index", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "capa_created_by_index": {"name": "capa_created_by_index", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "capa_archived_index": {"name": "capa_archived_index", "columns": [{"expression": "archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"capas_event_id_events_id_fk": {"name": "capas_event_id_events_id_fk", "tableFrom": "capas", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comment_mentions": {"name": "comment_mentions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "comment_id": {"name": "comment_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": false}, "mention_text": {"name": "mention_text", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"comment_mention_comment_id_index": {"name": "comment_mention_comment_id_index", "columns": [{"expression": "comment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "comment_mention_user_id_index": {"name": "comment_mention_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comment_mentions_comment_id_comments_id_fk": {"name": "comment_mentions_comment_id_comments_id_fk", "tableFrom": "comment_mentions", "tableTo": "comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comments": {"name": "comments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "capa_id": {"name": "capa_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}}, "indexes": {"comment_created_at_index": {"name": "comment_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comments_event_id_events_id_fk": {"name": "comments_event_id_events_id_fk", "tableFrom": "comments", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "comments_capa_id_capas_id_fk": {"name": "comments_capa_id_capas_id_fk", "tableFrom": "comments", "tableTo": "capas", "columnsFrom": ["capa_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.control_measures": {"name": "control_measures", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "control_measure_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"jha_control_measures_upkeep_company_id_index": {"name": "jha_control_measures_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_control_measures_name_index": {"name": "jha_control_measures_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"control_measures_unique_name_type_company_index": {"name": "control_measures_unique_name_type_company_index", "nullsNotDistinct": false, "columns": ["name", "type", "upkeep_company_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "reported_at": {"name": "reported_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reported_by": {"name": "reported_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "reported_by_name": {"name": "reported_by_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "reported_by_email": {"name": "reported_by_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "report_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "asset_ids": {"name": "asset_ids", "type": "<PERSON><PERSON><PERSON>(10)[]", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "hazard_category", "typeSchema": "public", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "severity_level", "typeSchema": "public", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "immediate_actions": {"name": "immediate_actions", "type": "text", "primaryKey": false, "notNull": false}, "osha_reportable": {"name": "osha_reportable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "ai_confidence_score": {"name": "ai_confidence_score", "type": "real", "primaryKey": false, "notNull": false}, "team_members_to_notify": {"name": "team_members_to_notify", "type": "<PERSON><PERSON><PERSON>(10)[]", "primaryKey": false, "notNull": false}, "customer_name": {"name": "customer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "customer_phone_number": {"name": "customer_phone_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "customer_address": {"name": "customer_address", "type": "text", "primaryKey": false, "notNull": false}, "witnesses": {"name": "witnesses", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"event_title_index": {"name": "event_title_index", "columns": [{"expression": "title", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_slug_index": {"name": "event_slug_index", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_reported_by_index": {"name": "event_reported_by_index", "columns": [{"expression": "reported_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_type_index": {"name": "event_type_index", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_archived_index": {"name": "event_archived_index", "columns": [{"expression": "archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_deleted_at_index": {"name": "event_deleted_at_index", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_users": {"name": "events_users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"events_users_event_id_events_id_fk": {"name": "events_users_event_id_events_id_fk", "tableFrom": "events_users", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(127)", "primaryKey": false, "notNull": true}, "presigned_url": {"name": "presigned_url", "type": "text", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "text", "primaryKey": false, "notNull": true}, "s3_bucket": {"name": "s3_bucket", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "file_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "entity_type": {"name": "entity_type", "type": "entity_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"file_status_index": {"name": "file_status_index", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_entity_index": {"name": "file_entity_index", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploaded_by_index": {"name": "file_uploaded_by_index", "columns": [{"expression": "uploaded_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hazards": {"name": "hazards", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "hazard_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"jha_hazards_upkeep_company_id_index": {"name": "jha_hazards_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_hazards_name_index": {"name": "jha_hazards_name_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"hazards_unique_name_type_company_index": {"name": "hazards_unique_name_type_company_index", "nullsNotDistinct": false, "columns": ["name", "type", "upkeep_company_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.jha": {"name": "jha", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'1.0'"}, "instance_id": {"name": "instance_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "approver_id": {"name": "approver_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "jha_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "review_date": {"name": "review_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "asset_ids": {"name": "asset_ids", "type": "<PERSON><PERSON><PERSON>(10)[]", "primaryKey": false, "notNull": false}, "highest_severity": {"name": "highest_severity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "work_order_id": {"name": "work_order_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"jha_upkeep_company_id_index": {"name": "jha_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_instance_id_index": {"name": "jha_instance_id_index", "columns": [{"expression": "instance_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_owner_id_index": {"name": "jha_owner_id_index", "columns": [{"expression": "owner_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_search_index": {"name": "jha_search_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "title", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_work_order_id_index": {"name": "jha_work_order_id_index", "columns": [{"expression": "work_order_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_highest_severity_index": {"name": "jha_highest_severity_index", "columns": [{"expression": "highest_severity", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"jha_slug_index": {"name": "jha_slug_index", "nullsNotDistinct": false, "columns": ["slug", "upkeep_company_id", "version"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.jha_steps": {"name": "jha_steps", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "jha_id": {"name": "jha_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "serial": {"name": "serial", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "hazard_ids": {"name": "hazard_ids", "type": "<PERSON><PERSON><PERSON>(24)[]", "primaryKey": false, "notNull": false}, "control_measure_ids": {"name": "control_measure_ids", "type": "<PERSON><PERSON><PERSON>(24)[]", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "likelihood": {"name": "likelihood", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"jha_steps_upkeep_company_id_index": {"name": "jha_steps_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "jha_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "jha_steps_serial_index": {"name": "jha_steps_serial_index", "columns": [{"expression": "serial", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "jha_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"jha_steps_jha_id_jha_id_fk": {"name": "jha_steps_jha_id_jha_id_fk", "tableFrom": "jha_steps", "tableTo": "jha", "columnsFrom": ["jha_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.osha_agency_report": {"name": "osha_agency_report", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "osha_location_id": {"name": "osha_location_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "date_of_incident": {"name": "date_of_incident", "type": "timestamp", "primaryKey": false, "notNull": true}, "type_of_incident": {"name": "type_of_incident", "type": "osha_agency_report_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "employees_involved": {"name": "employees_involved", "type": "text", "primaryKey": false, "notNull": false}, "company_contact_person": {"name": "company_contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contact_person_phone": {"name": "contact_person_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "affected_count": {"name": "affected_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "date_prepared": {"name": "date_prepared", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"osha_agency_report_company_year_osha_location_index": {"name": "osha_agency_report_company_year_osha_location_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "osha_location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"osha_agency_report_osha_location_id_osha_locations_id_fk": {"name": "osha_agency_report_osha_location_id_osha_locations_id_fk", "tableFrom": "osha_agency_report", "tableTo": "osha_locations", "columnsFrom": ["osha_location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.osha_audit_trail": {"name": "osha_audit_trail", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "osha_entity_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "osha_audit_trail_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_agent": {"name": "user_agent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"osha_audit_trail_upkeep_company_id_index": {"name": "osha_audit_trail_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_audit_trail_entity_company_index": {"name": "osha_audit_trail_entity_company_index", "columns": [{"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_audit_trail_entity_order_index": {"name": "osha_audit_trail_entity_order_index", "columns": [{"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.osha_company_information": {"name": "osha_company_information", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": true}, "osha_location_id": {"name": "osha_location_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_facility_id": {"name": "company_facility_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_naics_code": {"name": "company_naics_code", "type": "integer", "primaryKey": false, "notNull": true}, "company_ein": {"name": "company_ein", "type": "<PERSON><PERSON><PERSON>(9)", "primaryKey": false, "notNull": true}, "company_annual_average_number_of_employees": {"name": "company_annual_average_number_of_employees", "type": "integer", "primaryKey": false, "notNull": true}, "company_total_hours_worked": {"name": "company_total_hours_worked", "type": "integer", "primaryKey": false, "notNull": true}, "executive_name": {"name": "executive_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "executive_title": {"name": "executive_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_certified": {"name": "date_certified", "type": "timestamp", "primaryKey": false, "notNull": false}, "digital_signature": {"name": "digital_signature", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_by": {"name": "archived_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}}, "indexes": {"osha_company_information_upkeep_company_id_index": {"name": "osha_company_information_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_company_information_year_company_index": {"name": "osha_company_information_year_company_index", "columns": [{"expression": "year", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"osha_company_information_osha_location_id_osha_locations_id_fk": {"name": "osha_company_information_osha_location_id_osha_locations_id_fk", "tableFrom": "osha_company_information", "tableTo": "osha_locations", "columnsFrom": ["osha_location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"osha_company_information_unique_year_company_global_location_index": {"name": "osha_company_information_unique_year_company_global_location_index", "nullsNotDistinct": false, "columns": ["year", "upkeep_company_id", "osha_location_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.osha_locations": {"name": "osha_locations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"osha_locations_upkeep_company_id_index": {"name": "osha_locations_upkeep_company_id_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_locations_name_upkeep_company_id_index": {"name": "osha_locations_name_upkeep_company_id_index", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.osha_reports": {"name": "osha_reports", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": true, "notNull": true}, "upkeep_company_id": {"name": "upkeep_company_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "employee_name": {"name": "employee_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "employee_work_location": {"name": "employee_work_location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "employee_department": {"name": "employee_department", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "employee_job_title": {"name": "employee_job_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "employee_date_of_hire": {"name": "employee_date_of_hire", "type": "timestamp", "primaryKey": false, "notNull": false}, "employee_shift": {"name": "employee_shift", "type": "shifts", "typeSchema": "public", "primaryKey": false, "notNull": false}, "employee_supervisor": {"name": "employee_supervisor", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "osha_location_id": {"name": "osha_location_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "privacy_case": {"name": "privacy_case", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "reason_for_privacy_case": {"name": "reason_for_privacy_case", "type": "text", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(24)", "primaryKey": false, "notNull": true}, "reported_by_name": {"name": "reported_by_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "body_part_injured": {"name": "body_part_injured", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type_of_injury": {"name": "type_of_injury", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "treatment_location": {"name": "treatment_location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type_of_medical_care": {"name": "type_of_medical_care", "type": "type_of_medical_care", "typeSchema": "public", "primaryKey": false, "notNull": true}, "prescribed_medication": {"name": "prescribed_medication", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "was_hospitalized": {"name": "was_hospitalized", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "was_deceased": {"name": "was_deceased", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "days_away_from_work": {"name": "days_away_from_work", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "days_restricted_from_work": {"name": "days_restricted_from_work", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "witnesses": {"name": "witnesses", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "root_cause_analysis": {"name": "root_cause_analysis", "type": "text", "primaryKey": false, "notNull": false}, "corrective_actions": {"name": "corrective_actions", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "osha_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reason_for_report": {"name": "reason_for_report", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_by": {"name": "archived_by", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}}, "indexes": {"osha_reports_company_archived_index": {"name": "osha_reports_company_archived_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_reports_company_archived_created_index": {"name": "osha_reports_company_archived_created_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_reports_company_year_status_index": {"name": "osha_reports_company_year_status_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "osha_reports_company_year_osha_location_index": {"name": "osha_reports_company_year_osha_location_index", "columns": [{"expression": "upkeep_company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "archived_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "osha_location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"osha_reports_osha_location_id_osha_locations_id_fk": {"name": "osha_reports_osha_location_id_osha_locations_id_fk", "tableFrom": "osha_reports", "tableTo": "osha_locations", "columnsFrom": ["osha_location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "osha_reports_event_id_events_id_fk": {"name": "osha_reports_event_id_events_id_fk", "tableFrom": "osha_reports", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.access_point_status": {"name": "access_point_status", "schema": "public", "values": ["active", "inactive"]}, "public.audit_trail_action": {"name": "audit_trail_action", "schema": "public", "values": ["created", "updated", "deleted", "commented", "submitted", "closed", "in_review", "reopened", "archived", "unarchived"]}, "public.audit_trail_entity_type": {"name": "audit_trail_entity_type", "schema": "public", "values": ["event", "capa", "access_point", "global_location", "jha", "hazard", "control_measure"]}, "public.capa_effectiveness_status": {"name": "capa_effectiveness_status", "schema": "public", "values": ["effective", "partial", "not_effective", "not_evaluated"]}, "public.capa_priority": {"name": "capa_priority", "schema": "public", "values": ["low", "medium", "high"]}, "public.capa_tags": {"name": "capa_tags", "schema": "public", "values": ["training", "policy", "hazard", "equipment", "procedure", "personnel"]}, "public.capa_type": {"name": "capa_type", "schema": "public", "values": ["corrective", "preventive", "both"]}, "public.control_measure_category": {"name": "control_measure_category", "schema": "public", "values": ["engineering_controls", "administrative_controls", "personal_protective_equipment", "specialized_advanced_controls", "other"]}, "public.entity_type": {"name": "entity_type", "schema": "public", "values": ["event", "capa", "access_point", "comment", "global_location", "audit_trail", "file", "osha_audit_trail", "osha_report", "osha_company_information", "osha_agency_report", "jha", "hazard", "control_measure"]}, "public.file_status": {"name": "file_status", "schema": "public", "values": ["pending", "completed", "failed", "expired"]}, "public.hazard_category": {"name": "hazard_category", "schema": "public", "values": ["chemical", "electrical", "physical", "environmental", "ergonomic", "fall", "biological", "fire", "mechanical", "radiation", "noise", "thermal", "atmospheric", "spill", "transportation", "violence", "other"]}, "public.jha_status": {"name": "jha_status", "schema": "public", "values": ["draft", "review", "approved", "revision", "archived"]}, "public.osha_agency_report_type": {"name": "osha_agency_report_type", "schema": "public", "values": ["fatality", "amputation", "hospitalization", "eye_loss"]}, "public.osha_audit_trail_action": {"name": "osha_audit_trail_action", "schema": "public", "values": ["created", "updated", "submitted", "downloaded", "signed", "archived", "restored"]}, "public.osha_entity_type": {"name": "osha_entity_type", "schema": "public", "values": ["osha_report", "osha_company_information", "osha_agency_report"]}, "public.osha_type": {"name": "osha_type", "schema": "public", "values": ["fatality", "days_away_from_work", "job_restriction", "medical_treatment_beyond_first_aid", "loss_of_consciousness", "significant_injury"]}, "public.rca_method": {"name": "rca_method", "schema": "public", "values": ["5_whys", "fishbone", "fault_tree", "other", "not_selected"]}, "public.report_type": {"name": "report_type", "schema": "public", "values": ["incident", "near_miss", "observation", "customer_incident"]}, "public.role": {"name": "role", "schema": "public", "values": ["admin", "technician"]}, "public.root_cause": {"name": "root_cause", "schema": "public", "values": ["human_error", "equipment_failure", "environmental", "procedural", "other"]}, "public.severity_level": {"name": "severity_level", "schema": "public", "values": ["low", "medium", "high", "critical"]}, "public.shifts": {"name": "shifts", "schema": "public", "values": ["day", "evening", "night", "rotating", "other"]}, "public.status": {"name": "status", "schema": "public", "values": ["open", "in_review", "closed"]}, "public.type_of_medical_care": {"name": "type_of_medical_care", "schema": "public", "values": ["first_aid", "medical_treatment", "emergency_room", "overnight_hospital_stay"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}