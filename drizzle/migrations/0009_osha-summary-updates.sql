ALTER TABLE "osha_company_information"
DROP COLUMN "company_naics_code";

ALTER TABLE "osha_company_information"
ADD COLUMN "company_naics_code" integer NOT NULL;

--> statement-breakpoint
ALTER TABLE "osha_company_information"
ALTER COLUMN "company_ein"
SET
    DATA TYPE varchar(9);

--> statement-breakpoint
ALTER TABLE "osha_company_information"
ALTER COLUMN "date_certified"
DROP DEFAULT;

--> statement-breakpoint
ALTER TABLE "osha_company_information"
ALTER COLUMN "date_certified"
DROP NOT NULL;

--> statement-breakpoint
ALTER TABLE "osha_reports"
ALTER COLUMN "witnesses"
SET
    DATA TYPE varchar(255);

--> statement-breakpoint
ALTER TABLE "osha_company_information"
ADD COLUMN "digital_signature" text;

--> statement-breakpoint
CREATE INDEX "osha_audit_trail_upkeep_company_id_index" ON "osha_audit_trail" USING btree ("upkeep_company_id");

--> statement-breakpoint
CREATE INDEX "osha_audit_trail_entity_company_index" ON "osha_audit_trail" USING btree ("entity_id", "upkeep_company_id", "entity_type");

--> statement-breakpoint
CREATE INDEX "osha_audit_trail_entity_order_index" ON "osha_audit_trail" USING btree ("entity_id", "created_at");

--> statement-breakpoint
CREATE INDEX "osha_company_information_upkeep_company_id_index" ON "osha_company_information" USING btree ("upkeep_company_id");

--> statement-breakpoint
CREATE INDEX "osha_company_information_year_company_index" ON "osha_company_information" USING btree ("year", "upkeep_company_id");

--> statement-breakpoint
CREATE INDEX "osha_reports_company_archived_index" ON "osha_reports" USING btree ("upkeep_company_id", "archived_at", "id");

--> statement-breakpoint
CREATE INDEX "osha_reports_company_archived_created_index" ON "osha_reports" USING btree ("upkeep_company_id", "archived_at", "created_at");

--> statement-breakpoint
CREATE INDEX "osha_reports_company_year_status_index" ON "osha_reports" USING btree (
    "upkeep_company_id",
    "status",
    "archived_at",
    "created_at"
);

--> statement-breakpoint
ALTER TABLE "osha_company_information" ADD CONSTRAINT "osha_company_information_unique_year_company_index" UNIQUE ("year", "upkeep_company_id");