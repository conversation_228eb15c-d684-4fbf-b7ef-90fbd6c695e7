---
description: Content: Express and tRPC middleware, context enrichment, logging middleware, authentication, CORS, body parsing, performance monitoring Purpose: Guides the implementation of cross-cutting concerns through middleware Key sections: tRPC middleware, Express middleware, context enrichment, logging middleware When to use: Request processing, authentication, logging, performance monitoring
globs: 
alwaysApply: false
---
# Server Middleware Guidelines

## Overview
- Middleware intercepts and processes requests before they reach route handlers
- Used for cross-cutting concerns like logging, authentication, and error handling
- Applied to both Express routes and tRPC procedures

## tRPC Middleware
- Defined in `server/trpc/middleware.ts` and `server/trpc/trpc.ts`
- Key middleware includes:
  - **Logging middleware**: Tracks procedure execution time and logs details
  - **Context enrichment**: Adds user information to the request context

## Express Middleware
- Defined in `server/index.ts`
- Key middleware includes:
  - **Body parsing**: For handling JSON request bodies
  - **CORS**: For handling cross-origin requests
  - **Authentication**: For validating user sessions
  - **Error handling**: For catching and formatting errors

## Context Enrichment
- The `addUserContext` function in `server/trpc/middleware.ts` adds user data to requests
- User data is retrieved from the UpKeep API using request headers
- Makes user data available to all tRPC procedures via `ctx.user`

## Logging Middleware
- Wraps all tRPC procedures via `loggedProcedure`
- Logs the start and end of each procedure call
- Captures execution time, path, and error details
- Helps with debugging and performance monitoring

## Example Middleware Implementation
```typescript
// Logging middleware example
const loggingMiddleware = trpc.middleware(async ({ path, type, next }) => {
  const start = Date.now();
  logger.info('tRPC call started', { path, type });

  const result = await next();

  const durationMs = Date.now() - start;
  logger.info('tRPC call completed', {
    path,
    type,
    durationMs,
    ok: result.ok,
    error: result.ok ? undefined : (result.error?.message ?? result.error),
  });

  return result;
});

// Apply middleware to procedures
export const loggedProcedure = trpc.procedure.use(loggingMiddleware);
```

## Best Practices
- Keep middleware focused on a single responsibility
- Order middleware appropriately (e.g., authentication before authorization)
- Log middleware execution for debugging
- Handle errors properly in middleware
- Consider performance impact of middleware
- Use middleware for cross-cutting concerns only
