import { HazardCategorySchema } from '../settings.types';
import { z } from 'zod';

type HazardItem = {
  hazard_id: string;
  name: string;
  type: z.infer<typeof HazardCategorySchema>;
};

export const HAZARDS: HazardItem[] = [
  // Chemical
  { hazard_id: 'toxic_gas', name: 'Toxic Gas', type: 'chemical' },
  { hazard_id: 'corrosive', name: 'Corrosive', type: 'chemical' },
  { hazard_id: 'flammable', name: 'Flammable', type: 'chemical' },
  { hazard_id: 'acid', name: 'Acid', type: 'chemical' },
  { hazard_id: 'caustic', name: 'Caustic', type: 'chemical' },

  // Electrical
  { hazard_id: 'arc_flash', name: 'Arc Flash', type: 'electrical' },
  { hazard_id: 'shock', name: 'Shock', type: 'electrical' },
  { hazard_id: 'static_electricity', name: 'Static Electricity', type: 'electrical' },
  { hazard_id: 'high_voltage', name: 'High Voltage', type: 'electrical' },

  // Physical
  { hazard_id: 'pinch_point', name: 'Pinch Point', type: 'physical' },
  { hazard_id: 'struck_by_object', name: 'Struck-by Object', type: 'physical' },
  { hazard_id: 'cutting', name: 'Cutting', type: 'physical' },
  { hazard_id: 'crushing', name: 'Crushing', type: 'physical' },
  { hazard_id: 'noise', name: 'Noise', type: 'physical' },

  // Environmental
  { hazard_id: 'heat_stress', name: 'Heat Stress', type: 'environmental' },
  { hazard_id: 'cold_stress', name: 'Cold Stress', type: 'environmental' },
  { hazard_id: 'oxygen_deficiency', name: 'Oxygen Deficiency', type: 'environmental' },
  { hazard_id: 'poor_ventilation', name: 'Poor Ventilation', type: 'environmental' },

  // Ergonomic
  { hazard_id: 'repetitive_motion', name: 'Repetitive Motion', type: 'ergonomic' },
  { hazard_id: 'overexertion', name: 'Overexertion', type: 'ergonomic' },
  { hazard_id: 'awkward_posture', name: 'Awkward Posture', type: 'ergonomic' },
  { hazard_id: 'heavy_lifting', name: 'Heavy Lifting', type: 'ergonomic' },

  // Fall
  { hazard_id: 'fall_from_height', name: 'Fall from Height', type: 'fall' },
  { hazard_id: 'ladder_use', name: 'Ladder Use', type: 'fall' },
  { hazard_id: 'scaffold_work', name: 'Scaffold Work', type: 'fall' },
  { hazard_id: 'slips_and_trips', name: 'Slips and Trips', type: 'fall' },

  // Radiation
  { hazard_id: 'ionizing_radiation', name: 'Ionizing Radiation', type: 'radiation' },
  { hazard_id: 'non_ionizing_radiation', name: 'Non-ionizing Radiation', type: 'radiation' },
  { hazard_id: 'ultraviolet_radiation', name: 'Ultraviolet (UV) Radiation', type: 'radiation' },
  { hazard_id: 'laser_exposure', name: 'Laser Exposure', type: 'radiation' },

  // Biological
  { hazard_id: 'bloodborne_pathogens', name: 'Bloodborne Pathogens', type: 'biological' },
  { hazard_id: 'infectious_agents', name: 'Infectious Agents', type: 'biological' },
  { hazard_id: 'mold_fungi', name: 'Mold / Fungi', type: 'biological' },
  { hazard_id: 'animal_waste_exposure', name: 'Animal Waste / Exposure', type: 'biological' },

  // Noise
  { hazard_id: 'loud_environment', name: 'Loud Environment (≥85 dBA)', type: 'noise' },
  { hazard_id: 'hand_arm_vibration', name: 'Hand-Arm Vibration', type: 'noise' },
  { hazard_id: 'whole_body_vibration', name: 'Whole-Body Vibration', type: 'noise' },

  // Thermal
  { hazard_id: 'hot_surfaces', name: 'Hot Surfaces', type: 'thermal' },
  { hazard_id: 'cold_surfaces', name: 'Cold Surfaces', type: 'thermal' },
  { hazard_id: 'thermal_burns', name: 'Thermal Burns', type: 'thermal' },
  { hazard_id: 'steam_release', name: 'Steam Release', type: 'thermal' },

  // Mechanical
  { hazard_id: 'rotating_machinery', name: 'Rotating Machinery', type: 'mechanical' },
  { hazard_id: 'unguarded_moving_parts', name: 'Unguarded Moving Parts', type: 'mechanical' },
  { hazard_id: 'stored_hydraulic_energy', name: 'Stored Hydraulic Energy', type: 'mechanical' },
  { hazard_id: 'pressurized_equipment', name: 'Pressurized Equipment', type: 'mechanical' },

  // Atmospheric
  { hazard_id: 'oxygen_enriched_atmosphere', name: 'Oxygen-Enriched Atmosphere', type: 'atmospheric' },
  { hazard_id: 'engulfment_risk', name: 'Engulfment Risk', type: 'atmospheric' },
  { hazard_id: 'toxic_atmosphere', name: 'Toxic Atmosphere', type: 'atmospheric' },
  { hazard_id: 'limited_entry_egress', name: 'Limited Entry/Egress', type: 'atmospheric' },
];
