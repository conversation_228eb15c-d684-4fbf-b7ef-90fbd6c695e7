import { InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import {
  oshaAgencyReport,
  oshaAgencyReportTypeEnum,
  oshaAuditTrail,
  oshaCompanyInformation,
  oshaReports,
  oshaTypeEnum,
  shiftsEnum,
  typeOfMedicalCareEnum,
  oshaAuditTrailActionEnum,
  oshaEntityTypeEnum,
} from './schema';
import { PaginationInputSchema, SortInputSchema, IdSchema } from './schema.types';

// ========================================
// SHARED UTILITIES AND REQUEST SCHEMAS
// ========================================

const RequestInfoSchema = {
  ipAddress: z.string().ip('Invalid IP address').optional(),
  userAgent: z.string().min(1, 'User agent is required').optional(),
};

/*
 * VALIDATION STRATEGY EXPLANATION:
 *
 * We use separate validation rules for create vs update operations because:
 * 1. createInsertSchema() makes all fields required by default (based on DB schema)
 * 2. createUpdateSchema() makes all fields optional for partial updates
 * 3. We need to add business logic validation (like string min length, custom rules)
 *
 * For CREATE: Use validation object with createInsertSchema() - enforces required fields
 * For UPDATE: Use validation object with .optional() on each rule with createUpdateSchema() - validates when provided
 *
 * This ensures:
 * - Create operations validate all required fields
 * - Update operations only validate provided fields (dirty fields pattern)
 * - Consistent business rules across both operations
 */

// ========================================
// SHARED OSHA CONSTANTS AND AUDIT TRAIL
// ========================================

export const OSHA_AUDIT_TRAIL_ACTION_MAP: Record<(typeof oshaAuditTrailActionEnum.enumValues)[number], string> = {
  [oshaAuditTrailActionEnum.enumValues[0]]: 'Created',
  [oshaAuditTrailActionEnum.enumValues[1]]: 'Updated',
  [oshaAuditTrailActionEnum.enumValues[2]]: 'Submitted',
  [oshaAuditTrailActionEnum.enumValues[3]]: 'Downloaded',
  [oshaAuditTrailActionEnum.enumValues[4]]: 'Signed',
  [oshaAuditTrailActionEnum.enumValues[5]]: 'Archived',
  [oshaAuditTrailActionEnum.enumValues[6]]: 'Restored',
};

export const OSHA_ENTITY_TYPE_MAP: Record<(typeof oshaEntityTypeEnum.enumValues)[number], string> = {
  [oshaEntityTypeEnum.enumValues[0]]: 'OSHA Report',
  [oshaEntityTypeEnum.enumValues[1]]: 'OSHA Company Information',
  [oshaEntityTypeEnum.enumValues[2]]: 'OSHA Agency Report',
};

export const OshaAuditTrailCreateSchema = createInsertSchema(oshaAuditTrail);

export const OshaAuditTrailSchema = OshaAuditTrailCreateSchema.omit({
  id: true,
  upkeepCompanyId: true,
  details: true,
  createdAt: true,
  createdBy: true,
});

export const OshaAuditTrailProcedureSchema = OshaAuditTrailSchema.omit({
  ipAddress: true,
  userAgent: true,
});

export type OshaAuditTrail = z.infer<typeof OshaAuditTrailSchema>;

// ========================================
// OSHA REPORTS SECTION
// ========================================

export const SHIFTS_MAP: Record<(typeof shiftsEnum.enumValues)[number], string> = {
  [shiftsEnum.enumValues[0]]: 'Day',
  [shiftsEnum.enumValues[1]]: 'Evening',
  [shiftsEnum.enumValues[2]]: 'Night',
  [shiftsEnum.enumValues[3]]: 'Rotating',
  [shiftsEnum.enumValues[4]]: 'Other',
};

export const TYPE_OF_MEDICAL_CARE_MAP: Record<(typeof typeOfMedicalCareEnum.enumValues)[number], string> = {
  [typeOfMedicalCareEnum.enumValues[0]]: 'First Aid',
  [typeOfMedicalCareEnum.enumValues[1]]: 'Medical Treatment',
  [typeOfMedicalCareEnum.enumValues[2]]: 'Emergency Room',
  [typeOfMedicalCareEnum.enumValues[3]]: 'Overnight Hospital Stay',
};

export const OSHA_TYPE_MAP: Record<(typeof oshaTypeEnum.enumValues)[number], string> = {
  [oshaTypeEnum.enumValues[0]]: 'Fatality',
  [oshaTypeEnum.enumValues[1]]: 'Days Away From Work',
  [oshaTypeEnum.enumValues[2]]: 'Job Restriction',
  [oshaTypeEnum.enumValues[3]]: 'Medical Treatment Beyond First Aid',
  [oshaTypeEnum.enumValues[4]]: 'Loss of Consciousness',
  [oshaTypeEnum.enumValues[5]]: 'Significant Injury',
};

// https://www.osha.gov/laws-regs/regulations/standardnumber/1904/1904.7#:~:text=Is%20there%20a%20limit%20to,column%20will%20be%20considered%20adequate.
// By default, OSHA allows up to 180 days away from work or restricted from work.
const OshaReportValidations = {
  employeeJobTitle: z.string().min(1, 'Employee job title is required'),
  employeeWorkLocation: z.string().min(1, 'Employee work location is required'),
  bodyPartInjured: z.string().min(1, 'Body Part Injured is required'),
  typeOfInjury: z.string().min(1, 'Type of Injury is required'),
  typeOfMedicalCare: z.enum(typeOfMedicalCareEnum.enumValues, {
    required_error: 'Type of medical care is required',
  }),
  daysAwayFromWork: z.coerce
    .number({ invalid_type_error: 'Days away from work must be a number' })
    .min(0, 'Days away from work cannot be negative')
    .max(180, 'Days away from work cannot exceed 180 days per OSHA regulations')
    .optional(),
  daysRestrictedFromWork: z.coerce
    .number({ invalid_type_error: 'Days restricted from work must be a number' })
    .min(0, 'Days restricted from work cannot be negative')
    .max(180, 'Days restricted from work cannot exceed 180 days per OSHA regulations')
    .optional(),
  oshaLocationId: z.string({ required_error: 'OSHA location is required' }).cuid2(),
};

// Conditional validations for updates - only validate when fields are provided
const OshaReportUpdateValidations = {
  employeeJobTitle: z.string().min(1, 'Employee job title is required').optional(),
  employeeWorkLocation: z.string().min(1, 'Employee work location is required').optional(),
  bodyPartInjured: z.string().min(1, 'Body Part Injured is required').optional(),
  typeOfInjury: z.string().min(1, 'Type of Injury is required').optional(),
  typeOfMedicalCare: z
    .enum(typeOfMedicalCareEnum.enumValues, {
      required_error: 'Type of medical care is required',
    })
    .optional(),
  daysAwayFromWork: z.coerce
    .number({ invalid_type_error: 'Days away from work must be a number' })
    .min(0, 'Days away from work cannot be negative')
    .max(180, 'Days away from work cannot exceed 180 days per OSHA regulations')
    .optional(),
  daysRestrictedFromWork: z.coerce
    .number({ invalid_type_error: 'Days restricted from work must be a number' })
    .min(0, 'Days restricted from work cannot be negative')
    .max(180, 'Days restricted from work cannot exceed 180 days per OSHA regulations')
    .optional(),
  oshaLocationId: z.string({ required_error: 'OSHA location is required' }).cuid2().optional(),
};

const CreateOshaReportSchema = createInsertSchema(oshaReports, OshaReportValidations);
const UpdateOshaReportSchema = createUpdateSchema(oshaReports, OshaReportUpdateValidations);

// Reusable refinement logic
const OshaReportConditionalRefinement = (
  data: z.infer<typeof CreateOshaReportSchema | typeof UpdateOshaReportSchema>,
  ctx: z.RefinementCtx,
) => {
  if (data.privacyCase === true) {
    data.employeeName = null;
    if (!data.reasonForPrivacyCase?.trim()) {
      ctx.addIssue({
        code: 'custom',
        path: ['reasonForPrivacyCase'],
        message: 'Reason for privacy case is required.',
      });
    }
  } else if (data.privacyCase === false) {
    if (!data.employeeName?.trim()) {
      ctx.addIssue({
        code: 'custom',
        path: ['employeeName'],
        message: 'Employee name is required.',
      });
    }
  }

  // Validation for Hospitalization condition
  // When hospitalized (and not deceased), both fields are required
  // When deceased, no validation is required for these fields as they are disabled
  if (data.wasHospitalized && !data.wasDeceased) {
    if (data.daysAwayFromWork === null || data.daysAwayFromWork === undefined) {
      ctx.addIssue({
        code: 'custom',
        path: ['daysAwayFromWork'],
        message: 'Days away from work is required when hospitalized.',
      });
    }
    if (data.daysRestrictedFromWork === null || data.daysRestrictedFromWork === undefined) {
      ctx.addIssue({
        code: 'custom',
        path: ['daysRestrictedFromWork'],
        message: 'Days restricted from work is required when hospitalized.',
      });
    }
  }

  // OSHA legal maximum validation (180 days is the regulatory limit)
  if (data.daysAwayFromWork && data.daysAwayFromWork > 180) {
    ctx.addIssue({
      code: 'custom',
      path: ['daysAwayFromWork'],
      message: 'Days away from work cannot exceed 180 days per OSHA regulations.',
    });
  }

  if (data.daysRestrictedFromWork && data.daysRestrictedFromWork > 180) {
    ctx.addIssue({
      code: 'custom',
      path: ['daysRestrictedFromWork'],
      message: 'Days restricted from work cannot exceed 180 days per OSHA regulations.',
    });
  }
};

export const CreateOshaReportFormSchema = CreateOshaReportSchema.omit({
  id: true,
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  createdAt: true,
  updatedAt: true,
})
  .extend(RequestInfoSchema)
  .superRefine(OshaReportConditionalRefinement);

export const EditOshaReportFormSchema = UpdateOshaReportSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  createdAt: true,
  updatedAt: true,
})
  .extend(RequestInfoSchema)
  .and(IdSchema)
  .superRefine(OshaReportConditionalRefinement);

export type CreateOshaReportForm = z.infer<typeof CreateOshaReportFormSchema>;
export type EditOshaReportForm = z.infer<typeof EditOshaReportFormSchema>;

export const OshaReportAuditTrailSchema = createSelectSchema(oshaAuditTrail);
export type OshaReportAuditTrail = z.infer<typeof OshaReportAuditTrailSchema>;

export const OshaReportsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  caseType: z.array(z.enum(oshaTypeEnum.enumValues)).optional(),
  includeArchived: z.boolean().optional(),
  year: z.number(),
  oshaLocationId: z.string().optional(),
});

export type OshaReportsFilters = z.infer<typeof OshaReportsFiltersSchema>;

export const ListOshaReportsSchema = PaginationInputSchema.and(SortInputSchema).and(OshaReportsFiltersSchema);

export const ExportOshaReportsSchema = SortInputSchema.and(OshaReportsFiltersSchema);

// ========================================
// OSHA SUMMARY SECTION (Annual Forms & Company Info)
// ========================================

const OshaCompanyInformationValidations = {
  companyEIN: z
    .string()
    .length(9, 'EIN must be exactly 9 digits')
    .regex(/^\d{9}$/, 'EIN must be exactly 9 digits'),
  companyAnnualAverageNumberOfEmployees: z.coerce.number().positive('Number of employees must be positive'),
  companyTotalHoursWorked: z.coerce.number().positive('Total hours worked must be positive'),
  year: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear() + 1, 'Year must be valid'),
  companyNAICSCode: z.coerce.number().int().positive('NAICS code must be a positive integer').min(10).max(999999),
  companyName: z.string().min(1, 'Company name is required'),
  companyFacilityId: z.string().min(1, 'Facility ID is required'),
};

// OSHA Company Information Schema with validation
export const UpsertOshaCompanyInformationSchema = createInsertSchema(
  oshaCompanyInformation,
  OshaCompanyInformationValidations,
)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    archivedBy: true,
  })
  .extend(RequestInfoSchema);

export type UpsertOshaCompanyInformation = z.infer<typeof UpsertOshaCompanyInformationSchema>;
export type OshaCompanyInformation = InferSelectModel<typeof oshaCompanyInformation>;

const OshaSummaryExecutiveCertificationValidations = {
  executiveName: z.string().min(1, 'Executive name is required'),
  executiveTitle: z.string().min(1, 'Executive title is required'),
  dateCertified: z.coerce.date(),
  digitalSignature: z.string().min(1, 'Digital signature is required'),
  id: z.string().cuid2(),
};

export const OshaSummaryExecutiveCertificationSchema = createInsertSchema(
  oshaCompanyInformation,
  OshaSummaryExecutiveCertificationValidations,
)
  .omit({
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    archivedBy: true,
    archived: true,
    companyName: true,
    companyFacilityId: true,
    companyNAICSCode: true,
    companyEIN: true,
    companyAnnualAverageNumberOfEmployees: true,
    companyTotalHoursWorked: true,
    oshaLocationId: true,
    year: true,
  })
  .extend(RequestInfoSchema);

export type OshaSummaryExecutiveCertification = z.infer<typeof OshaSummaryExecutiveCertificationSchema>;

export const GetEstablishInformationSchema = z.object({
  year: z.number(),
  oshaLocationId: z.string().optional(),
});

export type GetEstablishInformation = z.infer<typeof GetEstablishInformationSchema>;

export const OshaSummaryFiltersSchema = z.object({
  year: z.number(),
  oshaLocationId: z.string().optional(),
});

export type OshaSummaryFilters = z.infer<typeof OshaSummaryFiltersSchema>;

// ========================================
// OSHA AGENCY REPORTS SECTION (Serious Incident Reporting)
// ========================================

export const OSHA_AGENCY_REPORT_TYPE_MAP: Record<(typeof oshaAgencyReportTypeEnum.enumValues)[number], string> = {
  [oshaAgencyReportTypeEnum.enumValues[0]]: 'Fatality',
  [oshaAgencyReportTypeEnum.enumValues[1]]: 'Amputation',
  [oshaAgencyReportTypeEnum.enumValues[2]]: 'Hospitalization',
  [oshaAgencyReportTypeEnum.enumValues[3]]: 'Eye Loss',
};

export const AgencyReportValidations = {
  dateOfIncident: z.coerce.date({
    required_error: 'Date of incident is required',
  }),
  oshaLocationId: z.string({
    required_error: 'Location of incident is required',
  }),
  typeOfIncident: z.enum(oshaAgencyReportTypeEnum.enumValues, {
    required_error: 'Type of incident is required',
  }),
  description: z.string().min(1, 'Description is required'),
  companyContactPerson: z.string().min(1, 'Company contact person is required'),
  contactPersonPhone: z
    .string()
    .min(1, 'Contact person phone is required')
    .regex(
      /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
      'Please enter a valid US phone number (e.g., (*************, ************, or ************)',
    ),
  employeesInvolved: z.string().optional(),
  affectedCount: z.coerce
    .number({ invalid_type_error: 'Number of people affected is required' })
    .min(1, 'Number of people affected must be greater than 0'),
};

// Conditional validations for updates - only validate when fields are provided
const AgencyReportUpdateValidations = {
  dateOfIncident: z.coerce
    .date({
      required_error: 'Date of incident is required',
    })
    .optional(),
  oshaLocationId: z
    .string({
      required_error: 'Location of incident is required',
    })
    .optional(),
  typeOfIncident: z
    .enum(oshaAgencyReportTypeEnum.enumValues, {
      required_error: 'Type of incident is required',
    })
    .optional(),
  description: z.string().min(1, 'Description is required').optional(),
  companyContactPerson: z.string().min(1, 'Company contact person is required').optional(),
  contactPersonPhone: z
    .string()
    .min(1, 'Contact person phone is required')
    .regex(
      /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
      'Please enter a valid US phone number (e.g., (*************, ************, or ************)',
    )
    .optional(),
  employeesInvolved: z.string().optional(),
  affectedCount: z.coerce
    .number({ invalid_type_error: 'Number of people affected is required' })
    .min(1, 'Number of people affected must be greater than 0')
    .optional(),
};

const CreateOshaAgencyReportSchema = createInsertSchema(oshaAgencyReport, AgencyReportValidations);
const UpdateOshaAgencyReportSchema = createUpdateSchema(oshaAgencyReport, AgencyReportUpdateValidations);

export const CreateOshaAgencyReportFormSchema = CreateOshaAgencyReportSchema.omit({
  id: true,
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
  updatedAt: true,
  slug: true,
}).extend(RequestInfoSchema);

export const EditOshaAgencyReportFormSchema = UpdateOshaAgencyReportSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
  updatedAt: true,
  slug: true,
}).extend(RequestInfoSchema);

export type CreateOshaAgencyReportForm = z.infer<typeof CreateOshaAgencyReportFormSchema>;
export type EditOshaAgencyReportForm = z.infer<typeof EditOshaAgencyReportFormSchema>;

export const OshaAgencyReportsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  typeOfIncident: z.array(z.enum(oshaAgencyReportTypeEnum.enumValues)).optional(),
  dateRange: z
    .object({
      from: z.date().optional(),
      to: z.date().optional(),
    })
    .optional(),
  year: z.number().optional(),
  oshaLocationId: z.string().optional(),
  showPrivacyCases: z.boolean().optional(),
  includeArchived: z.boolean().optional(),
});

export type OshaAgencyReportsFilters = z.infer<typeof OshaAgencyReportsFiltersSchema>;

export const ListOshaAgencyReportsSchema =
  PaginationInputSchema.and(SortInputSchema).and(OshaAgencyReportsFiltersSchema);

export const ExportOshaAgencyReportsSchema = SortInputSchema.and(OshaAgencyReportsFiltersSchema);
