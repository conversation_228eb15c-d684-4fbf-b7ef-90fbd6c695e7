/**
 * Comprehensive mapping of supported MIME types to their corresponding file extensions.
 * Used for file upload validation and type checking throughout the EHS application.
 *
 * Categories:
 * - Images: JPEG, PNG, HEIC/HEIF formats
 * - Videos: MP4 and QuickTime formats
 * - Documents: PDF, Office documents (Word, Excel, PowerPoint), text files
 *
 * @example
 * ```typescript
 * // Check if a MIME type is supported
 * const isSupported = 'image/jpeg' in SUPPORTED_MIME_TYPES;
 *
 * // Get extensions for a MIME type
 * const jpegExtensions = SUPPORTED_MIME_TYPES['image/jpeg']; // ['.jpg', '.jpeg']
 * ```
 */
export const SUPPORTED_MIME_TYPES = {
  // Images - Common web and mobile formats
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/heic': ['.heic'], // Apple's High Efficiency Image Container
  'image/heif': ['.heic'], // High Efficiency Image File Format

  // Videos - Web-compatible formats
  'video/mp4': ['.mp4'],
  'video/quicktime': ['.mov'], // Apple QuickTime format

  // Documents - Office and text formats
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'], // Word 2007+
  'application/msword': ['.doc'], // Legacy Word format
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'], // Excel 2007+
  'application/vnd.ms-excel': ['.xls'], // Legacy Excel format
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'], // PowerPoint 2007+
  'application/vnd.ms-powerpoint': ['.ppt'], // Legacy PowerPoint format
  'text/csv': ['.csv'], // Standard CSV MIME type
  'application/csv': ['.csv'], // Alternative CSV MIME type
  'text/plain': ['.txt'], // Plain text files
  'application/rtf': ['.rtf'], // Rich Text Format
} as const;

/**
 * Flattened array of all accepted file extensions across all supported MIME types.
 * Duplicates are automatically removed using Set deduplication.
 *
 * Useful for:
 * - File input accept attributes
 * - Client-side validation
 * - General file extension checking
 *
 * @example
 * ```typescript
 * // Use in HTML file input
 * <input type="file" accept={ACCEPTED_FORMATS.join(',')} />
 *
 * // Check if extension is supported
 * const isValid = ACCEPTED_FORMATS.includes('.pdf');
 * ```
 */
export const ACCEPTED_FORMATS = Array.from(new Set(Object.values(SUPPORTED_MIME_TYPES).flat()));

/**
 * Array of file extensions for image and video files only.
 * Filtered from SUPPORTED_MIME_TYPES to include only media files.
 *
 * Use cases:
 * - Media-specific file uploads
 * - Gallery or media library components
 * - Incident photo/video attachments
 *
 * @example
 * ```typescript
 * // Media-only file input
 * <input type="file" accept={ACCEPTED_IMAGE_VIDEO_FORMATS.join(',')} />
 *
 * // Check if file is media
 * const isMediaFile = ACCEPTED_IMAGE_VIDEO_FORMATS.includes(fileExtension);
 * ```
 */
export const ACCEPTED_IMAGE_VIDEO_FORMATS = Array.from(
  new Set(
    Object.entries(SUPPORTED_MIME_TYPES)
      .filter(([mime]) => mime.startsWith('image') || mime.startsWith('video'))
      .flatMap(([_, extensions]) => extensions),
  ),
);

/**
 * Array of file extensions for document files only.
 * Excludes image and video formats, focusing on text-based and office documents.
 *
 * Use cases:
 * - Document upload components
 * - Report and policy attachments
 * - Procedure documentation uploads
 *
 * @example
 * ```typescript
 * // Document-only file input
 * <input type="file" accept={ACCEPTED_DOCUMENT_FORMATS.join(',')} />
 *
 * // Validate document upload
 * const isDocument = ACCEPTED_DOCUMENT_FORMATS.includes(fileExtension);
 * ```
 */
export const ACCEPTED_DOCUMENT_FORMATS = Array.from(
  new Set(
    Object.entries(SUPPORTED_MIME_TYPES)
      .filter(([mime]) => !mime.startsWith('image') && !mime.startsWith('video'))
      .flatMap(([_, extensions]) => extensions),
  ),
);

/**
 * Maps MIME types to human-readable file type categories for UI display and logic.
 * Only includes document MIME types (excludes images and videos).
 *
 * Categories:
 * - 'pdf': PDF documents
 * - 'spreadsheet': Excel files and CSV data
 * - 'word': Microsoft Word documents
 * - 'presentation': PowerPoint presentations
 * - 'text': Plain text and RTF files
 *
 * Use cases:
 * - Displaying file type icons in UI
 * - Categorizing uploaded documents
 * - File type-specific processing logic
 * - Generating user-friendly file descriptions
 *
 * @example
 * ```typescript
 * // Get file type category
 * const documentType = DOCUMENT_TYPE_BY_MIME_TYPE['application/pdf']; // 'pdf'
 *
 * // Display appropriate icon
 * const getFileIcon = (mimeType: string) => {
 *   const documentType = DOCUMENT_TYPE_BY_MIME_TYPE[mimeType];
 *   return documentType ? `${documentType}-icon` : 'generic-file-icon';
 * };
 *
 * // Check if file is spreadsheet
 * const isSpreadsheet = DOCUMENT_TYPE_BY_MIME_TYPE[mimeType] === 'spreadsheet';
 * ```
 */
export const DOCUMENT_TYPE_BY_MIME_TYPE = {
  'application/pdf': 'pdf',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'spreadsheet',
  'application/vnd.ms-excel': 'spreadsheet',
  'text/csv': 'spreadsheet',
  'application/csv': 'spreadsheet',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'word',
  'application/msword': 'word',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'presentation',
  'application/vnd.ms-powerpoint': 'presentation',
  'text/plain': 'text',
  'application/rtf': 'text',
} as const;

export const FILE_TYPE_LABEL_BY_MIME_TYPE = {
  // Images - Common web and mobile formats
  'image/jpeg': 'Photo',
  'image/png': 'Photo',
  'image/heic': 'Photo',
  'image/heif': 'Photo',

  // Videos - Web-compatible formats
  'video/mp4': 'Video',
  'video/quicktime': 'Video',

  // Documents - Office and text formats
  'application/pdf': 'Document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Document',
  'application/msword': 'Document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Document',
  'application/vnd.ms-excel': 'Document',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'Document',
  'application/vnd.ms-powerpoint': 'Document',
  'text/csv': 'Document',
  'application/csv': 'Document',
  'text/plain': 'Document',
  'application/rtf': 'Document',
};
