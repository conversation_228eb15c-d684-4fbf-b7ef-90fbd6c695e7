import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import { controlMeasures, oshaLocations, hazards, hazardCategoryEnum, controlMeasureCategoryEnum } from './schema';
import { PaginationInputSchema, SortInputSchema } from './schema.types';

export const CreateOshaLocationSchema = createInsertSchema(oshaLocations)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
  })
  .extend({
    name: z
      .string({ required_error: 'Name is required' })
      .trim()
      .min(1, 'Name is required')
      .max(255, 'Name must be less than 255 characters'),
  });
export const UpdateOshaLocationSchema = createUpdateSchema(oshaLocations).omit({
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});

export const OshaLocationsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: z
    .object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional(),
    })
    .optional(),
});

export type OshaLocationsFilters = z.infer<typeof OshaLocationsFiltersSchema>;

export const ListOshaLocationsSchema = PaginationInputSchema.and(SortInputSchema).and(OshaLocationsFiltersSchema);

export const ExportOshaLocationsSchema = SortInputSchema.and(OshaLocationsFiltersSchema);

export const SelectOshaLocationSchema = createSelectSchema(oshaLocations);

export const ArchiveOshaLocationSchema = z.object({
  id: z.string(),
});

export type CreateOshaLocation = z.infer<typeof CreateOshaLocationSchema>;
export type ArchiveOshaLocation = z.infer<typeof ArchiveOshaLocationSchema>;
export type UpdateOshaLocation = z.infer<typeof UpdateOshaLocationSchema>;
export type SelectOshaLocation = z.infer<typeof SelectOshaLocationSchema>;

export const ControlMeasuresCreateSchema = createInsertSchema(controlMeasures)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
  })
  .extend({
    name: z.string().min(1, 'Name is required and cannot be empty'),
  });

export const HazardsCreateSchema = createInsertSchema(hazards)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
  })
  .extend({
    name: z.string().min(1, 'Name is required and cannot be empty'),
  });

export const HazardsTypesSchema = z.enum(hazardCategoryEnum.enumValues);
export const ControlMeasuresTypesSchema = z.enum(controlMeasureCategoryEnum.enumValues);
export const HazardCategorySchema = z.enum(hazardCategoryEnum.enumValues);

export const ControlMeasureCategorySchema = z.enum(controlMeasureCategoryEnum.enumValues);

export const BulkHazardsCreateSchema = z.array(HazardsCreateSchema).min(1, 'At least one hazard is required');

export const BulkControlMeasuresCreateSchema = z
  .array(ControlMeasuresCreateSchema)
  .min(1, 'At least one control measure is required');
