import { z } from 'zod';

export const AnalyzeTextSchema = z.object({
  text: z.string().min(1),
  timezone: z.string().optional(),
});

export const AnalyzeDocumentSchema = z.object({
  documentBase64: z.string().min(1),
  filename: z.string().min(1),
  mediaType: z.literal('application/pdf'),
});

export type AnalyzeTextInput = z.infer<typeof AnalyzeTextSchema>;
export type AnalyzeDocumentInput = z.infer<typeof AnalyzeDocumentSchema>;
