import { createInsertSchema } from 'drizzle-zod';
import { z } from 'zod';
import { jha } from './schema';

const JhaInsertSchema = createInsertSchema(jha).omit({
  id: true,
  upkeepCompanyId: true,
  slug: true,
  version: true,
  status: true,
  highestSeverity: true,
  createdBy: true,
  createdAt: true,
});

const JhaStepsCreateSchema = z.object({
  serial: z.number().min(1, 'Serial is required'),
  title: z.string().min(1, 'Title is required'),
  hazardIds: z.array(z.string().cuid2()).nullish(),
  controlMeasureIds: z.array(z.string().cuid2()).nullish(),
  hazardsToCreate: z
    .array(
      z.object({
        name: z.string().min(1, 'Hazard name is required'),
        type: z.string().min(1, 'Hazard type is required'),
      }),
    )
    .nullish(),
  controlMeasuresToCreate: z
    .array(
      z.object({
        name: z.string().min(1, 'Control measure name is required'),
        type: z.string().min(1, 'Control measure type is required'),
      }),
    )
    .nullish(),
  severity: z.number().min(1, 'Severity is required'),
  likelihood: z.number().min(1, 'Likelihood is required'),
});

export const CreateFullJhaSchema = z.object({
  jha: JhaInsertSchema,
  steps: z.array(JhaStepsCreateSchema).min(1, 'At least one step is required'),
});

export type CreateFullJhaType = z.infer<typeof CreateFullJhaSchema>;
