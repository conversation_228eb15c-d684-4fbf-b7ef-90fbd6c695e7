import { RouterOutputs } from '@shared/router.types';
import { isBefore } from 'date-fns';
import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import {
  accessPoints,
  accessPointStatusEnum,
  auditTrail,
  auditTrailActionEnum,
  capaEffectivenessStatusEnum,
  capaPriorityEnum,
  capas,
  capaTagsEnum,
  capaTypeEnum,
  comments,
  controlMeasureCategoryEnum,
  entityTypeEnum,
  events,
  files,
  fileStatusEnum,
  hazardCategoryEnum,
  rcaMethodEnum,
  reportTypeEnum,
  roleEnum,
  rootCauseEnum,
  severityEnum,
  statusEnum,
} from './schema';
import { UserAccountTypeSchema, UserPermissionSchema } from './user-permissions';

// ========================================
// COMMON TYPES AND INTERFACES
// ========================================

export const featureFlags = ['webEHS'] as const;
export type FeatureFlag = (typeof featureFlags)[number];
export type FeatureFlags = Record<FeatureFlag, boolean>;

export interface PaginatedResponse<T> {
  noResults: boolean;
  result: T[];
  nextCursor: number | undefined;
}

export interface StatusConfig {
  label: string;
  color: string;
  backgroundColor: string;
}

export interface BulkImportResult {
  total: number;
  created: number;
  failed: number;
  failedItems: Array<{
    name: string;
    location: string;
    reason: string;
  }>;
}

export interface BulkImportSummary {
  successCount: number;
  failureCount: number;
  totalProcessed: number;
  failures: Array<{
    name: string;
    location: string;
    reason: string;
  }>;
}

export type RawParseObject = {
  __type?: string;
  className?: string;
  objectId: string;
};

// ========================================
// COMMON SCHEMAS
// ========================================

// Cursor-based Pagination Schema for useInfiniteQuery
export const PaginationInputSchema = z.object({
  cursor: z.number().min(0).default(0).optional(),
  limit: z.number().min(1).max(100).default(50).optional(),
});

export type PaginationInput = z.infer<typeof PaginationInputSchema>;

// Generic Sorting Schemas and Types
export const SortInputSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

export type SortInput = z.infer<typeof SortInputSchema>;

export const IdSchema = z.object({
  id: z.string().cuid2(),
});

export const IdArraySchema = z.array(z.string().cuid2());

export const UpkeepCompanyIdSchema = z.object({
  upkeepCompanyId: z.string().min(1).max(10),
});

export const PublicSearchSchema = z
  .object({
    upkeepCompanyId: z.string().min(1, 'Required'),
    locationId: z.string().optional(),
    userAccountType: UserAccountTypeSchema.optional(),
    objectId: z.union([z.string(), z.array(z.string())]).optional(),
    mustIncludeObjectIds: z.array(z.string()).optional(),
    search: z.string().optional(),
  })
  .and(PaginationInputSchema);

export type PublicSearchInput = z.infer<typeof PublicSearchSchema>;

export const TransientFileSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  url: z.string(),
  type: z.string(),
  size: z.number(),
  file: z.instanceof(File).optional(),
});

// ========================================
// USER SCHEMAS AND TYPES
// ========================================

export const UserSchema = z.object({
  id: z.string(),
  username: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  fullName: z.string(),
  upkeepCompanyId: z.string(),
  role: UserAccountTypeSchema,
  permissions: UserPermissionSchema,
  featureFlags: z.record(z.enum(featureFlags), z.boolean()),
  createdAt: z.string().optional(),
  lastLoginAt: z.string().optional(),
  hasEhsEnabled: z.boolean(),
});

export const UserPublicSchema = UserSchema.pick({
  id: true,
  firstName: true,
  lastName: true,
  username: true,
}).extend({
  lastName: z.string().optional(),
  fullName: z.string().optional(),
  email: z.string().optional(),
});

export type User = z.infer<typeof UserSchema>;
export type UserPublic = z.infer<typeof UserPublicSchema>;

// ========================================
// ENUM SCHEMAS AND MAPPINGS
// ========================================

export const ReportTypeSchema = z.enum(reportTypeEnum.enumValues);
export const SeveritySchema = z.enum(severityEnum.enumValues);
export const RootCauseSchema = z.enum(rootCauseEnum.enumValues);
export const StatusSchema = z.enum(statusEnum.enumValues);
export const RoleSchema = z.enum(roleEnum.enumValues);
export const HazardCategorySchema = z.enum(hazardCategoryEnum.enumValues);
export const ControlMeasureCategorySchema = z.enum(controlMeasureCategoryEnum.enumValues);
export const FileStatusSchema = z.enum(fileStatusEnum.enumValues);
export const AccessPointStatusSchema = z.enum(accessPointStatusEnum.enumValues);

// Specific schema for file status updates
export const FileUpdateStatusSchema = z.enum(['completed', 'failed'] as const);

// Status Mappings
export const STATUS_MAP: Record<(typeof statusEnum.enumValues)[number], string> = {
  [statusEnum.enumValues[0]]: 'Open',
  [statusEnum.enumValues[1]]: 'In Review',
  [statusEnum.enumValues[2]]: 'Closed',
};

export const SEVERITY_MAP: Record<(typeof severityEnum.enumValues)[number], string> = {
  [severityEnum.enumValues[0]]: 'Low',
  [severityEnum.enumValues[1]]: 'Medium',
  [severityEnum.enumValues[2]]: 'High',
  [severityEnum.enumValues[3]]: 'Critical',
};

export const REPORT_TYPE_MAP: Record<(typeof reportTypeEnum.enumValues)[number], string> = {
  [reportTypeEnum.enumValues[0]]: 'Incident',
  [reportTypeEnum.enumValues[1]]: 'Near Miss',
  [reportTypeEnum.enumValues[2]]: 'Observation',
  [reportTypeEnum.enumValues[3]]: 'Customer Incident',
};

export const CATEGORY_MAP: Record<(typeof hazardCategoryEnum.enumValues)[number], string> = {
  [hazardCategoryEnum.enumValues[0]]: 'Chemical',
  [hazardCategoryEnum.enumValues[1]]: 'Electrical',
  [hazardCategoryEnum.enumValues[2]]: 'Physical',
  [hazardCategoryEnum.enumValues[3]]: 'Environmental',
  [hazardCategoryEnum.enumValues[4]]: 'Ergonomic',
  [hazardCategoryEnum.enumValues[5]]: 'Fall',
  [hazardCategoryEnum.enumValues[6]]: 'Biological',
  [hazardCategoryEnum.enumValues[7]]: 'Fire',
  [hazardCategoryEnum.enumValues[8]]: 'Mechanical',
  [hazardCategoryEnum.enumValues[9]]: 'Radiation',
  [hazardCategoryEnum.enumValues[10]]: 'Noise',
  [hazardCategoryEnum.enumValues[11]]: 'Thermal',
  [hazardCategoryEnum.enumValues[12]]: 'Atmospheric',
  [hazardCategoryEnum.enumValues[13]]: 'Spill',
  [hazardCategoryEnum.enumValues[14]]: 'Transportation',
  [hazardCategoryEnum.enumValues[15]]: 'Violence',
  [hazardCategoryEnum.enumValues[16]]: 'Other',
};

export const RCA_METHOD_MAP: Record<(typeof rcaMethodEnum.enumValues)[number], string> = {
  [rcaMethodEnum.enumValues[0]]: '5 Whys',
  [rcaMethodEnum.enumValues[1]]: 'Fishbone Diagram',
  [rcaMethodEnum.enumValues[2]]: 'Fault Tree Analysis',
  [rcaMethodEnum.enumValues[3]]: 'Other Method',
  [rcaMethodEnum.enumValues[4]]: 'Not Selected',
};

export const ROOT_CAUSE_MAP: Record<(typeof rootCauseEnum.enumValues)[number], string> = {
  [rootCauseEnum.enumValues[0]]: 'Human Error',
  [rootCauseEnum.enumValues[1]]: 'Equipment Failure',
  [rootCauseEnum.enumValues[2]]: 'Environmental',
  [rootCauseEnum.enumValues[3]]: 'Procedural',
  [rootCauseEnum.enumValues[4]]: 'Other',
};

export const ENTITY_TYPE_MAP: Record<'event' | 'capa', string> = {
  event: 'Safety Event',
  capa: 'CAPA',
};

export const ACCESS_POINT_STATUS_MAP: Record<(typeof accessPointStatusEnum.enumValues)[number], string> = {
  [accessPointStatusEnum.enumValues[0]]: 'Active',
  [accessPointStatusEnum.enumValues[1]]: 'Inactive',
};

// Status Style Configurations
export const STATUS_STYLES: Record<(typeof statusEnum.enumValues)[number], StatusConfig> = {
  [statusEnum.enumValues[0]]: {
    label: 'Open',
    backgroundColor: '#e0e7ff',
    color: '#2563eb',
  },
  [statusEnum.enumValues[1]]: {
    label: 'In Review',
    backgroundColor: '#fef9c3',
    color: '#b45309',
  },
  [statusEnum.enumValues[2]]: {
    label: 'Closed',
    backgroundColor: '#dcfce7',
    color: '#166534',
  },
};

export const SEVERITY_STYLES: Record<(typeof severityEnum.enumValues)[number], StatusConfig> = {
  [severityEnum.enumValues[0]]: {
    label: 'Low',
    backgroundColor: '#dcfce7',
    color: '#15803d',
  },
  [severityEnum.enumValues[1]]: {
    label: 'Medium',
    backgroundColor: '#fef3c7',
    color: '#d97706',
  },
  [severityEnum.enumValues[2]]: {
    label: 'High',
    backgroundColor: '#fee2e2',
    color: '#b91c1c',
  },
  [severityEnum.enumValues[3]]: {
    label: 'Critical',
    backgroundColor: '#7f1d1d',
    color: '#fecaca',
  },
};

// ========================================
// EVENTS SCHEMAS AND TYPES
// ========================================

export const EventValidations = {
  title: z
    .string({
      required_error: 'Title is required',
    })
    .min(1, {
      message: 'Title is required',
    }),
  reportedAt: z.coerce
    .date({ required_error: 'Date & Time is required' })
    .refine((date) => isBefore(date, new Date()), {
      message: 'Reported at must be in the past',
    }),
  type: z.enum(reportTypeEnum.enumValues, {
    message: 'Type is required',
  }),
  status: z.enum(statusEnum.enumValues, {
    message: 'Status is required',
  }),
  customerPhoneNumber: z
    .string()
    .regex(
      /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
      'Please enter a valid US phone number (e.g., (*************, ************, or ************)',
    )
    .optional()
    .or(z.literal('')),
};

export const CreateEventSchema = createInsertSchema(events, EventValidations);
export const UpdateEventSchema = createUpdateSchema(events);
export const SelectEventSchema = createSelectSchema(events);

export type CreateEvent = InferInsertModel<typeof events>;
export type Event = InferSelectModel<typeof events>;

export const CreateEventFormSchema = CreateEventSchema.omit({
  id: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  archived: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
  status: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
});

export const EditEventFormSchema = UpdateEventSchema.omit({
  aiConfidenceScore: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
  id: z.string().cuid2(),
});

export const CreateEventFormPublicSchema = CreateEventFormSchema.extend({
  name: z.string({ required_error: 'Name is required' }).min(1, { message: 'Name is required' }),
  email: z.string({ required_error: 'Invalid email address' }).email({ message: 'Invalid email address' }),
  accessPointCreatedBy: z.string(),
  reportedBy: z.string().optional(),
}).and(UpkeepCompanyIdSchema);

export const EventsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  status: z.array(z.enum(statusEnum.enumValues)).optional(),
  type: z.array(z.enum(reportTypeEnum.enumValues)).optional(),
  severity: z.array(z.enum(severityEnum.enumValues)).optional(),
  oshaReportable: z.boolean().optional(),
  includeArchived: z.boolean().optional(),
  locationIds: z.array(z.string()).optional(),
});

export type EventsFilters = z.infer<typeof EventsFiltersSchema>;

export const ListEventSchema = PaginationInputSchema.and(SortInputSchema).and(EventsFiltersSchema);

export const ExportEventsSchema = SortInputSchema.and(EventsFiltersSchema);

// Note: LocationSchema will be defined in the ASSET AND LOCATION section below

// ========================================
// CAPA SCHEMAS AND TYPES
// ========================================

export const CAPA_PRIORITY_MAP: Record<(typeof capaPriorityEnum.enumValues)[number], string> = {
  [capaPriorityEnum.enumValues[0]]: 'High',
  [capaPriorityEnum.enumValues[1]]: 'Medium',
  [capaPriorityEnum.enumValues[2]]: 'Low',
};

export const CAPA_TYPE_MAP: Record<(typeof capaTypeEnum.enumValues)[number], string> = {
  [capaTypeEnum.enumValues[0]]: 'Corrective',
  [capaTypeEnum.enumValues[1]]: 'Preventive',
  [capaTypeEnum.enumValues[2]]: 'Both',
};

export const CAPA_TAGS_MAP: Record<(typeof capaTagsEnum.enumValues)[number], string> = {
  [capaTagsEnum.enumValues[0]]: 'Training',
  [capaTagsEnum.enumValues[1]]: 'Policy',
  [capaTagsEnum.enumValues[2]]: 'Hazard',
  [capaTagsEnum.enumValues[3]]: 'Equipment',
  [capaTagsEnum.enumValues[4]]: 'Procedure',
  [capaTagsEnum.enumValues[5]]: 'Personnel',
};

export const CAPA_EFFECTIVENESS_STATUS_MAP: Record<(typeof capaEffectivenessStatusEnum.enumValues)[number], string> = {
  [capaEffectivenessStatusEnum.enumValues[0]]: 'Effective',
  [capaEffectivenessStatusEnum.enumValues[1]]: 'Partial',
  [capaEffectivenessStatusEnum.enumValues[2]]: 'Not Effective',
  [capaEffectivenessStatusEnum.enumValues[3]]: 'Not Evaluated',
};

export const CapaValidations = {
  title: z.string({ required_error: 'Title is required' }).min(1, 'Title must be at least 1 character'),
  ownerId: z.string({ required_error: 'Owner is required' }).min(1),
  type: z.enum(capaTypeEnum.enumValues, {
    message: 'Type is required',
  }),
  rcaFindings: z
    .string({ required_error: 'RCA findings are required' })
    .min(1, 'RCA findings must be at least 1 character'),
  priority: z.enum(capaPriorityEnum.enumValues, {
    message: 'Priority is required',
  }),
  status: z.enum(statusEnum.enumValues, {
    message: 'Status is required',
  }),
  actionsToAddress: z
    .string({ required_error: 'Proposed actions are required' })
    .min(1, 'Proposed actions must be at least 1 character'),
};

export const CreateCapasSchema = createInsertSchema(capas, CapaValidations);
export const UpdateCapasSchema = createUpdateSchema(capas);
export const SelectCapasSchema = createSelectSchema(capas);

export type CreateCapa = z.infer<typeof CreateCapasSchema>;
export type Capa = InferSelectModel<typeof capas>;

export const CreateCapasFormSchema = CreateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiConfidenceScore: true,
  archived: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
});

export type CreateCapasForm = z.infer<typeof CreateCapasFormSchema>;

export const EditCapasFormSchema = UpdateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiSuggestedAction: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
  id: z.string().cuid2(),
});

export type EditCapasForm = z.infer<typeof EditCapasFormSchema>;

export const CapasFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  status: z.array(z.enum(statusEnum.enumValues)).optional(),
  type: z.array(z.enum(capaTypeEnum.enumValues)).optional(),
  priority: z.array(z.enum(capaPriorityEnum.enumValues)).optional(),
  owner: z.array(z.string()).optional(),
  dueDateRange: z
    .object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional(),
    })
    .optional(),
  includeArchived: z.boolean().optional(),
  tags: z.array(z.enum(capaTagsEnum.enumValues)).optional(),
  eventId: z.string().cuid2().optional(),
});

export type CapasFilters = z.infer<typeof CapasFiltersSchema>;

export const ListCapasSchema = PaginationInputSchema.and(SortInputSchema).and(CapasFiltersSchema);

export const ExportCapasSchema = SortInputSchema.and(CapasFiltersSchema);

// ========================================
// COMMENTS SCHEMAS AND TYPES
// ========================================

export const CreateCommentsSchema = createInsertSchema(comments);

export const CreateCommentFormSchema = CreateCommentsSchema.omit({
  id: true,
  createdAt: true,
  capaId: true,
  eventId: true,
  updatedAt: true,
  upkeepCompanyId: true,
  userId: true,
}).and(
  z.object({
    entityId: z.string(),
    entityType: z.enum(['event', 'capa']),
    entitySlug: z.string(),
    entityTitle: z.string(),
    status: z.enum(statusEnum.enumValues),
  }),
);

export const ListCommentsSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(['event', 'capa']),
  options: z
    .object({
      limit: z.number().optional(),
      offset: z.number().optional(),
    })
    .optional(),
});

export const UpdateCommentsSchema = createUpdateSchema(comments);
export const SelectCommentsSchema = createSelectSchema(comments);

export type CreateComments = InferInsertModel<typeof comments>;
export type Comments = InferSelectModel<typeof comments>;

// ========================================
// AUDIT TRAIL SCHEMAS AND TYPES
// ========================================

export const CreateAuditTrailSchema = createInsertSchema(auditTrail);
export const UpdateAuditTrailSchema = createUpdateSchema(auditTrail);
export const SelectAuditTrailSchema = createSelectSchema(auditTrail);

export const GetAuditTrailSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(entityTypeEnum.enumValues),
});

export type CreateAuditTrail = InferInsertModel<typeof auditTrail>;
export type AuditTrail = InferSelectModel<typeof auditTrail>;

// ========================================
// FILES SCHEMAS AND TYPES
// ========================================

export const CreateFileSchema = createInsertSchema(files);
export const UpdateFileSchema = createUpdateSchema(files);
export const SelectFileSchema = createSelectSchema(files);

export const UpdateFilePublicSchema = UpdateFileSchema.and(UpkeepCompanyIdSchema);

export const GetPresignedUrlInputSchema = CreateFileSchema.pick({
  fileName: true,
  fileSize: true,
  mimeType: true,
  entityType: true,
  entityId: true,
}).extend({
  fileSize: z.number().positive('File size must be positive'),
});

export const GetPresignedUrlInputPublicSchema = GetPresignedUrlInputSchema.and(UpkeepCompanyIdSchema);

export const ListFilesSchema = z.object({
  entityType: z.enum(entityTypeEnum.enumValues),
  entityId: z.string().min(1),
  status: FileStatusSchema.optional(),
});

// ========================================
// ASSET AND LOCATION SCHEMAS AND TYPES
// ========================================

export const AssetSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
});

export type Asset = z.infer<typeof AssetSchema>;

export const AssetSearchInputSchema = PaginationInputSchema.extend({
  locationId: z.string().optional(),
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
});

export type AssetSearchInput = z.infer<typeof AssetSearchInputSchema>;

export const LocationSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export type Location = z.infer<typeof LocationSchema>;

export const LocationSearchInputSchema = PaginationInputSchema.extend({
  restrictionsLevel: z.number().optional(),
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
});

export type LocationSearchInput = z.infer<typeof LocationSearchInputSchema>;

export type UpkeepAsset = {
  id: string;
  Name: string;
  Description: string;
  objectLocation?: { stringName: string };
};

// Event Notification Schemas (defined here after LocationSchema is available)
export const EventNotificationSchema = SelectEventSchema.omit({
  type: true,
  category: true,
  archived: true,
  immediateActions: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  deletedAt: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  location: LocationSchema.optional(),
  action: z.enum(auditTrailActionEnum.enumValues).optional(),
  teamMembersToNotify: z.array(UserSchema.pick({ id: true, fullName: true, email: true })).optional(),
});

export const IncidentNotificationPublicSchema = SelectEventSchema.omit({
  type: true,
  category: true,
  archived: true,
  immediateActions: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  deletedAt: true,
  upkeepCompanyId: true,
  updatedAt: true,
  teamMembersToNotify: true,
}).extend({
  location: LocationSchema.optional(),
  action: z.enum(auditTrailActionEnum.enumValues).optional(),
});

// ========================================
// WORK ORDER SCHEMAS AND TYPES
// ========================================

export type WorkOrder = {
  id: string;
  workOrderNumber: string;
  title: string;
  currentStatus: string;
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  assignedTo: string;
  locationId: string;
  assetId: string;
  assetName: string;
};

export const CreateWorkOrderFromCapaSchema = CreateCapasSchema.pick({
  title: true,
  actionsToAddress: true,
  priority: true,
  dueDate: true,
  locationId: true,
  assetId: true,
})
  .extend({
    id: z.string().min(1, 'CAPA ID is required'),
    slug: z.string().min(1, 'Slug is required'),
    userAssignedTo: z.string().optional(),
  })
  .extend({ dueDate: z.string().min(1, 'Due date is required') }); // avoid conflicts with the dueDate type from the schema

export type CreateWorkOrderFromCapaInput = z.infer<typeof CreateWorkOrderFromCapaSchema>;

export const WorkOrderSearchInputSchema = PaginationInputSchema.extend({
  capaId: z.array(z.string()),
  sort: z.string().optional().default('createdAt DESC'),
});

export const CountWorkOrdersByCapaIdSchema = z.object({
  capaId: z.array(z.string()),
});

export type WorkOrderSearchInput = z.infer<typeof WorkOrderSearchInputSchema>;
export type CountWorkOrdersByCapaIdInput = z.infer<typeof CountWorkOrdersByCapaIdSchema>;

export type CreateWorkOrderParams = {
  mainDescription: string; // Title
  note: string; // Description
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder?: string;
  objectAsset?: string;
  userAssignedTo?: string;
  capaId: string;
};

// ========================================
// QUEUE JOB SCHEMAS AND TYPES
// ========================================

// HTTP Headers type for job payloads
export type QueueHeaders = Record<string, string | string[] | undefined>;

// CAPA Update Notification Job Payload - matches service signature exactly
export type CapaUpdateNotificationJobPayload = {
  capa: RouterOutputs['capa']['update'];
  user: User;
  headers: QueueHeaders;
  needPartialCheck: boolean;
  actionPrefix?: string;
};

// CAPA Assigned Notification Job Payload - matches service signature exactly
export type CapaAssignedNotificationJobPayload = {
  capa: RouterOutputs['capa']['create'];
  user: User;
  headers: QueueHeaders;
  needPartialCheck: boolean;
};

// Comment Mention Notification Job Payload - matches service signature exactly
export type CommentMentionNotificationJobPayload = {
  newComment: Comments;
  savedMentions:
    | Array<{
        id: string;
        createdAt: Date;
        userId: string;
        commentId: string;
        position: number | null;
        mentionText: string | null;
      }>
    | undefined;
  user: User;
  headers: QueueHeaders;
  input: z.infer<typeof CreateCommentFormSchema>;
};

// Event Public Create Notification Job Payload - matches service signature exactly
export type EventPublicCreateNotificationJobPayload = {
  createdEvent: RouterOutputs['event']['createPublic'];
  headers: QueueHeaders;
  upkeepCompanyId: string;
  reporterInfo: {
    email: string;
    fullName: string;
    id?: string;
  };
  teamMembersToNotify: {
    email: string;
    fullName: string;
    id?: string;
  }[];
};

// Event Create Notification Job Payload - matches service signature exactly
export type EventCreateNotificationJobPayload = {
  createdEvent: RouterOutputs['event']['create'];
  user: User;
  headers: QueueHeaders;
  teamMembersToNotify?: string[];
};

// Event Update Notification Job Payload - matches service signature exactly
export type EventUpdateNotificationJobPayload = {
  updatedEvent: Partial<Event>;
  user: User;
  headers: QueueHeaders;
  actionPrefix?: string;
};

// ========================================
// ACCESS POINTS SCHEMAS AND TYPES
// ========================================

export const CreateAccessPointSchema = createInsertSchema(accessPoints);
export const UpdateAccessPointSchema = createUpdateSchema(accessPoints);
export const SelectAccessPointSchema = createSelectSchema(accessPoints);

export const CreateAccessPointFormSchema = CreateAccessPointSchema.pick({
  name: true,
  locationId: true,
}).extend({
  name: z.string().min(1, 'Name is required'),
});

export type CreateAccessPoint = InferInsertModel<typeof accessPoints>;
export type AccessPoint = InferSelectModel<typeof accessPoints>;

export const AccessPointsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  locationId: z.array(z.string()).optional(),
  status: z.array(z.enum(accessPointStatusEnum.enumValues)).optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: z
    .object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional(),
    })
    .optional(),
});

export type AccessPointsFilters = z.infer<typeof AccessPointsFiltersSchema>;

export const ListAccessPointsSchema = PaginationInputSchema.and(SortInputSchema).and(AccessPointsFiltersSchema);

export const ExportAccessPointsSchema = SortInputSchema.and(AccessPointsFiltersSchema);

export const BulkCreateAccessPointInputSchema = z.object({
  name: z.string().min(1),
  location: z.string().min(1),
});

export type BulkCreateAccessPointInput = z.infer<typeof BulkCreateAccessPointInputSchema>;

// Helper function to convert API response to client format
export function convertBulkImportResult(apiResult: BulkImportResult): BulkImportResult {
  return {
    total: apiResult.total || 0,
    created: apiResult.created || 0,
    failed: apiResult.failed || 0,
    failedItems: apiResult.failedItems || [],
  };
}
