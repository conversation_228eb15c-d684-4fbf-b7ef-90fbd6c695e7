import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { AnalyzingLoading } from '@/components/composite/analyzing-loading';
import { AsyncAssetMultiSelect } from '@/components/composite/async-asset-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserMultiSelect } from '@/components/composite/async-user-multi-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { SuccessModal } from '@/components/composite/success-modal';
import { VoiceInput, VoiceInputRef } from '@/components/composite/voice-input';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { formatPhoneNumber } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { hazardCategoryEnum, reportTypeEnum, severityEnum } from '@shared/schema';
import { CATEGORY_MAP, CreateEventFormSchema, REPORT_TYPE_MAP, SEVERITY_MAP } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import axios from 'axios';
import { Info, Sparkles } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect } from 'wouter';
import { z } from 'zod';

type CreatedEvent = RouterOutputs['event']['create'];

const FormSchema = CreateEventFormSchema;

export default function NewEvent() {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [createdEvent, setCreatedEvent] = useState<CreatedEvent>();
  // Track if AI voice assistance was used
  const [isVoiceUsed, setIsVoiceUsed] = useState<boolean>(false);
  // Track when voice analysis completed for abandonment tracking
  const [voiceCompletedAt, setVoiceCompletedAt] = useState<number | undefined>();
  // Track last field user interacted with for abandonment tracking
  const [lastFieldInteracted, setLastFieldInteracted] = useState<string>('');

  // Ref for VoiceInput component to control transcript reset
  const voiceInputRef = useRef<VoiceInputRef>(null);

  const utils = trpc.useUtils();

  const { hasPermission } = usePermissions();
  const { track } = useAnalytics();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      type: reportTypeEnum.enumValues[0],
      title: '',
      reportedAt: new Date(),
      category: undefined,
      severity: undefined,
      description: '',
      immediateActions: '',
      assetIds: [],
      locationId: undefined,
      teamMembersToNotify: [],
      customerName: '',
      customerPhoneNumber: '',
      customerAddress: '',
    },
    mode: 'onSubmit',
  });

  const { mutateAsync: analyze, isPending: isAnalyzing } = trpc.ai.analyzeEvent.useMutation({
    onSuccess: (data) => {
      console.log('Analysis complete', data);
      toast.success('Analysis complete', {
        description: 'We have analyzed your safety event and filled out the form for you.',
      });
    },
    onError: (error) => {
      console.error('Error analyzing safety event', error);
      toast.error('Error analyzing safety event', {
        description: 'There was a problem analyzing your safety event. Please try again.',
      });
    },
  });

  const { mutateAsync: createEvent } = trpc.event.create.useMutation({
    onSuccess: () => {
      utils.event.list.invalidate();
    },
    onError: (error) => {
      console.error('Error creating safety event', error);
      toast.error('Error creating safety event', {
        description: 'There was a problem creating your safety event. Please try again.',
      });
    },
  });

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();

  const { mutateAsync: updateFile } = trpc.file.update.useMutation();

  // Track form abandonment when user leaves without submitting
  useEffect(() => {
    const formStartTime = Date.now();

    const handleBeforeUnload = () => {
      const formDuration = Math.floor((Date.now() - formStartTime) / 1000);

      // Check for voice-to-form abandonment (voice was used successfully but form abandoned)
      if (isVoiceUsed && voiceCompletedAt && !isSubmitting) {
        const voiceDuration = Date.now() - voiceCompletedAt;
        track(ANALYTICS_EVENTS.EVENT.VOICE_TO_FORM_ABANDONED, {
          duration_ms: voiceDuration,
          last_field_interacted: lastFieldInteracted,
        });
      }
      // General form abandonment tracking
      else if (formDuration > 30 && !isSubmitting) {
        track(ANALYTICS_EVENTS.EVENT.FORM_ABANDONED, {
          duration_on_form_seconds: formDuration,
        });
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isSubmitting, isVoiceUsed, voiceCompletedAt, lastFieldInteracted]);

  useEffect(() => {
    if (form.watch('locationId')) {
      form.setValue('assetIds', []);
      utils.asset.search.invalidate();
    }
  }, [form.watch('locationId')]);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    setIsSubmitting(true);

    try {
      const createdEvent = await createEvent({ ...values, media: undefined });

      setCreatedEvent(createdEvent);

      // Track event creation
      track(ANALYTICS_EVENTS.EVENT.FORM_SUBMITTED, {
        event_id: createdEvent?.id,
        report_type: REPORT_TYPE_MAP[values.type],
        severity_level: values.severity
          ? ((values.severity.charAt(0).toUpperCase() + values.severity.slice(1)) as
              | 'Low'
              | 'Medium'
              | 'High'
              | 'Critical')
          : undefined,
        location: values.locationId || '',
        asset: values.assetIds?.length ? values.assetIds[0] : '',
        hazard_category: values.category || '',
        media_attached_count: values.media?.length || 0,
        is_ai_assisted: isVoiceUsed,
        is_prefilled_from_qr: false,
      });

      for (const item of values.media ?? []) {
        const result = await getPresignedUrl({
          fileName: item.name,
          fileSize: item.size,
          mimeType: item.type,
          entityType: 'event',
          entityId: createdEvent?.id,
        });

        try {
          await axios.put(result.presignedUrl, item.file, {
            headers: {
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast.error('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      toast.success('Safety Event created', {
        description: 'Your safety event has been created successfully',
      });

      // Reset form
      form.reset();

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      // Reset the voice input transcript
      voiceInputRef.current?.resetTranscript();

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Show the success modal and let user decide when to close it
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error submitting safety event', error);
      // Track form validation failed
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      track(ANALYTICS_EVENTS.EVENT.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
        error_message: errorMessage,
      });
      toast.error('Error reporting safety event', {
        description: 'There was a problem submitting your safety event. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handler for "Report Another Safety Event" action
  const handleReportAnother = () => {
    form.reset();
    setAutoFilledFields([]);

    // Reset the voice input transcript
    voiceInputRef.current?.resetTranscript();

    setShowSuccessModal(false);
  };

  // Track which fields were auto-filled for animations
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);

  // Handle the extracted data from voice analysis
  const handleVoiceAnalysis = async (text: string) => {
    // Clear any previous animations
    setAutoFilledFields([]);

    // Set AI assistance flag when voice analysis starts
    setIsVoiceUsed(true);

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const startTime = Date.now();

    // Track voice analysis start
    track(ANALYTICS_EVENTS.EVENT.VOICE_STARTED, {});

    try {
      const data = await analyze({ text, timezone });
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Count how many fields were populated
      let fieldsPopulated: number = 0;

      // Track each field that gets populated
      const trackFieldPopulation = (fieldName: string, value: string | Date | undefined) => {
        if (value) {
          fieldsPopulated++;

          // Track individual field AI population
          track(ANALYTICS_EVENTS.EVENT.FIELD_AI_POPULATED, {
            field_name: fieldName,
          });
        }
      };

      // Animate each field separately with staggered timing
      const animateField = (
        field: keyof z.infer<typeof FormSchema>,
        value: string | Date | undefined,
        delay: number,
      ) => {
        setTimeout(() => {
          form.setValue(field, value);
          trackFieldPopulation(field, value);
          setAutoFilledFields((prev) => [...prev, field]);
        }, delay);
      };

      // Base delay between animations and running counter
      const baseDelay = 300;
      let currentDelay = 300;

      // Apply animations in sequence for first section of form
      if (data?.type) {
        animateField('type', data.type, currentDelay);
        currentDelay += baseDelay;
      }

      if (data?.title) {
        animateField('title', data.title, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'category' in data && data.category) {
        animateField('category', data.category, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'reportedAt' in data && data.reportedAt) {
        animateField('reportedAt', data.reportedAt, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'severity' in data && data.severity) {
        animateField('severity', data.severity, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'description' in data && data.description) {
        animateField('description', data.description, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'immediateActions' in data && data.immediateActions) {
        animateField('immediateActions', data.immediateActions, currentDelay);
        currentDelay += baseDelay;
      }

      // Track successful voice analysis
      track(ANALYTICS_EVENTS.EVENT.VOICE_SUCCESSFUL, {
        duration_ms: duration,
        fields_populated_count: fieldsPopulated,
      });

      // Mark when voice analysis completed for abandonment tracking
      setVoiceCompletedAt(Date.now());

      toast.success('🪄 AI Voice Analysis Complete', {
        description: "We've filled out the form with your spoken safety event report",
      });
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Reset AI assistance flag on failure
      setIsVoiceUsed(false);

      // Determine failure reason
      let reason: 'no_speech_detected' | 'mic_denied' | 'api_error' = 'api_error';
      if (error instanceof Error) {
        if (error.message.includes('speech') || error.message.includes('audio')) {
          reason = 'no_speech_detected';
        } else if (error.message.includes('permission') || error.message.includes('microphone')) {
          reason = 'mic_denied';
        }
      }

      // Track failed voice analysis
      track(ANALYTICS_EVENTS.EVENT.VOICE_FAILED, {
        reason,
        duration_ms: duration,
      });

      toast.error('Voice Analysis Failed', {
        description: 'There was an issue processing your voice input. Please try again or fill out the form manually.',
      });
    }
  };

  if (!hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.EVENT_LIST} />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        entity="event"
        data={createdEvent}
        onSecondaryActionClick={handleReportAnother}
      />

      <div>
        <div className="mb-6 sm:mb-8">
          <p className="text-muted-foreground text-base sm:text-lg">
            Use this form to report safety incidents or near misses that occurred in your facility.
          </p>
        </div>

        <div className="mb-5">
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 flex flex-col gap-2">
            <h1 className="text-lg sm:text-xl font-semibold text-gray-800">Describe the issue</h1>
            <VoiceInput
              ref={voiceInputRef}
              onAnalysisComplete={handleVoiceAnalysis}
              isPublic={false}
              isLoading={isAnalyzing}
            />
            {/* AI Analysis Loading */}
            {isAnalyzing && <AnalyzingLoading />}
          </div>
        </div>

        {/* Media Upload Component */}
        <div className="mb-10 w-full px-0 sm:px-0">
          <MediaUpload
            className="w-full max-w-full"
            files={form.watch('media') ?? []}
            setFiles={(tFiles) => {
              form.setValue('media', tFiles);
            }}
          />
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Report Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Report Type <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportType') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('type');
                    }}
                    value={field.value} // Use value instead of defaultValue to keep it updated
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('reportType') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {reportTypeEnum.enumValues.map((type) => {
                        return (
                          <SelectItem key={type} value={type} className="capitalize">
                            {REPORT_TYPE_MAP[type]}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    An event is an event that caused injury or damage. A near miss is an event that could have caused
                    injury or damage but didn't. An observation is an event that did not cause injury or damage. A
                    customer event involves a customer in a safety-related event.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Customer Event Specific Fields - Conditional Display */}
            {form.watch('type') === 'customer_incident' && (
              <>
                {/* Customer Name */}
                <FormField
                  control={form.control}
                  name="customerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter customer's full name" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormDescription>The name of the customer involved in this event</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Customer Phone Number */}
                <FormField
                  control={form.control}
                  name="customerPhoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(*************"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const formatted = formatPhoneNumber(e.target.value);
                            field.onChange(formatted);
                          }}
                        />
                      </FormControl>
                      <FormDescription>Enter phone number with area code</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Customer Address */}
                <FormField
                  control={form.control}
                  name="customerAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Address</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter customer's address"
                          className="min-h-[80px]"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>The customer's address for documentation purposes</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Title <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('title') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Input
                      placeholder="Briefly describe what happened"
                      className={autoFilledFields.includes('title') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('title')}
                    />
                  </FormControl>
                  <FormDescription>
                    A short description of the safety event (e.g., "Fall from ladder in warehouse")
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date and Time */}
            <FormField
              control={form.control}
              name="reportedAt"
              render={({ field }) => (
                <FormItem className={`flex flex-col`}>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Date and Time <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportedAt') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <DateTimePicker
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={{ after: new Date() }}
                      onFocus={() => setLastFieldInteracted('reportedAt')}
                    />
                  </FormControl>
                  <FormDescription>When did the safety event occur?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col md:flex-row md:items-start gap-2">
              {/* Location */}
              <FormField
                control={form.control}
                name="locationId"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-2">
                      <FormLabel>Location</FormLabel>
                      {autoFilledFields.includes('location') && (
                        <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                          <Sparkles className="h-4 w-4" />
                        </motion.div>
                      )}
                    </div>
                    <FormControl>
                      <AsyncLocationSelect
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Where did it happen?"
                      />
                    </FormControl>
                    <FormDescription>
                      Specific area, building, or equipment where the safety event occurred
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Assets */}
              <FormField
                control={form.control}
                name="assetIds"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-2">
                      <FormLabel>Assets</FormLabel>
                      {autoFilledFields.includes('assetIds') && (
                        <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                          <Sparkles className="h-4 w-4" />
                        </motion.div>
                      )}
                    </div>
                    <FormControl>
                      <AsyncAssetMultiSelect
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select assets"
                        locationId={form.watch('locationId') ?? undefined}
                      />
                    </FormControl>
                    <FormDescription>
                      Select the assets that were involved in the safety event. If the safety event involved multiple
                      assets, select all that apply.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Hazard Category */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Hazard Category</FormLabel>
                    {autoFilledFields.includes('category') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('category');
                    }}
                    value={field.value ?? ''}
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('category') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select hazard category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {hazardCategoryEnum.enumValues.map((category) => (
                        <SelectItem key={category} value={category}>
                          {CATEGORY_MAP[category]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Examples: Chemical, Electrical, Ergonomic, Fall, Fire, Mechanical</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Severity Level */}
            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Severity Level</FormLabel>
                    {autoFilledFields.includes('severityLevel') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('severity');
                    }}
                    value={field.value ?? ''}
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('severityLevel') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(severityEnum.enumValues).map((level) => (
                        <SelectItem key={level} value={level}>
                          {SEVERITY_MAP[level]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    How serious was this safety event or how serious could it have been?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Description</FormLabel>
                    {autoFilledFields.includes('description') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what happened in detail"
                      className={`min-h-[120px] ${autoFilledFields.includes('description') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                      onFocus={() => setLastFieldInteracted('description')}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed account of what happened, what led to the safety event, and any relevant
                    circumstances
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Immediate Actions */}
            <FormField
              control={form.control}
              name="immediateActions"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Immediate Actions Taken</FormLabel>
                    {autoFilledFields.includes('immediateActions') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="What actions were taken immediately after the safety event?"
                      className={`min-h-[80px] ${autoFilledFields.includes('immediateActions') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                      onFocus={() => setLastFieldInteracted('immediateActions')}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe any immediate steps taken to address the situation, treat injuries, or prevent further harm
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Team Members to Notify */}
            <FormField
              control={form.control}
              name="teamMembersToNotify"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span>Team Members to Notify</span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-3 w-3" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="font-bold mb-1">Can't find a team member?</div>
                          <div>
                            Only active users in your UpKeep EHS system can be notified. <br /> To add new team members,
                            please contact your UpKeep Administrator for assistance.
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <AsyncUserMultiSelect placeholder="Select team members to notify" {...field} value={field.value} />
                  </FormControl>
                  <FormDescription>
                    Select team members who should be notified about this safety event update.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  form.reset();
                  setAutoFilledFields([]);

                  // Reset the voice input transcript
                  voiceInputRef.current?.resetTranscript();

                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              >
                Reset
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Safety Event'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
