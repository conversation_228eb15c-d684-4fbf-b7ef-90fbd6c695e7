// eslint-disable
// @ts-nocheck
import { useLocation } from 'wouter';

const WorkOrders = () => {
  const [location] = useLocation();

  // Extract the page name from the URL
  const pageName = location
    .slice(1)
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <div className="px-6 py-5 sm:px-4 xs:px-3">
      <div className="bg-white border border-gray-200 rounded-lg shadow-xs p-6">
        <h1 className="text-2xl font-bold mb-4">{pageName}</h1>
        <p className="text-gray-600">This is a blank content area that will be filled with custom content later.</p>
      </div>
    </div>
  );
};

export default WorkOrders;
