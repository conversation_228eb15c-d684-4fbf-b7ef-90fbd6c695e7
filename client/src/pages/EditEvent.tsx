import { AsyncAssetMultiSelect } from '@/components/composite/async-asset-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserMultiSelect } from '@/components/composite/async-user-multi-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { WitnessSection } from '@/components/composite/witness-section';
import { EditEventError } from '@/components/events/edit/edit-event-error';
import { EditEventLoading } from '@/components/events/edit/edit-event-loading';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { formatPhoneNumber } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { hazardCategoryEnum, reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import {
  CATEGORY_MAP,
  EditEventFormSchema,
  EventValidations,
  REPORT_TYPE_MAP,
  SEVERITY_MAP,
  STATUS_MAP,
  TransientFileSchema,
} from '@shared/schema.types';
import axios from 'axios';
import { ArrowLeft, Info } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { z } from 'zod';

const FormSchema = EditEventFormSchema.extend(EventValidations);

type FormValues = z.infer<typeof FormSchema>;

export default function EditEvent({ params }: { params: { id: string } }) {
  const [_, navigate] = useLocation();
  const eventId = params.id;
  const utils = trpc.useUtils();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      type: reportTypeEnum.enumValues[0],
      title: '',
      reportedAt: new Date(),
      category: undefined,
      severity: undefined,
      description: '',
      immediateActions: '',
      status: undefined,
      oshaReportable: false,
      locationId: undefined,
      assetIds: [],
      media: [],
      archived: false,
      teamMembersToNotify: [],
      customerName: '',
      customerPhoneNumber: '',
      customerAddress: '',
      witnesses: [],
    },
    mode: 'onSubmit',
  });

  const { data: event, isPending, isSuccess, error } = trpc.event.getByIdForEdit.useQuery({ id: eventId });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  useEffect(() => {
    const selectedLocationId = form.watch('locationId');
    if (selectedLocationId && selectedLocationId !== event?.locationId) {
      form.setValue('assetIds', []);
      utils.asset.search.invalidate();
    }
  }, [form.watch('locationId'), event?.locationId]);

  const { mutate: updateEvent } = trpc.event.update.useMutation({
    onSuccess: () => {
      utils.event.getById.invalidate({ id: eventId });
      utils.auditTrail.get.invalidate({ entityId: eventId, entityType: 'event' });
    },
  });

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();

  const { mutateAsync: updateFile } = trpc.file.update.useMutation();

  const { mutateAsync: removeFiles } = trpc.file.removeFiles.useMutation();

  // Update form values when safety event data is loaded
  useEffect(() => {
    if (event && isSuccess) {
      form.reset({
        id: event.id,
        type: event.type,
        title: event.title,
        reportedAt: event.reportedAt,
        category: event.category,
        severity: event.severity,
        description: event.description,
        immediateActions: event.immediateActions,
        status: event.status,
        oshaReportable: event.oshaReportable,
        locationId: event.locationId,
        assetIds: event.assetIds,
        media: event.media || [],
        teamMembersToNotify: event.teamMembersToNotify,
        customerName: event.customerName || '',
        customerPhoneNumber: event.customerPhoneNumber || '',
        customerAddress: event.customerAddress || '',
        witnesses: event.witnesses || [],
      });
    }
  }, [event, isSuccess]);

  const onFileRemoval = async (file: z.infer<typeof TransientFileSchema>) => {
    try {
      if (!file.id) {
        console.log('File has no id, skipping removal');
        return;
      }

      const fileId = file.id;

      await removeFiles([fileId]);

      const updatedMedia = form.getValues('media')?.filter((file) => file.id !== fileId);
      form.setValue('media', updatedMedia);

      toast.success('File removed', {
        description: 'The file has been successfully removed.',
      });
    } catch (error) {
      console.error('Error removing file:', error);
      toast.error('Error removing file', {
        description: 'There was a problem removing the file. Please try again.',
      });
    }
  };

  // Handle form submission
  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);

    const toUpdate = {};

    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as Record<string, unknown>)[field] = values[field as keyof FormValues];
      }
    }

    try {
      // Only upload new files (files with file object)
      const newFiles = values.media?.filter((item) => item.file) || [];

      // Upload only new media files
      for (const item of newFiles) {
        const result = await getPresignedUrl({
          fileName: item.name,
          fileSize: item.size,
          mimeType: item.type,
          entityType: 'event',
          entityId: event?.id,
        });

        try {
          await axios.put(result.presignedUrl, item.file, {
            headers: {
              Accept: 'application/json',
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast.error('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      updateEvent({
        ...toUpdate,
        id: eventId,
        media: undefined,
      });

      toast.success('Safety Event updated', {
        description: 'Your changes have been saved successfully.',
      });

      // Navigate back to the safety event details page
      navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(eventId));
    } catch (error) {
      console.error('Error updating safety event', error);
      toast.error('Error updating safety event', {
        description: 'There was a problem saving your changes. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isPending) {
    return <EditEventLoading />;
  }

  if (!isSuccess || !event) {
    return <EditEventError />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Button
          variant="ghost"
          onClick={() => window.history.back()}
          className="gap-1 hover:bg-neutral-300 text-neutral-900"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 className="text-2xl font-bold text-neutral-black">Edit #{event.slug}</h1>
      </div>

      {/* Main layout container - full width form */}
      <div>
        <div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Report Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Report Type <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select key={field.value} onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select report type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {reportTypeEnum.enumValues.map((type) => (
                          <SelectItem key={type} value={type}>
                            {REPORT_TYPE_MAP[type]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      An event is an event that caused injury or damage. A near miss is an event that could have caused
                      injury or damage but didn't. An observation is an event that did not cause injury or damage. A
                      customer event involves a customer in a safety-related event.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Customer Event Specific Fields - Conditional Display */}
              {form.watch('type') === 'customer_incident' && (
                <>
                  {/* Customer Name */}
                  <FormField
                    control={form.control}
                    name="customerName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter customer's full name" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormDescription>The name of the customer involved in this event</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Customer Phone Number */}
                  <FormField
                    control={form.control}
                    name="customerPhoneNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="(*************"
                            {...field}
                            value={field.value || ''}
                            onChange={(e) => {
                              const formatted = formatPhoneNumber(e.target.value);
                              field.onChange(formatted);
                            }}
                          />
                        </FormControl>
                        <FormDescription>Enter phone number with area code</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Customer Address */}
                  <FormField
                    control={form.control}
                    name="customerAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer Address</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter customer's address"
                            className="min-h-[80px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>The customer's address for documentation purposes</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Title <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Brief, descriptive title" {...field} />
                    </FormControl>
                    <FormDescription>Provide a clear, concise title that describes the safety event.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date and Time */}
              <FormField
                control={form.control}
                name="reportedAt"
                render={({ field }) => (
                  <FormItem className={`flex flex-col`}>
                    <div className="flex items-center gap-2">
                      <FormLabel>
                        Date and Time <span className="text-red-500">*</span>
                      </FormLabel>
                    </div>
                    <FormControl>
                      <DateTimePicker
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={{ after: new Date() }}
                      />
                    </FormControl>
                    <FormDescription>When did the safety event occur?</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name="locationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <AsyncLocationSelect
                        placeholder="Where did it happen?"
                        {...field}
                        value={field.value}
                        onChange={field.onChange}
                        mustIncludeObjectIds={event?.locationId ? [event.locationId] : undefined}
                      />
                    </FormControl>
                    <FormDescription>
                      Specific area, building, or equipment where the safety event occurred
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Assets */}
              <FormField
                control={form.control}
                name="assetIds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assets</FormLabel>
                    <FormControl>
                      <AsyncAssetMultiSelect
                        placeholder="Select assets"
                        {...field}
                        value={field.value}
                        onChange={field.onChange}
                        locationId={form.watch('locationId') ?? undefined}
                      />
                    </FormControl>
                    <FormDescription>
                      {/* Equipment or asset involved (AI will suggest based on selected location) */}
                      Select the assets that were involved in the safety event. If the safety event involved multiple
                      assets, select all that apply.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Category */}
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hazard Category</FormLabel>
                    <FormControl>
                      <Select key={field.value} onValueChange={field.onChange} value={field.value ?? ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select hazard category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {hazardCategoryEnum.enumValues.map((category) => (
                            <SelectItem key={category} value={category}>
                              {CATEGORY_MAP[category]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>Examples: Chemical, Electrical, Ergonomic</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Severity Level */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="severity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Severity Level</FormLabel>
                      <Select key={field.value} onValueChange={field.onChange} value={field.value ?? ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select severity" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {severityEnum.enumValues.map((level) => (
                            <SelectItem key={level} value={level}>
                              {SEVERITY_MAP[level]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>How severe was the Safety Event or potential outcome?</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Safety Event Status</FormLabel>
                    <Select key={field.value} onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {statusEnum.enumValues.map((status) => (
                          <SelectItem key={status} value={status}>
                            {STATUS_MAP[status]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Change the current state of this safety event. All updates are logged in the Status Timeline.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Detailed description of what happened..."
                        className="min-h-[120px]"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>Provide details about what happened, when, where, and how.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Immediate Actions Taken */}
              <FormField
                control={form.control}
                name="immediateActions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Immediate Actions Taken</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Actions taken immediately after the safety event..."
                        className="min-h-[100px]"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>
                      What actions were taken immediately after the safety event occurred?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* OSHA Reportable */}
              <FormField
                control={form.control}
                name="oshaReportable"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">OSHA Reportable</FormLabel>
                      <FormDescription>Requires OSHA Form 300 and reporting to authorities.</FormDescription>
                    </div>
                    <FormControl>
                      <Switch checked={field.value || false} onCheckedChange={field.onChange} />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Media section */}
              <div className="w-full">
                <h3 className="text-base font-medium mb-2">Media Files</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Current media files are displayed below. You can add new files here.
                </p>

                <MediaUpload
                  className="w-full"
                  files={form.watch('media') ?? []}
                  setFiles={(tFiles) => {
                    form.setValue('media', tFiles);
                  }}
                  onFileRemove={onFileRemoval}
                />
              </div>

              {/* Team Members to Notify */}
              <FormField
                control={form.control}
                name="teamMembersToNotify"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <span>Team Members to Notify</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <Info className="h-3 w-3" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="font-bold mb-1">Can't find a team member?</div>
                            <div>
                              Only active users in your UpKeep EHS system can be notified. <br /> To add new team
                              members, please contact your UpKeep Administrator for assistance.
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>

                    <FormControl>
                      <AsyncUserMultiSelect
                        placeholder="Select team members to notify"
                        {...field}
                        value={field.value}
                      />
                    </FormControl>
                    <FormDescription>
                      Select team members who should be notified about this safety event update.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Witnesses Section */}
              <WitnessSection
                control={form.control}
                watch={form.watch}
                setValue={form.setValue}
                name="witnesses"
              />

              {/* Submit and Cancel Buttons */}
              <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(eventId))}
                  className="px-4 font-medium text-primary-700 border-primary-700 hover:bg-primary-300"
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting} className="px-5">
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
