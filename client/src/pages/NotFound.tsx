import { useIsMobile } from '@/hooks/use-mobile';
import { AlertCircle, AlertTriangle, ArrowRight } from 'lucide-react';
import { useLocation, useSearch } from 'wouter';

export default function NotFound() {
  const isMobile = useIsMobile();
  const [location, setLocation] = useLocation();
  const search = useSearch();

  const isIncidentsRoute = location.includes('/incidents');

  const handleRedirect = () => {
    const currentPath = location + (search ? `?${search}` : '');
    const newPath = currentPath.replace('/incidents', '/events');
    setLocation(newPath);
  };

  if (isIncidentsRoute) {
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-yellow-100 flex items-center justify-center">
            <AlertTriangle className="h-8 w-8 text-yellow-600" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>Deprecated Route</h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
            The <code className="bg-gray-100 px-1 rounded text-sm">/incidents/**</code> route has been deprecated and
            moved to <code className="bg-gray-100 px-1 rounded text-sm">/events/**</code>. Please use the{' '}
            {location.includes('/public/incidents') ? 'public report' : 'Safety Events'} page instead.
          </p>
          <button
            onClick={handleRedirect}
            className={`inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors ${
              isMobile ? 'text-sm' : 'text-base'
            }`}
          >
            Go to {location.includes('/public/incidents') ? 'Public Report' : 'Safety Events'}
            <ArrowRight className="h-4 w-4" />
          </button>
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              From now on, always use the /events/** route to access{' '}
              {location.includes('/public/incidents') ? 'the public report page' : 'Safety Events'}.
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>Page Not Found</h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          The page you're looking for doesn't exist or you don't have permission to access it. This could be due to an
          invalid URL or insufficient access rights for the requested resource.
        </p>
        {isMobile && (
          <p className="text-xs text-gray-400 mt-3">
            If you believe you should have access to this page, please contact your administrator.
          </p>
        )}
      </div>
    </div>
  );
}
