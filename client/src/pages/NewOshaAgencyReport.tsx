import { AgencyReportBanner } from '@/components/agency-reports/agency-report-banner';
import { getAffectedCountLabel } from '@/components/agency-reports/agency-report.logic';
import { AsyncOshaLocationSelect } from '@/components/composite/async-osha-location-select';
import { But<PERSON> } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { usePermissions } from '@/hooks/use-permissions';
import { formatPhoneNumber } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  CreateOshaAgencyReportForm,
  CreateOshaAgencyReportFormSchema,
  OSHA_AGENCY_REPORT_TYPE_MAP,
} from '@shared/osha.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { endOfYear } from 'date-fns';
import { AlertTriangle, ArrowLeft, FileText, Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation } from 'wouter';

const FormSchema = CreateOshaAgencyReportFormSchema;

type FormType = CreateOshaAgencyReportForm;

export default function NewOshaAgencyReport() {
  const [_, navigate] = useLocation();
  const { hasPermission } = usePermissions();
  const utils = trpc.useUtils();

  const { mutateAsync: createAgencyReport, isPending } = trpc.oshaAgencyReport.create.useMutation({
    onSuccess: () => {
      utils.oshaAgencyReport.list.invalidate();
      toast('Agency Report Created', {
        description: 'The serious incident has been reported successfully.',
      });
      navigate(ROUTES.OSHA_AGENCY_REPORTS);
    },
    onError: (error) => {
      console.error('Error creating agency report:', error);
      toast('Error Creating Report', {
        description: 'There was a problem creating the agency report. Please try again.',
      });
    },
  });

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      dateOfIncident: undefined,
      oshaLocationId: undefined,
      typeOfIncident: undefined,
      description: '',
      employeesInvolved: '',
      companyContactPerson: '',
      contactPersonPhone: '',
      affectedCount: undefined,
      datePrepared: new Date(),
    },
    mode: 'onSubmit',
  });

  const onSubmit = async (values: FormType) => {
    try {
      await createAgencyReport(values);
    } catch (error) {
      // Error handling is done in the mutation's onError callback
      console.error('Error submitting serious incident:', error);
    }
  };

  if (!hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.OSHA_AGENCY_REPORTS} />;
  }

  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      {/* Header */}
      <div className="mb-6">
        <Button variant="ghost" onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS)} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Agency Reports
        </Button>

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-red-700 mb-2">
            <AlertTriangle className="h-6 w-6 inline mr-2" />
            Report Serious Incident to Regulatory Agencies
          </h1>
        </div>

        <AgencyReportBanner />
      </div>

      {/* Form Card */}
      <div className="bg-white border rounded-lg p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
              {/* Date and Time of Incident */}
              <FormField
                control={form.control}
                name="dateOfIncident"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Date & Time of Incident <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <DateTimePicker
                        selected={field.value ?? undefined}
                        onSelect={field.onChange}
                        placeholder="Select incident date & time"
                        disabled={{ after: endOfYear(new Date()), before: new Date() }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name="oshaLocationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Location of Incident <span className="text-red-500">*</span>
                    </FormLabel>

                    <FormControl>
                      <AsyncOshaLocationSelect
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select location where incident occurred"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Type of Serious Incident */}
            {/* Type of Serious Incident */}
            <FormField
              control={form.control}
              name="typeOfIncident"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Type of Serious Incident <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup value={field.value} onValueChange={field.onChange} className="grid grid-cols-2 gap-3">
                      {Object.entries(OSHA_AGENCY_REPORT_TYPE_MAP).map(([value, label]) => (
                        <div key={value} className="flex items-center space-x-2">
                          <RadioGroupItem value={value} id={`type-${value}`} />
                          <Label htmlFor={`type-${value}`}>{label}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Number of People Affected */}
            <FormField
              control={form.control}
              name="affectedCount"
              render={({ field }) => (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
                  <FormItem>
                    <FormLabel>
                      {getAffectedCountLabel(form.watch('typeOfIncident'))} <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={getAffectedCountLabel(form.watch('typeOfIncident'))}
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </div>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Brief Description of Incident <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="What happened and how it happened..."
                      value={field.value || ''}
                      onChange={field.onChange}
                      onBlur={field.onBlur}
                      name={field.name}
                      ref={field.ref}
                      rows={4}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Additional Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
              <FormField
                control={form.control}
                name="employeesInvolved"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employee(s) Involved (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter employee names separated by commas (e.g., John Doe, Jane Smith)"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>Enter the names of employees involved, separated by commas</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyContactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Contact Person for OSHA <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Contact person name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPersonPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Contact Person's Phone <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(*************"
                        {...field}
                        onChange={(e) => {
                          const formatted = formatPhoneNumber(e.target.value);
                          field.onChange(formatted);
                        }}
                      />
                    </FormControl>
                    <FormDescription>Enter phone number with area code</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="datePrepared"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date/Time Prepared</FormLabel>
                    <FormControl>
                      <DateTimePicker placeholder="Select date/time prepared" disabled selected={field.value} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Actions */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t sm:justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS)}
                className="sm:w-auto"
              >
                Cancel
              </Button>

              <Button type="submit" variant="destructive" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Save & Mark as Reported
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
