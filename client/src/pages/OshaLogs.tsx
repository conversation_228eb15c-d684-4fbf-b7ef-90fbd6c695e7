import { AsyncOshaLocationFilter } from '@/components/composite/async-osha-location-filter';
import { YearSelect } from '@/components/composite/year-select';
import { OshaReportsEmpty } from '@/components/osha-reports/list/osha-reports-empty';
import { OshaReportsError } from '@/components/osha-reports/list/osha-reports-error';
import { Filters } from '@/components/osha-reports/list/osha-reports-filters';
import { OshaReportsLoading } from '@/components/osha-reports/list/osha-reports-loading';
import { MobileFilters } from '@/components/osha-reports/list/osha-reports-mobile-filters';
import { OshaReportsMobileView } from '@/components/osha-reports/list/osha-reports-mobile-view';
import { OshaReportsTable } from '@/components/osha-reports/list/osha-reports-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteOshaReports } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useOshaReportsUrlFilters } from '@/hooks/use-url-filters';
import { exportOshaLogsCSV } from '@/lib/export-osha-logs';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { oshaTypeEnum } from '@shared/schema';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, X } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function OshaLogs() {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();

  const { hasPermission } = usePermissions();

  // Use the URL filters hook instead of manual state management
  const { filters, immediateFilters, updateFilter, resetFilters, activeFilterCount } = useOshaReportsUrlFilters();

  // Enhanced reset filters that also clears search
  const handleResetFilters = () => {
    resetFilters();
  };

  const {
    data: oshaReports,
    isLoading,
    isError,
    error,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteOshaReports({
    filters,
    enabled: true,
  });

  const { mutateAsync: exportOshaReports, isPending: isExporting } = trpc.oshaReport.export.useMutation();

  const { data: establishmentInfo } = trpc.oshaSummary.getEstablishmentInformation.useQuery(
    {
      year: filters.year,
      oshaLocationId: filters.oshaLocationId,
    },
    {
      enabled: !!filters.year,
      networkMode: 'offlineFirst',
    },
  );

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  const handleExport = async () => {
    toast.info('Exporting OSHA logs started...');

    try {
      const oshaReports = await exportOshaReports(filters);

      exportOshaLogsCSV(oshaReports);

      toast.success('Exporting OSHA logs completed...');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting OSHA logs');
    }
  };

  const toggleFilter = useCallback(
    (type: 'caseType', value: (typeof oshaTypeEnum.enumValues)[number]) => {
      const currentFilters = [...(immediateFilters[type] ?? [])];
      const index = currentFilters.indexOf(value);

      let newFilters;
      if (index > -1) {
        currentFilters.splice(index, 1);
        newFilters = currentFilters;
      } else {
        currentFilters.push(value);
        newFilters = currentFilters;
      }

      updateFilter(type, newFilters);
    },
    [immediateFilters, updateFilter],
  );

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start gap-4 md:gap-0 md:items-center mb-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">OSHA Logs</h1>
          <p className="text-muted-foreground text-sm mt-1">OSHA Form 300 Log of Work-Related Injuries and Illnesses</p>
        </div>
        <div className="flex flex-col md:flex-row items-center w-full md:w-auto gap-4">
          <AsyncOshaLocationFilter
            selected={filters.oshaLocationId}
            onSelect={(oshaLocationId) => updateFilter('oshaLocationId', oshaLocationId)}
            className="w-full md:w-auto"
          />
          <YearSelect
            value={filters.year}
            onChange={(year) => updateFilter('year', year)}
            className="w-full md:w-auto"
          />

          <div className="flex items-center w-full md:w-auto gap-4">
            {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE) && (
              <Button
                className="flex-1"
                onClick={() => navigate(ROUTES.OSHA_REPORT_NEW)}
                disabled={establishmentInfo?.archived ?? false}
              >
                + Create OSHA Record
              </Button>
            )}
            <MobileFilters
              toggleFilter={toggleFilter}
              filters={immediateFilters}
              updateFilter={updateFilter}
              activeFilterCount={activeFilterCount}
              resetFilters={handleResetFilters}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-center gap-4 md:gap-0 md:items-center mb-6">
        <Filters
          toggleFilter={toggleFilter}
          filters={immediateFilters}
          updateFilter={updateFilter}
          activeFilterCount={activeFilterCount}
          resetFilters={handleResetFilters}
        />

        <div className="flex items-center gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64 flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search OSHA Reports..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EXPORT) && (
            <Tooltip delayDuration={500}>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                  <FileDown className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent align="end">
                Export OSHA logs (maximum 500 records). <br />
                Only the first 500 records matching your <br /> current filters will be included in the export.
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>

      {isError ? <OshaReportsError /> : null}

      {isLoading ? <OshaReportsLoading /> : null}

      {oshaReports && oshaReports.length === 0 && !isLoading && !isError && (
        <OshaReportsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={handleResetFilters}
          canCreateReport={!!establishmentInfo?.id && !establishmentInfo.archived}
        />
      )}

      {!isMobile && oshaReports && oshaReports.length > 0 ? <OshaReportsTable oshaReports={oshaReports} /> : null}

      {isMobile && oshaReports && oshaReports.length > 0 ? <OshaReportsMobileView oshaReports={oshaReports} /> : null}

      {oshaReports && oshaReports.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${oshaReports.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
