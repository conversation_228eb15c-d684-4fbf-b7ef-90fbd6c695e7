import { CapaTags } from '@/components/capas/details/tags';
import { EditCapaError } from '@/components/capas/edit/edit-capa-error';
import { EditCapaLoading } from '@/components/capas/edit/edit-capa-loading';
import { AsyncAssetSelect } from '@/components/composite/async-asset-select';
import { AsyncEventsSelect } from '@/components/composite/async-events-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { AsyncUserMultiSelect } from '@/components/composite/async-user-multi-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { RootCauseSelector } from '@/components/capas/root-cause-selector';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { capaEffectivenessStatusEnum, capaPriorityEnum, capaTypeEnum, rcaMethodEnum, statusEnum } from '@shared/schema';
import {
  CAPA_EFFECTIVENESS_STATUS_MAP,
  CAPA_PRIORITY_MAP,
  CAPA_TYPE_MAP,
  CapaValidations,
  EditCapasFormSchema,
  RCA_METHOD_MAP,
  STATUS_MAP,
  TransientFileSchema,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import axios from 'axios';
import { ArrowLeft, Info } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { z } from 'zod';

const FormSchema = EditCapasFormSchema.extend(CapaValidations);

type FormValues = z.infer<typeof FormSchema>;

export default function EditCapa({ params }: { params: { id: string } }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();

  // Fetch the existing CAPA data
  const capaId = params.id;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      type: 'corrective',
      title: '',
      status: 'open',
      rootCauses: [],
      otherRootCause: '',
      ownerId: '',
      dueDate: undefined,
      priority: 'medium',
      eventId: null,
      privateToAdmins: false,
      actionsToAddress: '',
      assetId: null,
      locationId: null,
      rcaFindings: '',
      rcaMethod: '5_whys',
      archived: false,
      tags: [],
      actionsImplemented: '',
      implementationDate: undefined,
      implementedBy: '',
      voeDueDate: undefined,
      verificationFindings: '',
      voePerformedBy: '',
      voeDate: undefined,
      effectivenessStatus: 'not_effective',
      attachments: [],
      teamMembersToNotify: [],
    },
    mode: 'onSubmit',
  });

  const {
    data: capa,
    isPending: isCapaLoading,
    error: capaError,
  } = trpc.capa.getByIdForEdit.useQuery({
    id: capaId,
  });

  useEffect(() => {
    if (capaError?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [capaError]);

  // Add useEffect to handle locationId changes
  useEffect(() => {
    form.setValue('assetId', null);
    utils.asset.search.invalidate();
  }, [form.watch('locationId')]);

  const { mutateAsync: updateCapa } = trpc.capa.update.useMutation({
    onSuccess: () => {
      utils.auditTrail.get.invalidate({ entityType: 'capa', entityId: capaId });
      utils.capa.getById.invalidate({ id: capaId });
    },
    onError: (error) => {
      console.error(error);

      toast.success('Error updating CAPA', {
        description: 'There was a problem saving your changes.',
      });
    },
  });

  // Mutations for file upload
  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();
  const { mutateAsync: updateFile } = trpc.file.update.useMutation();
  const { mutateAsync: removeFiles } = trpc.file.removeFiles.useMutation();

  // Handle file removal - called when X button is clicked on existing files
  const onFileRemoval = async (file: z.infer<typeof TransientFileSchema>) => {
    try {
      if (!file.id) {
        return;
      }

      const fileId = file.id;

      await removeFiles([fileId]);

      const updatedAttachments = form.getValues('attachments')?.filter((file) => file.id !== fileId);

      form.setValue('attachments', updatedAttachments);

      toast.success('File removed', {
        description: 'The file has been successfully removed.',
      });
    } catch (error) {
      console.error('Error removing file:', error);
      toast.error('Error removing file', {
        description: 'There was a problem removing the file. Please try again.',
      });
    }
  };

  // Populate form with CAPA data when it loads
  useEffect(() => {
    if (capa && !isCapaLoading && !capaError) {
      // Set form values from CAPA data
      form.reset({
        id: capa.id,
        title: capa.title,
        status: capa.status,
        rootCauses: capa.rootCauses || [],
        otherRootCause: capa.rootCauses?.includes('other') ? (capa.otherRootCause ?? undefined) : undefined,
        actionsToAddress: capa.actionsToAddress ?? undefined,
        ownerId: capa.ownerId ?? '',
        dueDate: capa.dueDate ? new Date(capa.dueDate) : undefined,
        priority: capa.priority ?? 'medium',
        eventId: capa.eventId || null,
        privateToAdmins: capa.privateToAdmins ?? false,
        type: capa.type,
        assetId: capa.assetId ?? undefined,
        locationId: capa.locationId ?? undefined,
        rcaFindings: capa.rcaFindings ?? undefined,
        rcaMethod: capa.rcaMethod ?? '5_whys',
        archived: capa.archived ?? false,
        tags: capa.tags ?? [],
        actionsImplemented: capa.actionsImplemented ?? undefined,
        implementationDate: capa.implementationDate ? new Date(capa.implementationDate) : undefined,
        implementedBy: capa.implementedBy ?? undefined,
        voeDueDate: capa.voeDueDate ? new Date(capa.voeDueDate) : undefined,
        verificationFindings: capa.verificationFindings ?? undefined,
        voePerformedBy: capa.voePerformedBy ?? undefined,
        voeDate: capa.voeDate ? new Date(capa.voeDate) : undefined,
        effectivenessStatus: capa.effectivenessStatus ?? 'not_effective',
        attachments: capa.attachments || [],
        teamMembersToNotify: capa.teamMembersToNotify ?? [],
      });
    }
  }, [capa, isCapaLoading, capaError]);

  // Handle form submission
  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);

    const toUpdate = {};

    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as Record<string, unknown>)[field] = values[field as keyof FormValues];
      }
    }

    try {
      // Only upload new files (files with file object)
      const newFiles = values.attachments?.filter((item) => item.file) || [];

      // Upload only new attachment files
      for (const item of newFiles) {
        const result = await getPresignedUrl({
          fileName: item.name,
          fileSize: item.size,
          mimeType: item.type,
          entityType: 'capa',
          entityId: capaId,
        });

        try {
          await axios.put(result.presignedUrl, item.file, {
            headers: {
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast.error('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      await updateCapa({
        ...toUpdate,
        id: capaId,
        attachments: undefined,
      });

      toast.success('CAPA updated', {
        description: 'Your changes have been saved successfully.',
      });

      // Navigate back to the CAPA details page
      navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId));
    } catch (error) {
      console.error('Error updating CAPA', error);
      toast.error('Error updating CAPA', {
        description: 'There was a problem saving your changes. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isCapaLoading) {
    return <EditCapaLoading />;
  }

  // If error, show error message
  if (capaError || !capa) {
    return <EditCapaError />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Button
          variant="ghost"
          onClick={() => window.history.back()}
          className="gap-1 hover:bg-neutral-300 text-neutral-900"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 className="text-2xl font-bold text-neutral-black">Edit #{capa.slug}</h1>
      </div>

      {/* Main layout container - full width form */}
      <div>
        <div>
          {capa && (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* CAPA Summary Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">CAPA Summary</h2>

                  {/* Title */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Title <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Brief, descriptive title" {...field} />
                        </FormControl>
                        <FormDescription>Provide a clear, concise title that describes the CAPA.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* CAPA Type */}
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>
                            CAPA Type <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select CAPA type" />
                              </SelectTrigger>
                              <SelectContent>
                                {capaTypeEnum.enumValues.map((type) => (
                                  <SelectItem key={type} value={type}>
                                    {CAPA_TYPE_MAP[type]}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Select whether this is a corrective action, preventive action, or both.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Status */}
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>
                            Status <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              key={field.value}
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={!hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                              <SelectContent>
                                {statusEnum.enumValues.map((status) => (
                                  <SelectItem key={status} value={status}>
                                    {STATUS_MAP[status]}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Current status of this CAPA. Status changes are logged in the audit trail.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex flex-col md:flex-row md:items-start gap-2">
                  {/* Location */}
                  <FormField
                    control={form.control}
                    name="locationId"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Location</FormLabel>
                        <FormControl>
                          <AsyncLocationSelect
                            placeholder="Where did it happen?"
                            {...field}
                            value={field.value}
                            onChange={field.onChange}
                            mustIncludeObjectIds={capa.locationId ? [capa.locationId] : undefined}
                          />
                        </FormControl>
                        <FormDescription>
                          Specific area, building, or equipment where the CAPA will be implemented
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Assets */}
                  <FormField
                    control={form.control}
                    name="assetId"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Asset</FormLabel>
                        <FormControl>
                          <AsyncAssetSelect
                            placeholder="Select an asset (optional)"
                            {...field}
                            value={field.value}
                            onChange={field.onChange}
                            locationId={form.watch('locationId') ?? undefined}
                          />
                        </FormControl>
                        <FormDescription>Specific equipment or asset related to this CAPA</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Action Details Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Action Details</h2>

                  {/* Root Cause */}
                  <RootCauseSelector
                    control={form.control}
                    setValue={form.setValue}
                    rootCausesFieldName="rootCauses"
                    otherRootCauseFieldName="otherRootCause"
                  />

                  {/* Actions to Address */}
                  <FormField
                    control={form.control}
                    name="actionsToAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Proposed Actions
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Based on the RCA, what specific corrective and preventive actions are planned to eliminate the root cause and prevent recurrence?"
                            className="min-h-[150px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          List specific actions to take. Each line will be automatically formatted as a numbered item.
                          Include both immediate actions and long-term preventive measures
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* RCA Method */}
                  <FormField
                    control={form.control}
                    name="rcaMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>RCA Method</FormLabel>
                        <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value || '5_whys'}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select RCA method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rcaMethodEnum.enumValues.map((method) => (
                              <SelectItem key={method} value={method}>
                                {RCA_METHOD_MAP[method]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Method used for root cause analysis.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* RCA Findings */}
                  <FormField
                    control={form.control}
                    name="rcaFindings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          RCA Findings
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the findings from your root cause analysis"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed findings from your root cause analysis.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <CapaTags {...field} />
                        </FormControl>
                        <FormDescription>
                          Add tags to categorize this CAPA (e.g., Training, Procedure, Equipment).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Actual Actions Taken */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Actual Actions Taken</h2>

                  {/* VoE Findings */}
                  <FormField
                    control={form.control}
                    name="actionsImplemented"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Actions Implemented</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the actions implemented"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed actions implemented.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* Implemented By */}
                    <FormField
                      control={form.control}
                      name="implementedBy"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Implemented By</FormLabel>
                          <FormControl>
                            <AsyncUserSelect placeholder="Select a person" {...field} value={field.value} />
                          </FormControl>
                          <FormDescription>Person responsible for implementing this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VoE Date */}
                    <FormField
                      control={form.control}
                      name="implementationDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Implementation Date</FormLabel>
                          <FormControl>
                            <DateTimePicker
                              selected={field.value ?? undefined}
                              onSelect={field.onChange}
                              disabled={{
                                before: new Date(),
                              }}
                              onlyDate
                              placeholder="Select implementation date"
                            />
                          </FormControl>
                          <FormDescription>Date this CAPA was implemented.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* VoE Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Verification of Effectiveness (VoE)</h2>

                  {/* VoE Due Date */}
                  <FormField
                    control={form.control}
                    name="voeDueDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VoE Due Date</FormLabel>
                        <FormControl>
                          <DateTimePicker
                            selected={field.value ?? undefined}
                            onSelect={field.onChange}
                            disabled={{
                              before: new Date(),
                            }}
                            onlyDate
                            placeholder="Select VoE due date"
                          />
                        </FormControl>
                        <FormDescription>Target completion date for this VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* VoE Performed By */}
                    <FormField
                      control={form.control}
                      name="voePerformedBy"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>VoE Performed By</FormLabel>
                          <FormControl>
                            <AsyncUserSelect placeholder="Select a person" {...field} value={field.value} />
                          </FormControl>
                          <FormDescription>Person responsible for performing this VoE.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VoE Date */}
                    <FormField
                      control={form.control}
                      name="voeDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>VoE Date</FormLabel>
                          <FormControl>
                            <DateTimePicker
                              selected={field.value ?? undefined}
                              onSelect={field.onChange}
                              disabled={{
                                before: new Date(),
                              }}
                              onlyDate
                              placeholder="Select VoE date"
                            />
                          </FormControl>
                          <FormDescription>Date this VoE was completed.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* VoE Findings */}
                  <FormField
                    control={form.control}
                    name="verificationFindings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VoE Findings</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the findings from your VoE"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed findings from your VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Effectiveness Status */}
                  <FormField
                    control={form.control}
                    name="effectivenessStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Effectiveness Status</FormLabel>
                        <FormControl>
                          <Select
                            key={field.value}
                            onValueChange={field.onChange}
                            defaultValue={field.value ?? undefined}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select effectiveness status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {capaEffectivenessStatusEnum.enumValues.map((status) => (
                                <SelectItem key={status} value={status}>
                                  {CAPA_EFFECTIVENESS_STATUS_MAP[status]}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormDescription>Status of this VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Assignment Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Assignment</h2>
                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* Owner */}
                    <FormField
                      control={form.control}
                      name="ownerId"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>
                            Owner <span className="text-red-500">*</span>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger>
                                  <Info className="h-3 w-3" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <div className="font-bold mb-1">Can't find a team member?</div>
                                  <div>
                                    Only active users in your UpKeep EHS system can be assigned. <br /> To add new team
                                    members, please contact your UpKeep Administrator for assistance.
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormLabel>

                          <FormControl>
                            <AsyncUserSelect placeholder="Select an owner" {...field} value={field.value} />
                          </FormControl>
                          <FormDescription>Person responsible for implementing this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Due Date */}
                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Due Date</FormLabel>
                          <FormControl>
                            <DateTimePicker
                              selected={field.value ?? undefined}
                              onSelect={field.onChange}
                              disabled={{
                                before: new Date(),
                              }}
                              onlyDate
                              placeholder="Select due date"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Priority */}
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>
                          Priority <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {capaPriorityEnum.enumValues.map((priority) => (
                              <SelectItem key={priority} value={priority}>
                                {CAPA_PRIORITY_MAP[priority]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Set the priority level for this CAPA.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Linkage Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Linkage</h2>

                  {/* Linked Safety Event */}
                  <FormField
                    control={form.control}
                    name="eventId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Link to Safety Event</FormLabel>
                        <div className="flex flex-col gap-3">
                          <AsyncEventsSelect
                            onChange={(eventId) => {
                              field.onChange(eventId);
                            }}
                            value={field.value}
                            placeholder="Search and select a safety event..."
                          />
                        </div>
                        <FormDescription>Connect this CAPA to a related safety event.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Attachments Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Attachments</h2>

                  {/* File Uploads */}
                  <div>
                    <FormLabel>Upload Files</FormLabel>
                    <div className="mt-2">
                      <MediaUpload
                        maxFiles={5}
                        maxSize={20}
                        className="bg-white"
                        files={form.watch('attachments') ?? []}
                        setFiles={(tFiles) => {
                          form.setValue('attachments', tFiles);
                        }}
                        onFileRemove={onFileRemoval}
                      />
                    </div>
                  </div>
                </div>

                {/* Team Notifications Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Team Notifications</h2>

                  {/* Private to Admins Toggle */}
                  {/* TODO: REMOVED FROM V1 */}
                  {/* <FormField
                    control={form.control}
                    name="privateToAdmins"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Private to Admins Only</FormLabel>
                          <FormDescription>When enabled, only administrators can view this CAPA.</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value || false} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  /> */}

                  {/* Team Members to Notify */}
                  <FormField
                    control={form.control}
                    name="teamMembersToNotify"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Team Members to Notify
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-3 w-3" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="font-bold mb-1">Can't find a team member?</div>
                                <div>
                                  Only active users in your UpKeep EHS system can be notified. <br /> To add new team
                                  members, please contact your UpKeep Administrator for assistance.
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <AsyncUserMultiSelect
                            placeholder="Select team members to notify"
                            {...field}
                            value={field.value}
                          />
                        </FormControl>
                        <FormDescription>
                          Select team members who should be notified about this CAPA update.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId))}
                    className="px-4 font-medium text-primary-700 border-primary-700 hover:bg-primary-300"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting} className="px-5">
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>
    </div>
  );
}
