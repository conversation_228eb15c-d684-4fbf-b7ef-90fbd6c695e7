import { AgencyReportsEmpty } from '@/components/agency-reports/list/agency-reports-empty';
import { AgencyReportsError } from '@/components/agency-reports/list/agency-reports-error';
import { AgencyReportsFilters } from '@/components/agency-reports/list/agency-reports-filters';
import { AgencyReportsLoading } from '@/components/agency-reports/list/agency-reports-loading';
import { AgencyReportsMobileFilters } from '@/components/agency-reports/list/agency-reports-mobile-filters';
import { AgencyReportsMobileView } from '@/components/agency-reports/list/agency-reports-mobile-view';
import { AgencyReportsTable } from '@/components/agency-reports/list/agency-reports-table';
import { AsyncOshaLocationFilter } from '@/components/composite/async-osha-location-filter';
import { YearSelect } from '@/components/composite/year-select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteAgencyReports } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useOshaAgencyReportsUrlFilters } from '@/hooks/use-url-filters';
import { exportOshaAgencyReportsCSV } from '@/lib/export-osha-agency-reports';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { oshaAgencyReportTypeEnum } from '@shared/schema';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { AlertTriangle, FileDown, Search, X } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function OshaAgencyReportLogs() {
  const isMobile = useIsMobile();
  const { hasPermission } = usePermissions();
  const [_, navigate] = useLocation();

  // Use the URL filters hook instead of manual state management
  const { filters, immediateFilters, updateFilter, resetFilters, activeFilterCount } = useOshaAgencyReportsUrlFilters();

  // Enhanced reset filters that also clears search
  const handleResetFilters = () => {
    resetFilters();
  };

  const {
    data: agencyReports,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteAgencyReports({
    filters,
    enabled: true,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  const { data: establishmentInfo } = trpc.oshaSummary.getEstablishmentInformation.useQuery(
    {
      year: filters.year!,
      oshaLocationId: filters.oshaLocationId,
    },
    {
      enabled: !!filters.year && !!filters.oshaLocationId,
      networkMode: 'offlineFirst',
    },
  );

  const { mutateAsync: exportAgencyReports, isPending: isExporting } = trpc.oshaAgencyReport.export.useMutation();

  const toggleFilter = useCallback(
    (type: 'typeOfIncident', value: (typeof oshaAgencyReportTypeEnum.enumValues)[number]) => {
      const currentFilters = [...(immediateFilters[type] ?? [])];
      const index = currentFilters.indexOf(value);

      let newFilters;
      if (index > -1) {
        currentFilters.splice(index, 1);
        newFilters = currentFilters;
      } else {
        currentFilters.push(value);
        newFilters = currentFilters;
      }

      updateFilter(type, newFilters);
    },
    [immediateFilters, updateFilter],
  );

  const handleExport = async () => {
    try {
      toast.info('Exporting OSHA agency reports...');
      const agencyReports = await exportAgencyReports(filters);
      exportOshaAgencyReportsCSV(agencyReports);
      toast.success('Exporting OSHA agency reports completed...');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting OSHA agency reports');
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4">
        <div className="mb-4 lg:mb-0">
          <h1 className="text-2xl font-bold">Agency Reports</h1>
          <p className="text-muted-foreground text-sm mt-1">
            Records of serious incidents reported to OSHA and other regulatory agencies
          </p>
        </div>

        <div className="flex flex-col md:flex-row items-center w-full lg:w-auto gap-4">
          <AsyncOshaLocationFilter
            selected={filters.oshaLocationId}
            onSelect={(oshaLocationId) => updateFilter('oshaLocationId', oshaLocationId)}
            className="w-full md:w-auto"
          />
          <YearSelect
            value={filters.year}
            onChange={(year) => updateFilter('year', year)}
            className="w-full md:w-auto"
          />
          <div className="flex items-center w-full md:w-auto gap-4">
            {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE) && (
              <Button
                className="flex-1"
                variant="destructive"
                onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS_NEW)}
                disabled={establishmentInfo?.archived ?? false}
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                Report New Serious Incident
              </Button>
            )}

            <AgencyReportsMobileFilters
              toggleFilter={toggleFilter}
              filters={immediateFilters}
              updateFilter={updateFilter}
              activeFilterCount={activeFilterCount}
              resetFilters={handleResetFilters}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-center gap-4 md:gap-0 md:items-center mb-6">
        <AgencyReportsFilters
          toggleFilter={toggleFilter}
          filters={immediateFilters}
          updateFilter={updateFilter}
          activeFilterCount={activeFilterCount}
          resetFilters={handleResetFilters}
        />
        <div className="flex items-center gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search reports..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0"
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EXPORT) && (
            <Tooltip delayDuration={500}>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                  <FileDown className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent align="end">
                Export OSHA agency reports (maximum 500 records). <br />
                Only the first 500 records matching your <br /> current filters will be included in the export.
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>

      {/* Loading state */}
      {isLoading && <AgencyReportsLoading />}

      {/* Error state */}
      {error && <AgencyReportsError />}

      {/* Data states */}
      {!isLoading && !error && agencyReports && (
        <>
          {/* Desktop Table View */}
          {!isMobile && agencyReports.length > 0 && <AgencyReportsTable agencyReports={agencyReports} />}

          {/* Mobile Card View */}
          {isMobile && agencyReports.length > 0 && <AgencyReportsMobileView agencyReports={agencyReports} />}

          {/* Empty state */}
          {agencyReports.length === 0 && (
            <AgencyReportsEmpty
              hasActiveFilters={activeFilterCount > 0}
              onResetFilters={handleResetFilters}
              canCreateReport={!!establishmentInfo?.id && !establishmentInfo.archived}
            />
          )}

          {/* Load More Button */}
          {agencyReports.length > 0 && hasNextPage && (
            <div className="flex justify-center mt-6">
              <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
                {isFetchingNextPage ? 'Loading more...' : `Load More (${agencyReports.length} loaded)`}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
