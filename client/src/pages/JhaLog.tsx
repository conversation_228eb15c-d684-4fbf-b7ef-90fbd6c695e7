import { Button } from '@/components/ui/button';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { navigate } from 'wouter/use-browser-location';

export default function JhaLog() {
  const { hasPermission } = usePermissions();
  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start mb-6">
        <div className="mb-4">
          <h1 className="text-2xl font-bold  md:mb-0">Safety Events</h1>
          <p className="text-muted-foreground text-sm mt-1">Track and manage all safety events</p>
        </div>

        <div className="flex items-center gap-4 w-full md:w-auto">
          {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE) && (
            <Button
              onClick={() => {
                navigate(ROUTES.JHA_NEW);
              }}
              className="flex-1"
            >
              + Create JHA
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
