import { LocationSchema, SelectAccessPointSchema } from '@shared/schema.types';
import { z } from 'zod';

export const TransientAccessPointSchema = SelectAccessPointSchema.pick({
  id: true,
  upkeepCompanyId: true,
  name: true,
  archived: true,
  status: true,
  locationId: true,
}).extend({
  location: LocationSchema.optional(),
});

export type TransientAccessPoint = z.infer<typeof TransientAccessPointSchema>;
