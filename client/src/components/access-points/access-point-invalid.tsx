import { useIsMobile } from '@/hooks/use-mobile';
import { AlertTriangle, QrCode } from 'lucide-react';

export const AccessPointInvalid = () => {
  const isMobile = useIsMobile();

  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-orange-100 flex items-center justify-center">
          <div className="relative">
            <QrCode className="h-8 w-8 text-orange-500" />
            <div className="absolute -top-1 -right-1">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </div>
          </div>
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          Access point is deactivated
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          This access point has been deactivated and cannot be used to create safety events. Please contact your
          administrator to restore access to this location.
        </p>
        {isMobile && (
          <p className="text-xs text-gray-400 mt-3">
            Contact your administrator if you need help managing this access point.
          </p>
        )}
      </div>
    </div>
  );
};
