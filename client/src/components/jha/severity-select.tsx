import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';

export const SeveritySelect = ({ value, onSelect }: { value: number; onSelect: (value: string) => void }) => {
  return (
    <Select value={value.toString()} onValueChange={onSelect}>
      <SelectTrigger className="h-8 text-xs">
        <SelectValue placeholder="1" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="1">1 - Minor</SelectItem>
        <SelectItem value="2">2 - Moderate</SelectItem>
        <SelectItem value="3">3 - Serious</SelectItem>
        <SelectItem value="4">4 - Major</SelectItem>
        <SelectItem value="5">5 - Catastrophic</SelectItem>
      </SelectContent>
    </Select>
  );
};
