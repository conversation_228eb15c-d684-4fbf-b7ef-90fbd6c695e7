import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';

export const LikelihoodSelect = ({ value, onSelect }: { value: number; onSelect: (value: string) => void }) => {
  return (
    <Select value={value.toString()} onValueChange={onSelect}>
      <SelectTrigger className="h-8 text-xs">
        <SelectValue placeholder="1" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="1">1 - Rare</SelectItem>
        <SelectItem value="2">2 - Unlikely</SelectItem>
        <SelectItem value="3">3 - Possible</SelectItem>
        <SelectItem value="4">4 - Likely</SelectItem>
        <SelectItem value="5">5 - Almost Certain</SelectItem>
      </SelectContent>
    </Select>
  );
};
