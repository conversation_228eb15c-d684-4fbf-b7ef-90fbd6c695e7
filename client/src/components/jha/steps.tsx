import { Control, useFieldArray, UseFormSetValue, UseFormWatch } from 'react-hook-form';

import { LikelihoodSelect } from '@/components/jha/likelihood-select';
import { SeveritySelect } from '@/components/jha/severity-select';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { CreateFullJhaType } from '@shared/jha.types';

import { GripVertical, InfoIcon, Trash2 } from 'lucide-react';

interface JhaStepsProps {
  control: Control<CreateFullJhaType>;
  watch: UseFormWatch<CreateFullJhaType>;
  setValue: UseFormSetValue<CreateFullJhaType>;
}

interface SortableStepRowProps {
  id: string;
  stepNumber: number;
  stepIndex: number;
  riskScore: number;
  canRemove: boolean;
  onRemove: () => void;
  severity: number;
  likelihood: number;
  watch: UseFormWatch<CreateFullJhaType>;
  setValue: UseFormSetValue<CreateFullJhaType>;
}

function SortableStepRow({
  id,
  stepNumber,
  stepIndex,
  riskScore,
  canRemove,
  onRemove,
  severity,
  likelihood,
  watch,
  setValue,
}: SortableStepRowProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getRiskColor = (score: number) => {
    if (score >= 20) return 'bg-red-100 text-red-800';
    if (score >= 15) return 'bg-orange-100 text-orange-800';
    if (score >= 10) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const onSeveritySelect = (value: string) => {
    setValue(`steps.${stepIndex}.severity`, parseInt(value));
  };
  const onLikelihoodSelect = (value: string) => {
    setValue(`steps.${stepIndex}.likelihood`, parseInt(value));
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'flex items-center pl-4 py-2 hover:bg-gray-50/50 transition-colors',
        isDragging && 'z-50 bg-white shadow-lg rounded-lg border',
      )}
    >
      {/* Controls Section */}
      <div className="flex items-center gap-1">
        <AccordionTrigger className="hover:no-underline h-4 w-4 flex items-center justify-center" />

        <Button
          type="button"
          variant="ghost"
          size="icon"
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing size-6"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </Button>

        <Button type="button" variant="ghost" size="icon" disabled={!canRemove} onClick={onRemove} className="size-6">
          <Trash2 className="h-4 w-4 text-red-600 hover:text-red-700 hover:bg-red-50" />
        </Button>
      </div>

      {/* Step Number */}
      <div className="flex justify-center items-center w-8">
        <span className="font-medium text-sm">{stepNumber}</span>
      </div>

      {/* Step Title */}
      <div className="w-64 px-2">
        <Input
          placeholder="Enter step title..."
          className="border-0 bg-transparent p-0 text-sm focus-visible:ring-0 w-full border-b border-gray-300 rounded-none focus-visible:border-blue-500"
          value={watch(`steps.${stepIndex}.title`)}
          onChange={(e) => setValue(`steps.${stepIndex}.title`, e.target.value)}
        />
      </div>

      {/* Potential Hazards */}
      <div className="w-40 px-2">
        <div className="text-sm text-gray-500 truncate">None selected</div>
      </div>

      {/* Severity */}
      <div className="w-44 px-2">
        <SeveritySelect value={severity} onSelect={onSeveritySelect} />
      </div>

      {/* Likelihood */}
      <div className="w-44 px-2">
        <LikelihoodSelect value={likelihood} onSelect={onLikelihoodSelect} />
      </div>

      {/* Risk Score */}
      <div className="w-44 flex justify-center px-2">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(riskScore)}`}>{riskScore}</span>
      </div>

      {/* Control Measures */}
      <div className="w-40 px-2">
        <div className="text-sm text-gray-500 truncate">None selected</div>
      </div>
    </div>
  );
}

export function JhaSteps({ control, watch, setValue }: JhaStepsProps) {
  const { fields, append, remove, move } = useFieldArray({
    control,
    name: 'steps',
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const addStep = () => {
    append({
      serial: fields.length + 1,
      title: '',
      hazardIds: [],
      controlMeasureIds: [],
      severity: 1,
      likelihood: 1,
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex((field) => field.id === active.id);
      const newIndex = fields.findIndex((field) => field.id === over?.id);
      move(oldIndex, newIndex);
    }
  };

  return (
    <div>
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <ScrollArea className="min-h-96">
          {/* Table Header */}
          <div className="bg-gray-100 border-b border-gray-300">
            <div className="flex items-center py-4 pl-8 font-medium text-sm text-gray-800">
              <div className="w-14" />
              <div className="w-8 text-center">#</div>
              <div className="w-64 px-2">Step Title</div>
              <div className="w-40 px-2">Potential Hazard(s)</div>
              <div className="w-44 px-2">Severity</div>
              <div className="w-44 px-2">Likelihood</div>
              <div className="w-44 px-2 flex justify-center items-center gap-1">
                <span className="hidden sm:inline">Initial Risk Score</span>
                <span className="sm:hidden">Risk</span>
                <Tooltip>
                  <TooltipTrigger>
                    <InfoIcon className="w-3 h-3" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <strong>Risk Score Calculation</strong>
                    <br />
                    Risk Score = Severity (1-5) × Likelihood (1-5)
                    <br />
                    Risk Levels:
                    <br />
                    • Low: 1-5
                    <br />
                    • Medium: 6-14
                    <br />• High: 15-25
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="w-40 px-2">Control Measure(s)</div>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <Accordion type="multiple">
                <SortableContext items={fields.map((field) => field.id)} strategy={verticalListSortingStrategy}>
                  {fields.map((field, index) => {
                    const stepNumber = index + 1;
                    const severity = watch(`steps.${index}.severity`) || 1;
                    const likelihood = watch(`steps.${index}.likelihood`) || 1;
                    const riskScore = severity * likelihood;

                    return (
                      <AccordionItem key={field.id} value={`step-${field.id}`} className="border-b border-gray-200">
                        <SortableStepRow
                          id={field.id}
                          stepNumber={stepNumber}
                          stepIndex={index}
                          riskScore={riskScore}
                          canRemove={fields.length > 1}
                          onRemove={() => remove(index)}
                          watch={watch}
                          setValue={setValue}
                          severity={severity}
                          likelihood={likelihood}
                        />

                        <AccordionContent className="pt-0 pb-6">
                          <div className="px-4 py-6 bg-gray-50/50 border-t border-gray-100">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                              <div className="space-y-4">
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Hazard Details</h4>
                                  <div className="text-sm text-gray-600">
                                    Additional hazard information and descriptions can go here.
                                  </div>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Risk Assessment</h4>
                                  <div className="text-sm text-gray-600">
                                    Detailed risk analysis and considerations.
                                  </div>
                                </div>
                              </div>
                              <div className="space-y-4">
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Control Measures</h4>
                                  <div className="text-sm text-gray-600">
                                    Detailed control measures and implementation notes.
                                  </div>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Additional Notes</h4>
                                  <div className="text-sm text-gray-600">
                                    Any additional notes or comments for this step.
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
                </SortableContext>
              </Accordion>
            </DndContext>
          </div>

          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      <Button type="button" variant="outline" onClick={addStep} className="w-full mt-4">
        + Add Step
      </Button>
    </div>
  );
}
