import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

export const AgencyReportBanner = () => {
  return (
    <>
      {/* Critical Deadline Warnings */}
      <Alert variant="destructive" className="mb-6 border-l-4 border-red-500 bg-red-50">
        <AlertTitle className="text-red-800 text-xl mb-3">🚨 IMMEDIATE REPORTING REQUIRED</AlertTitle>
        <AlertDescription className="text-red-900 space-y-2 font-semibold">
          <p>FATALITIES, HOSPITALIZATIONS, AMPUTATIONS, LOSS OF AN EYE MUST BE REPORTED TO OSHA IMMEDIATELY</p>
          <p>
            • Federal OSHA: Within 8 hours for fatalities, inpatient hospitalizations, amputations, or loss of an eye
          </p>
          <p>
            • California (Cal/OSHA) & Other State Plans: Report immediately (as soon as feasible, no longer than 8
            hours)
          </p>
        </AlertDescription>
      </Alert>

      {/* Contact Information */}
      <Alert className="mb-6 bg-blue-50 border-blue-200">
        <AlertTitle className="text-blue-900 text-lg mb-3">How to Report - Contact OSHA Now:</AlertTitle>
        <AlertDescription className="text-blue-800 space-y-2">
          <p className="font-semibold">📞 Call OSHA toll-free at 1-800-321-OSHA (6742)</p>
          <p>• Or contact your nearest OSHA Area Office directly</p>
          <p>• For State Plans (e.g., Cal/OSHA), contact your specific state agency</p>
          <p>
            <a
              href="https://www.osha.gov/severeinjury/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline font-semibold"
            >
              🔗 OSHA Severe Injury Reporting Guidelines →
            </a>
          </p>
        </AlertDescription>
      </Alert>
    </>
  );
};
