import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, AlertTriangle } from 'lucide-react';

export const EditAgencyReportLoading = () => {
  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      {/* Header section with back button */}
      <div className="mb-6">
        <Button variant="ghost" disabled className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Agency Reports
        </Button>

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-red-700 mb-2">
            <AlertTriangle className="h-6 w-6 inline mr-2" />
            Edit Serious Incident Report
          </h1>
        </div>

        {/* Critical Deadline Warnings */}
        <div className="bg-red-50 border-l-4 border-red-500 p-6 rounded-r-sm mb-6">
          <div className="font-bold text-red-800 text-xl mb-3">🚨 IMMEDIATE REPORTING REQUIRED</div>
          <div className="text-red-900 space-y-2 font-semibold">
            <p>FATALITIES, HOSPITALIZATIONS, AMPUTATIONS, LOSS OF AN EYE MUST BE REPORTED TO OSHA IMMEDIATELY</p>
            <p>
              • Federal OSHA: Within 8 hours for fatalities, inpatient hospitalizations, amputations, or loss of an eye
            </p>
            <p>
              • California (Cal/OSHA) & Other State Plans: Report immediately (as soon as feasible, no longer than 8
              hours)
            </p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-blue-50 border border-blue-200 p-6 rounded-sm mb-6">
          <div className="font-semibold text-blue-900 text-lg mb-3">How to Report - Contact OSHA Now:</div>
          <div className="text-blue-800 space-y-2">
            <p className="font-semibold">📞 Call OSHA toll-free at 1-800-321-OSHA (6742)</p>
            <p>• Or contact your nearest OSHA Area Office directly</p>
            <p>• For State Plans (e.g., Cal/OSHA), contact your specific state agency</p>
            <p>
              <a
                href="https://www.osha.gov/severeinjury/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline font-semibold"
              >
                🔗 OSHA Severe Injury Reporting Guidelines →
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Form Card */}
      <div className="bg-white border rounded-lg p-6">
        <form className="space-y-6">
          {/* Date and Location - Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
            {/* Date and Time of Incident */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-10 w-full" />
            </div>

            {/* Location */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>

          {/* Type of Serious Incident */}
          <div className="space-y-4">
            <Skeleton className="h-4 w-44" />
            <div className="grid grid-cols-2 gap-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <Skeleton className="h-4 w-4 rounded-full" />
                  <Skeleton className="h-4 w-32" />
                </div>
              ))}
            </div>
          </div>

          {/* Affected Count */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-48" />
            <Skeleton className="h-24 w-full" />
          </div>

          {/* Additional Fields - Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
            {/* Employee(s) Involved */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>

            {/* Company Contact Person */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-10 w-full" />
            </div>

            {/* Contact Person's Phone */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>

            {/* Date Prepared */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>

          {/* Submit Actions */}
          <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t sm:justify-end">
            <Skeleton className="h-10 w-20 sm:w-auto" />
            <Skeleton className="h-10 w-48 sm:w-auto" />
          </div>
        </form>
      </div>
    </div>
  );
};
