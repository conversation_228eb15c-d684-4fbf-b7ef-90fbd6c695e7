import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ArrowLeft } from 'lucide-react';
import { useLocation } from 'wouter';

export const EditAgencyReportError = () => {
  const [_, navigate] = useLocation();
  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      <div>
        <div className="flex items-center gap-2 mb-6">
          <Button variant="ghost" onClick={() => window.history.back()} className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Error Loading Agency Report</CardTitle>
            <CardDescription>
              We couldn't load the agency report data. Please try again or go back to the agency reports list.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS)}>Return to Agency Reports</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
