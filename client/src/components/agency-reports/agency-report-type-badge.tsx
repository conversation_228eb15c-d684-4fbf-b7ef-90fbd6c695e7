import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { OSHA_AGENCY_REPORT_TYPE_MAP } from '@shared/osha.types';
import { oshaAgencyReportTypeEnum } from '@shared/schema';

export const AgencyReportTypeBadge = ({ type }: { type: (typeof oshaAgencyReportTypeEnum.enumValues)[number] }) => {
  const TYPE_BADGE_COLORS = {
    [oshaAgencyReportTypeEnum.enumValues[0]]: 'bg-red-100 text-red-800 border-red-200',
    [oshaAgencyReportTypeEnum.enumValues[1]]: 'bg-orange-100 text-orange-800 border-orange-200',
    [oshaAgencyReportTypeEnum.enumValues[2]]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    [oshaAgencyReportTypeEnum.enumValues[3]]: 'bg-purple-100 text-purple-800 border-purple-200',
  } as const;

  return (
    <Badge variant="outline" className={cn(TYPE_BADGE_COLORS[type])}>
      {OSHA_AGENCY_REPORT_TYPE_MAP[type]}
    </Badge>
  );
};
