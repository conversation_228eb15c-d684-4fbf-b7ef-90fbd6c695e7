import { Skeleton } from '@/components/ui/skeleton';

export const AgencyReportDetailsLoading = () => (
  <div className="container mx-auto py-4 px-4">
    {/* Back button */}
    <div className="mb-3">
      <Skeleton className="h-9 w-20" />
    </div>

    {/* Report Header */}
    <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
      {/* Desktop View */}
      <div className="hidden md:block w-full">
        <div className="flex flex-wrap items-center gap-2 mb-1">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-5 w-20 rounded-full" />
          <Skeleton className="h-5 w-24 rounded-full" />
        </div>
        <Skeleton className="h-8 w-64 mb-2" />
      </div>

      {/* Mobile View */}
      <div className="md:hidden w-full">
        <div className="flex flex-wrap items-center gap-2 mb-1">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-5 w-20 rounded-full" />
          <Skeleton className="h-5 w-24 rounded-full" />
        </div>
        
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48 mb-2 flex-1" />
          <Skeleton className="h-9 w-9 rounded" />
        </div>
      </div>

      {/* Desktop buttons */}
      <div className="hidden md:flex gap-2 self-start">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-8 w-8" />
      </div>
    </div>

    {/* Context bar with metadata */}
    <div className="flex flex-wrap items-center text-sm mb-4 gap-y-2">
      <Skeleton className="h-4 w-24" />
      <div className="hidden sm:block mx-2">•</div>
      <Skeleton className="h-4 w-20" />
      <div className="hidden sm:block mx-2">•</div>
      <Skeleton className="h-4 w-32" />
    </div>

    {/* Main content grid */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Left column - main content */}
      <div className="md:col-span-2 space-y-4">
        {/* Severity Alert Card */}
        <div className="bg-white rounded-lg shadow-sm border border-red-200 overflow-hidden">
          <div className="bg-red-50 border-b border-red-200 p-4">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-6 w-6" />
              <div>
                <Skeleton className="h-6 w-40 mb-1" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="text-center p-3 bg-red-50 rounded-md border border-red-200">
                  <Skeleton className="h-6 w-8 mx-auto mb-1" />
                  <Skeleton className="h-3 w-16 mx-auto" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Description Card */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <Skeleton className="h-6 w-40" />
          </div>
          <div className="p-6">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>

        {/* Employees Involved Card */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <Skeleton className="h-6 w-36" />
          </div>
          <div className="p-6">
            <Skeleton className="h-4 w-48" />
          </div>
        </div>

        {/* Emergency Contact Card */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <Skeleton className="h-6 w-36" />
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-5 w-32" />
              </div>
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <div className="flex items-center mt-1">
                  <Skeleton className="h-4 w-4 mr-2" />
                  <Skeleton className="h-5 w-28" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right column - metadata & timeline */}
      <div className="space-y-4">
        {/* Incident Details Card */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="p-6 space-y-3">
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-5 w-32" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-5 w-28" />
            </div>
          </div>
        </div>

        {/* Location Card */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="p-6">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-5 w-40" />
            </div>
          </div>
        </div>

        {/* Report Timeline Card */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="p-6 space-y-3">
            <div>
              <Skeleton className="h-4 w-28 mb-1" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div>
              <Skeleton className="h-4 w-32 mb-1" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="pt-3 border-t">
              <Skeleton className="h-4 w-full mb-1" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);
