import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { AlertTriangle, ArrowLeft } from 'lucide-react';
import { useLocation } from 'wouter';

export const AgencyReportDetailsError = () => {
  const [_, navigate] = useLocation();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-6">
        {/* Header skeleton placeholders */}
        <div className="mb-6">
          <Skeleton className="h-10 w-32 mb-4" />
          <div className="flex items-center gap-2 mb-2">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-24" />
          </div>
          <Skeleton className="h-8 w-2/3" />
        </div>

        {/* Error content card */}
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-40" />
            </div>
          </CardHeader>
          <CardContent className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Report Not Found</h2>
            <p className="text-gray-600 mb-4">The agency report you're looking for could not be found.</p>
            <Button onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to OSHA Agency Reports
            </Button>
          </CardContent>
        </Card>

        {/* Additional skeleton placeholders for layout consistency */}
        <div className="max-w-2xl mx-auto mt-6 space-y-4">
          <Card>
            <CardContent className="p-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
