import { oshaAgencyReportTypeEnum } from '@shared/schema';

export const getAffectedCountLabel = (selectedIncidentType?: (typeof oshaAgencyReportTypeEnum.enumValues)[number]) => {
  if (!selectedIncidentType) return 'Number of People Affected';
  const labelMap = {
    [oshaAgencyReportTypeEnum.enumValues[0]]: 'Number of Fatalities',
    [oshaAgencyReportTypeEnum.enumValues[1]]: 'Number of Amputations',
    [oshaAgencyReportTypeEnum.enumValues[2]]: 'Number of Hospitalizations',
    [oshaAgencyReportTypeEnum.enumValues[3]]: 'Number of Eye Loss Cases',
  } as const;

  return labelMap[selectedIncidentType] || 'Number of People Affected';
};
