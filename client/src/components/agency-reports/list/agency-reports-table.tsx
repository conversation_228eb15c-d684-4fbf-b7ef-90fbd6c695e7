import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { Eye, Info, MapPin } from 'lucide-react';
import { useLocation } from 'wouter';
import { AgencyReportTypeBadge } from '../agency-report-type-badge';

export function AgencyReportsTable({
  agencyReports,
}: {
  agencyReports: RouterOutputs['oshaAgencyReport']['list']['result'];
}) {
  const [_, navigate] = useLocation();

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Report ID</TableHead>
            <TableHead className="w-[130px]">Date & Time</TableHead>
            <TableHead className="w-[100px]">Type</TableHead>
            <TableHead>OSHA Location</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-[110px]">Prepared</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {agencyReports.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center">
                  <Info className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No agency reports found</p>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            agencyReports.map((report) => (
              <TableRow
                key={report.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORT_DETAILS?.replace(':id', report.id) || '#')}
              >
                <TableCell>
                  <div>
                    <span className="font-medium">{report.slug || `INC-${report.id.slice(-4)}`}</span>
                  </div>
                </TableCell>
                <TableCell>{format(new Date(report.dateOfIncident), 'MMM d, yyyy h:mm a')}</TableCell>
                <TableCell>
                  <AgencyReportTypeBadge type={report.typeOfIncident} />
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    <span className="truncate max-w-[150px]">{report.oshaLocation?.name || 'No location'}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm text-muted-foreground line-clamp-1 max-w-xs">
                    {report.description || 'No description available'}
                  </div>
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {format(new Date(report.createdAt), 'MMM d, yyyy')}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => navigate(ROUTES.BUILD_OSHA_AGENCY_REPORT_DETAILS_PATH(report.id))}
                  >
                    <Eye className="h-4 w-4 text-blue-600" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
