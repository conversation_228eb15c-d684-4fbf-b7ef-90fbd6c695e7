import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { AlertTriangle, FileText } from 'lucide-react';
import { useLocation } from 'wouter';

export const AgencyReportsEmpty = ({
  hasActiveFilters,
  canCreateReport,
  onResetFilters,
}: {
  hasActiveFilters: boolean;
  canCreateReport?: boolean;
  onResetFilters: () => void;
}) => {
  const isMobile = useIsMobile();
  const { hasPermission } = usePermissions();
  const [_, navigate] = useLocation();

  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
            No agency reports found
          </h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No agency reports match your current filters. Try adjusting your search criteria or clear filters to see all
            agency reports.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no agency reports exist at all
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
          <AlertTriangle className="h-8 w-8 text-red-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          No agency reports reported yet
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          When serious incidents occur (fatalities, hospitalizations, amputations, or eye loss), they must be reported
          to OSHA and other regulatory agencies immediately.
        </p>
        <div className="space-y-3">
          {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE) && canCreateReport && (
            <Button
              size="sm"
              variant="destructive"
              className={`${isMobile ? 'w-full' : ''}`}
              onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS_NEW)}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Report New Serious Incident
            </Button>
          )}
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              Federal OSHA requires reporting within 8 hours of serious incidents.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
