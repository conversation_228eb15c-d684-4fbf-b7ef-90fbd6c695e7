import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { AlertCircle } from 'lucide-react';

export const AgencyReportsError = () => {
  const isMobile = useIsMobile();

  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[300px]' : 'min-h-[400px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
          <AlertCircle className="h-6 w-6 text-red-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          Unable to load agency reports
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          There was a problem loading the agency reports. Please try again or contact support if the problem persists.
        </p>
        <div className="space-y-3">
          <Button onClick={() => window.location.reload()} variant="outline" className={`${isMobile ? 'w-full' : ''}`}>
            Try Again
          </Button>
        </div>
      </div>
    </div>
  );
};
