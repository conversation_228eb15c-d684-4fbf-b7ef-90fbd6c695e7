import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { oshaAgencyReportTypeEnum } from '@shared/schema';
import { OSHA_AGENCY_REPORT_TYPE_MAP, OshaAgencyReportsFilters } from '@shared/osha.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';
import { useState } from 'react';

export function AgencyReportsMobileFilters({
  toggleFilter,
  filters,
  updateFilter,
  activeFilterCount,
  resetFilters,
}: {
  toggleFilter: (type: 'typeOfIncident', value: (typeof oshaAgencyReportTypeEnum.enumValues)[number]) => void;
  filters: OshaAgencyReportsFilters;
  updateFilter: (
    key: keyof OshaAgencyReportsFilters,
    value: OshaAgencyReportsFilters[keyof OshaAgencyReportsFilters],
  ) => void;
  activeFilterCount: number;
  resetFilters: () => void;
}) {
  const [open, setOpen] = useState(false);

  return (
    <div className="md:hidden">
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size={activeFilterCount > 0 ? 'default' : 'icon'}>
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter Agency Reports</SheetTitle>
            <SheetDescription>Apply filters to narrow down the agency reports list.</SheetDescription>
          </SheetHeader>

          <div className="space-y-4 p-4">
            {/* Type Collapsible Section */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Type</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {oshaAgencyReportTypeEnum.enumValues.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type}-mobile`}
                      checked={filters.typeOfIncident?.includes(type)}
                      onCheckedChange={() => toggleFilter('typeOfIncident', type)}
                    />
                    <label htmlFor={`type-${type}-mobile`} className="text-sm cursor-pointer">
                      {OSHA_AGENCY_REPORT_TYPE_MAP[type]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Archive Status */}
            <div className="flex flex-col">
              <h3 className="font-medium mb-2">Archive Status</h3>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  updateFilter('includeArchived', !filters.includeArchived);
                }}
                className={`justify-start ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>

          <SheetFooter className="pt-4">
            <SheetClose asChild>
              <Button variant="outline" className="w-full" onClick={resetFilters}>
                Clear Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
}
