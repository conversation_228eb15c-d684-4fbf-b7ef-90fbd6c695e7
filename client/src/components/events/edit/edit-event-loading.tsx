import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';

export const EditEventLoading = () => {
  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Button variant="ghost" disabled className="gap-1 hover:bg-neutral-300 text-neutral-900">
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <Skeleton className="h-8 w-48" />
      </div>

      {/* Main form container */}
      <div>
        <div>
          <form className="space-y-8">
            {/* Report Type */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>

            {/* Title */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>

            {/* Date and Time */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-3/5" />
            </div>

            {/* Severity Level */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-4/5" />
              </div>
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-4 w-4/5" />
            </div>

            {/* Immediate Actions Taken */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-28 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>

            {/* Root Cause and OSHA Reportable */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              {/* Root Cause */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-4/5" />
              </div>

              {/* OSHA Reportable */}
              <div className="rounded-lg border p-4">
                <div className="flex flex-row items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-48" />
                  </div>
                  <Skeleton className="h-6 w-12 rounded-full" />
                </div>
              </div>
            </div>

            {/* Media section */}
            <div className="space-y-4">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-32 w-full rounded-lg border-2 border-dashed" />
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-28" />
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
