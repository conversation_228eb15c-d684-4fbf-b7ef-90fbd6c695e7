import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAppContext } from '@/contexts/app-context';
import { usePermissions } from '@/hooks/use-permissions';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { ALLOWED_ACTIONS, MODULES, USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ota<PERSON><PERSON>c<PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { REPORT_TYPE_MAP } from '@shared/schema.types';

// Enhanced AI summary with formatted text for better visual presentation
const generateAISummary = (event?: RouterOutputs['event']['getById']) => {
  // This would ideally come from the API but for now we'll generate it client-side
  const severity = event?.severity || 'unknown';
  const type = REPORT_TYPE_MAP[event?.type || 'incident'];
  const dateTime = formatDate(event?.reportedAt || new Date());

  return (
    <>
      <span className="font-semibold">{type}</span> of{' '}
      <span className="font-semibold text-indigo-700">{severity} severity</span> was reported at on {dateTime}.
      {event?.oshaReportable ? (
        <span className="text-red-600 font-medium block mt-2">
          ⚠️ This safety event requires OSHA reporting within 24 hours.
        </span>
      ) : (
        <span className="text-green-600 font-medium block mt-2">
          ✓ No OSHA reporting is required for this safety event.
        </span>
      )}
    </>
  );
};

export const Insight = ({ event }: { event: RouterOutputs['event']['getById'] }) => {
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();
  const { user } = useAppContext();
  const analytics = useAnalytics();

  const { mutateAsync: updateEvent } = trpc.event.update.useMutation({
    onSuccess: () => {
      utils.event.getById.invalidate({ id: event.id });
      utils.auditTrail.get.invalidate({ entityId: event.id, entityType: 'event' });
    },
  });

  const createCapaHandler = (event: RouterOutputs['event']['getById']) => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Create CAPA',
    });
    navigate(`${ROUTES.CAPA_NEW}?eventId=${event.id}`);
  };

  const editEventHandler = (event: RouterOutputs['event']['getById']) => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Edit Event',
    });
    navigate(ROUTES.BUILD_EVENT_EDIT_PATH(event.id));
  };

  const markAsReviewedHandler = async (event: RouterOutputs['event']['getById']) => {
    // Track generic action taken
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Mark as in Review',
    });

    // Track specific status change
    analytics.track(ANALYTICS_EVENTS.EVENT.REVIEWED, {
      event_id: event.id,
      previous_status: event.status,
    });

    await updateEvent({
      id: event.id,
      status: 'in_review',
    });

    toast.success('Mark as in Review', {
      description: 'Safety event marked as in review.',
    });
  };

  const openAgainHandler = async (event: RouterOutputs['event']['getById']) => {
    // Track generic action taken
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Reopen Event',
    });

    // Track specific status change
    analytics.track(ANALYTICS_EVENTS.EVENT.REVIEWED, {
      event_id: event.id,
      previous_status: event.status,
    });

    await updateEvent({
      id: event.id,
      status: 'open',
    });

    toast.success('Opened Again', {
      description: 'Safety event opened again.',
    });
  };

  const closeWithoutActionHandler = async (event: RouterOutputs['event']['getById']) => {
    // Track generic action taken
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Close Without Action',
    });

    // Track specific status change
    analytics.track(ANALYTICS_EVENTS.EVENT.CLOSED_WITHOUT_ACTION, {
      event_id: event.id,
      previous_status: event.status,
    });

    await updateEvent({
      id: event.id,
      status: 'closed',
    });

    toast.success('Close Without Action', {
      description: 'Safety event closed without further action.',
    });
  };

  const actions: Record<
    string,
    { label: string; icon: React.ElementType; onClick: () => void; separator?: boolean; disabled?: boolean }
  > = {
    'create-capa': {
      label: 'Create CAPA',
      icon: ClipboardCheck,
      onClick: () => {
        createCapaHandler(event);
      },
    },
    'edit-safety-event': {
      label: 'Edit Safety Event',
      icon: Edit,
      separator: true,
      onClick: () => {
        editEventHandler(event);
      },
    },
    ...(event.status !== 'open'
      ? {
          'open-again': {
            label: 'Reopen Event',
            icon: RotateCcw,
            onClick: async () => {
              await openAgainHandler(event);
            },
            disabled: !hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT, event?.reportedByUser?.id),
          },
        }
      : {}),
    ...(event.status !== 'in_review'
      ? {
          'mark-as-reviewed': {
            label: 'Mark as In Review',
            icon: CheckCircle2,
            onClick: async () => {
              await markAsReviewedHandler(event);
            },
            disabled: !hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT, event?.reportedByUser?.id),
          },
        }
      : {}),
    ...(event.status !== 'closed'
      ? {
          'close-without-action': {
            label: 'Close Without Action',
            icon: AlertCircle,
            onClick: async () => {
              await closeWithoutActionHandler(event);
            },
            disabled: !hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT, event?.reportedByUser?.id),
          },
        }
      : {}),
  };

  return (
    <div className="relative overflow-hidden bg-blue-50 rounded-lg border border-blue-100 shadow-xs mb-6 before:absolute before:top-0 before:left-0 before:w-1 before:h-full before:bg-indigo-500">
      <div className="p-5">
        <p className="text-gray-800 leading-relaxed text-[15px]">{generateAISummary(event)}</p>
        {user?.role === USER_ACCOUNT_TYPES.ADMIN && (
          <div className="mt-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button>
                        <Wrench className="h-4 w-4" />
                        Take Action
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56" align="start" data-testid="action-menu">
                      <DropdownMenuLabel>Safety Event Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {Object.entries(actions).map(([key, action]) => {
                        if (action.disabled) {
                          return null;
                        }
                        return (
                          <div key={key}>
                            <DropdownMenuItem key={key} onClick={action.onClick}>
                              <action.icon className="mr-2 h-4 w-4" />
                              {action.label}
                            </DropdownMenuItem>
                            {action.separator && <DropdownMenuSeparator />}
                          </div>
                        );
                      })}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Log a CAPA, create a work order, or update the safety event.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>
    </div>
  );
};
