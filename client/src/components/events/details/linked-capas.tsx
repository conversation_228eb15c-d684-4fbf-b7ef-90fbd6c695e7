import { StatusBadge } from '@/components/composite/status-badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { useInfiniteCapas } from '@/hooks/use-paginated-data';
import { ClipboardCheck } from 'lucide-react';
import { useLocation } from 'wouter';
import type { RouterOutputs } from '@shared/router.types';

export const LinkedCapas = ({ eventId }: { eventId: string }) => {
  const [_, navigate] = useLocation();

  const { data: allCapas } = useInfiniteCapas({
    filters: {
      status: [],
      type: [],
      priority: [],
      owner: [],
      dueDateRange: undefined,
      includeArchived: false,
      tags: [],
    },
    enabled: true,
  });

  // Filter CAPAs by eventId
  const capas =
    allCapas?.filter((capa: RouterOutputs['capa']['list']['result'][number]) => capa.eventId === eventId) || [];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Linked CAPAs</h2>
          <Button variant="outline" size="sm" onClick={() => navigate(ROUTES.CAPA_NEW + `?eventId=${eventId}`)}>
            <ClipboardCheck className="h-4 w-4 mr-2" />
            Create CAPA
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {capas && capas.length > 0 && (
          <div className="space-y-3 mb-4">
            {capas.map((capa: RouterOutputs['capa']['list']['result'][number]) => (
              <div key={capa.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-blue-600 font-medium text-sm">
                        {capa.slug}: {capa.title}
                      </h3>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-600">
                      <StatusBadge status={capa.status} />
                      <span>Owner: {capa.owner?.fullName}</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id))}>
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
