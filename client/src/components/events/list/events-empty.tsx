import { Button } from '@/components/ui/button';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { useAnalytics } from '@/hooks/use-analytics';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { useIsMobile } from '@/hooks/use-mobile';
import { AlertTriangle, FileText, Plus } from 'lucide-react';
import { useLocation } from 'wouter';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';

interface EventsEmptyProps {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
}

export const EventsEmpty = ({ hasActiveFilters, onResetFilters }: EventsEmptyProps) => {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();

  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
            No safety events found
          </h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No safety events match your current filters. Try adjusting your search criteria or clear filters to see all
            safety events.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no safety events exist at all
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-orange-100 flex items-center justify-center">
          <AlertTriangle className="h-8 w-8 text-orange-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          No safety events reported yet
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          This is where all safety events will be tracked and managed. When safety events are reported, they'll appear
          here for review and follow-up.
        </p>
        <div className="space-y-3">
          {hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.CREATE) && (
            <Button
              size="sm"
              className={`${isMobile ? 'w-full' : ''}`}
              onClick={() => {
                analytics.track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, {
                  form_entry_point: 'Event Log',
                });
                navigate(ROUTES.EVENT_NEW);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Report New Safety Event
            </Button>
          )}
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              Safety events can be reported through the main application or this interface.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
