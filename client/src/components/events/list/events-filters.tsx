import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import {
  EventsFilters,
  ReportTypeSchema,
  SeveritySchema,
  STATUS_MAP,
  StatusSchema,
  REPORT_TYPE_MAP,
} from '@shared/schema.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';
import z from 'zod';

export const Filters = ({
  toggleFilter,
  filters,
  updateFilters,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  toggleFilter: (
    type: 'status' | 'type' | 'severity',
    value: z.infer<typeof StatusSchema> | z.infer<typeof ReportTypeSchema> | z.infer<typeof SeveritySchema>,
  ) => void;
  filters: EventsFilters;
  updateFilters: (updates: Partial<EventsFilters>) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (
    type: 'status' | 'type' | 'severity' | 'locationIds' | 'oshaReportable' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  // Handle OSHA reportable toggle with tri-state logic
  const handleOshaToggle = () => {
    const newValue = filters.oshaReportable === undefined ? true : filters.oshaReportable === true ? false : undefined;

    trackFilterApplied('oshaReportable', newValue?.toString() ?? 'undefined');
    updateFilters({ oshaReportable: newValue });
  };

  // Handle include archived toggle
  const handleArchiveToggle = () => {
    const newValue = !filters.includeArchived;

    trackFilterApplied('includeArchived', newValue.toString());
    updateFilters({ includeArchived: newValue });
  };

  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.status?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {statusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center space-x-2"
                checked={filters.status?.includes(status)}
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('status', status);
                  trackFilterApplied('status', status);
                }}
              >
                {STATUS_MAP[status]}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.type?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {reportTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center space-x-2"
                checked={filters.type?.includes(type)}
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('type', type);
                  trackFilterApplied('type', type);
                }}
              >
                <span className="capitalize">{REPORT_TYPE_MAP[type]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Severity Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Severity
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.severity?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {severityEnum.enumValues.map((severity) => (
              <DropdownMenuCheckboxItem
                key={severity}
                className="flex items-center space-x-2"
                checked={filters.severity?.includes(severity)}
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('severity', severity);
                  trackFilterApplied('severity', severity);
                }}
              >
                <label className="capitalize">{severity}</label>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <AsyncLocationsFilter
          selected={filters.locationIds}
          onSelect={(locationIds) => {
            updateFilters({ locationIds });
            trackFilterApplied('locationIds', locationIds.join(','));
          }}
          label="Location"
          placeholder="Search location"
        />

        {/* OSHA Reportable Filter */}
        <Button
          variant={filters.oshaReportable !== undefined ? 'default' : 'outline'}
          size="sm"
          onClick={handleOshaToggle}
        >
          OSHA Reportable
          {filters.oshaReportable !== undefined && (
            <span className="ml-2 text-xs">({filters.oshaReportable ? 'Yes' : 'No'})</span>
          )}
        </Button>

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={handleArchiveToggle}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5 mr-1.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
