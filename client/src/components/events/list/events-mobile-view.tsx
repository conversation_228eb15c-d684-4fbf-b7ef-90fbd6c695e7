import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { EventTypeBadge } from '@/components/composite/event-type-badge';
import { SeverityBadge } from '@/components/composite/severity-badge';
import { StatusBadge } from '@/components/composite/status-badge';
import { StatusIndicator } from '@/components/composite/status-indicator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Calendar, Eye, MapPin, MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const EventsMobileView = ({ events }: { events: RouterOutputs['event']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<RouterOutputs['event']['list']['result'][number] | null>(null);

  const handleRowAction = (eventId: string, action: 'View' | 'Edit') => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED, {
      event_id: eventId,
      action,
    });
  };

  return (
    <div className="space-y-3">
      {selectedEvent && (
        <ArchiveConfirmationDialog
          archived={selectedEvent?.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedEvent.id}
          entityType="event"
        />
      )}

      {events.map((event) => (
        <Card
          key={event.id}
          className={`group relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200 ${
            event.archived ? 'bg-gradient-to-r from-amber-50/80 to-amber-50/60' : 'bg-white hover:bg-gray-50/50'
          }`}
          onClick={() => {
            handleRowAction(event.id, 'View');
            navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(event.id));
          }}
        >
          <CardContent>
            <div className="flex">
              {/* Status indicator line - left side */}
              <StatusIndicator status={event.status} archived={event.archived} />

              {/* Content Section */}
              <div className="flex-1">
                {/* Header Section */}
                <div className="px-4">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium text-muted-foreground">{event.slug}</span>
                        {event.oshaReportable && (
                          <Badge
                            className="bg-red-100 text-red-700 border-red-200 text-xs font-medium"
                            variant="outline"
                          >
                            OSHA
                          </Badge>
                        )}
                        {event.archived && (
                          <Badge
                            className="bg-amber-100 text-amber-700 border-amber-200 text-xs font-medium"
                            variant="outline"
                          >
                            <Archive className="h-3 w-3 mr-1" />
                            Archived
                          </Badge>
                        )}
                      </div>

                      <h3 className="font-semibold text-gray-900 text-base leading-tight line-clamp-2 mb-2">
                        {event.title}
                      </h3>
                    </div>

                    <div className="flex flex-col items-end gap-2">
                      <StatusBadge status={event.status} />
                      <SeverityBadge severity={event.severity} />
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="px-4 pb-3">
                  <div className="space-y-2.5">
                    {/* Type and Date */}
                    <div className="flex items-center justify-between">
                      <EventTypeBadge type={event.type} />
                      <div className="flex items-center text-xs text-gray-500">
                        <Calendar className="h-3 w-3 mr-1.5" />
                        {format(new Date(event.reportedAt), 'MMM d, yyyy')}
                      </div>
                    </div>

                    {/* Location */}
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-3.5 w-3.5 mr-2 text-gray-400" />
                      <span className="truncate">{event?.location?.name || 'No location'}</span>
                    </div>
                  </div>
                </div>

                {/* Actions Section */}
                <div className="px-4">
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center gap-1.5">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRowAction(event.id, 'View');
                          navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(event.id));
                        }}
                      >
                        <Eye className="h-3.5 w-3.5 mr-1.5" />
                        View
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRowAction(event.id, 'Edit');
                          navigate(ROUTES.BUILD_EVENT_EDIT_PATH(event.id));
                        }}
                      >
                        <Pencil className="h-3.5 w-3.5 mr-1.5" />
                        Edit
                      </Button>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                        {hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT) && (
                          <DropdownMenuItem
                            className={event.archived ? 'text-amber-600' : 'text-red-600'}
                            onClick={async (e) => {
                              e.stopPropagation();
                              setSelectedEvent(event);
                              setShowArchiveConfirm(true);
                              toast.success(event.archived ? 'Event Unarchived' : 'Event Archived', {
                                description: event.archived
                                  ? `${event.slug} has been restored to its previous status.`
                                  : `${event.slug} has been archived.`,
                              });
                            }}
                          >
                            {event.archived ? (
                              <>
                                <Archive className="h-4 w-4 mr-2" />
                                Unarchive
                              </>
                            ) : (
                              <>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Archive
                              </>
                            )}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
