import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { SeverityBadge } from '@/components/composite/severity-badge';
import { StatusBadge } from '@/components/composite/status-badge';
import { EventTypeBadge } from '@/components/composite/event-type-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Flag, Info, MapPin, MoreHorizontal, Pencil } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';

export const EventsTable = ({ events }: { events: RouterOutputs['event']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<RouterOutputs['event']['list']['result'][number] | null>(null);

  // Helper function to track row actions
  const handleRowAction = (eventId: string, action: 'View' | 'Edit' | 'Create CAPA' | 'Archive') => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED, {
      event_id: eventId,
      action,
    });
  };

  return (
    <div className="overflow-hidden">
      {selectedEvent && (
        <ArchiveConfirmationDialog
          archived={selectedEvent?.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedEvent.id}
          entityType="event"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Report ID & Title</TableHead>
            <TableHead className="w-[100px]">Type</TableHead>
            <TableHead className="w-[110px]">Status</TableHead>
            <TableHead className="w-[110px]">Severity</TableHead>
            <TableHead>Location</TableHead>
            <TableHead className="w-[130px]">Date & Time</TableHead>
            <TableHead className="text-center">OSHA</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {events.map((event: RouterOutputs['event']['list']['result'][number]) => {
            return (
              <TableRow
                key={event.id}
                className={`cursor-pointer hover:bg-muted/50 ${
                  event.archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''
                }`}
                onClick={() => {
                  handleRowAction(event.id, 'View');
                  navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(event.id));
                }}
              >
                <TableCell>
                  <div>
                    <div className="flex items-center gap-1">
                      <span className="font-medium">{event.slug}</span>
                      {event.archived && (
                        <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                          <Archive className="h-3 w-3 mr-1" />
                          Archived
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground line-clamp-1">{event.title}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <EventTypeBadge type={event.type} />
                </TableCell>
                <TableCell>
                  <StatusBadge status={event.status} />
                </TableCell>
                <TableCell>
                  <SeverityBadge severity={event.severity} />
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span className="truncate max-w-[150px]">{event.location?.name || 'No location'}</span>
                        </div>
                      </TooltipTrigger>
                      {event.location && (
                        <TooltipContent>
                          <p>{event.location?.name}</p>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>{format(new Date(event.reportedAt), 'MMM d, yyyy h:mm a')}</TableCell>

                <TableCell align="center">
                  {event.oshaReportable ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            className="bg-red-50 text-red-600 border-red-200 h-6 w-6 p-0 flex items-center justify-center"
                            variant="outline"
                          >
                            <Flag className="h-3 w-3" />
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>OSHA Reportable</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    <span className="text-muted-foreground">No</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleRowAction(event.id, 'View');
                          navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(event.id));
                        }}
                      >
                        <Info className="mr-2 h-4 w-4" />
                        <span>View Details</span>
                      </DropdownMenuItem>
                      {hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT, event.reportedBy ?? undefined) && (
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleRowAction(event.id, 'Edit');

                            // Track edit initiated from log table
                            analytics.track(ANALYTICS_EVENTS.EVENT.EDIT_INITIATED, {
                              event_id: event.id,
                              source: 'log_table',
                            });

                            navigate(ROUTES.BUILD_EVENT_EDIT_PATH(event.id));
                          }}
                        >
                          <Pencil className="mr-2 h-4 w-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                      )}
                      {hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EDIT) && (
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedEvent(event);
                            setShowArchiveConfirm(true);
                          }}
                        >
                          <Archive className="mr-2 h-4 w-4" />
                          <span>{event.archived ? 'Unarchive' : 'Archive'}</span>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
