import { capaTagsEnum } from '@shared/schema';
import { CAPA_TAGS_MAP } from '@shared/schema.types';
import { Check } from 'lucide-react';

export const CapaTags = ({
  value,
  onChange,
}: {
  value: string[] | null | undefined;
  onChange: (value: string[]) => void;
}) => {
  return (
    <div className="flex flex-wrap gap-2">
      {capaTagsEnum.enumValues.map((tag) => (
        <div
          key={tag}
          className={`inline-flex items-center px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
            value?.includes(tag)
              ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
          onClick={() => {
            const newTags = value?.includes(tag) ? value?.filter((t) => t !== tag) : [...(value || []), tag];
            onChange(newTags);
          }}
        >
          {value?.includes(tag) && <Check className="size-4 mr-1" />}
          {CAPA_TAGS_MAP[tag]}
        </div>
      ))}
    </div>
  );
};
