import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';

export const EditCapaLoading = () => {
  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Button variant="ghost" disabled className="gap-1 hover:bg-neutral-300 text-neutral-900">
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <Skeleton className="h-8 w-48" />
      </div>

      {/* Main form container */}
      <div>
        <div>
          <form className="space-y-8">
            {/* CAPA Summary Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-40 border-b" />

              {/* CAPA Type */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>

              {/* Title */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/5" />
              </div>
            </div>

            {/* Location */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-4/5" />
            </div>

            {/* Assets */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>

            {/* Action Details Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-32 border-b" />

              {/* Root Cause */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>

              {/* Actions to Address */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-40" />
                <Skeleton className="h-36 w-full" />
                <Skeleton className="h-4 w-4/5" />
              </div>

              {/* RCA Method */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-1/2" />
              </div>

              {/* RCA Findings */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-28 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-12" />
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-8 w-20 rounded-full" />
                  <Skeleton className="h-8 w-24 rounded-full" />
                  <Skeleton className="h-8 w-18 rounded-full" />
                  <Skeleton className="h-8 w-22 rounded-full" />
                  <Skeleton className="h-8 w-26 rounded-full" />
                </div>
                <Skeleton className="h-4 w-2/3" />
              </div>
            </div>

            {/* Actual Actions Taken Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-44 border-b" />

              {/* Actions Implemented */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-36" />
                <Skeleton className="h-28 w-full" />
                <Skeleton className="h-4 w-1/2" />
              </div>

              {/* Implemented By and Implementation Date */}
              <div className="flex flex-col md:flex-row md:items-center gap-2">
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-36" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
            </div>

            {/* VoE Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-56 border-b" />

              {/* VoE Due Date */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-1/2" />
              </div>

              {/* VoE Performed By and VoE Date */}
              <div className="flex flex-col md:flex-row md:items-center gap-2">
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>

              {/* VoE Findings */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-28 w-full" />
                <Skeleton className="h-4 w-3/5" />
              </div>

              {/* Effectiveness Status */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-36" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-1/3" />
              </div>
            </div>

            {/* Assignment Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-24 border-b" />

              {/* Owner and Due Date */}
              <div className="flex flex-col md:flex-row md:items-center gap-2">
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-4/5" />
                </div>
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>

              {/* Priority */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>

            {/* Linkage Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-20 border-b" />

              {/* Linked Event */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>

            {/* Attachments Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-28 border-b" />

              {/* File Uploads */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-32 w-full rounded-lg border-2 border-dashed" />
                <Skeleton className="h-4 w-4/5" />
              </div>
            </div>

            {/* Team Notifications Section */}
            <div className="space-y-6">
              <Skeleton className="h-6 w-40 border-b" />

              {/* Private to Admins Toggle */}
              <div className="rounded-lg border p-4">
                <div className="flex flex-row items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-36" />
                    <Skeleton className="h-3 w-56" />
                  </div>
                  <Skeleton className="h-6 w-12 rounded-full" />
                </div>
              </div>
            </div>

            {/* Submit and Cancel Buttons */}
            <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
