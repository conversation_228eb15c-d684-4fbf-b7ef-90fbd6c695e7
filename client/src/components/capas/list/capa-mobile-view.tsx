import { CapaTypeBadge } from '@/components/capas/list/capa-type-badge';
import { CapasEmpty } from '@/components/capas/list/capas-empty';
import { PriorityBadge } from '@/components/capas/list/priority-badge';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { StatusBadge } from '@/components/composite/status-badge';
import { StatusIndicator } from '@/components/composite/status-indicator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { CAPA_TAGS_MAP } from '@shared/schema.types';
import { format } from 'date-fns';
import { Archive, Calendar, Eye, Link, MoreHorizontal, Pencil, Trash2, User } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';
import { useAnalytics } from '@/hooks/use-analytics';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';

export const CapaMobileView = ({
  capas,
  activeFilterCount,
  resetFilters,
}: {
  capas?: RouterOutputs['capa']['list']['result'];
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const [selectedCapa, setSelectedCapa] = useState<RouterOutputs['capa']['list']['result'][number] | null>(null);
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);

  const handleRowAction = (capaId: string, action: 'View' | 'Edit') => {
    analytics.track(ANALYTICS_EVENTS.CAPA.ROW_ACTION_CLICKED, {
      capa_id: capaId,
      action,
    });
  };

  return (
    <div className="space-y-3">
      {selectedCapa && (
        <ArchiveConfirmationDialog
          archived={selectedCapa.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedCapa.id}
          entityType="capa"
        />
      )}

      {capas?.length === 0 ? (
        <CapasEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={resetFilters} />
      ) : (
        capas?.map((capa) => (
          <Card
            key={capa.id}
            className={`group relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200 ${
              capa.archived ? 'bg-gradient-to-r from-amber-50/80 to-amber-50/60' : 'bg-white hover:bg-gray-50/50'
            }`}
            onClick={() => {
              handleRowAction(capa.id, 'View');
              navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
            }}
          >
            <CardContent>
              <div className="flex">
                {/* Status indicator line - left side */}
                <StatusIndicator
                  status={capa.status as (typeof statusEnum.enumValues)[number]}
                  archived={capa.archived}
                />

                {/* Content Section */}
                <div className="flex-1">
                  {/* Header Section */}
                  <div className="px-4">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-muted-foreground">{capa.slug}</span>
                          {capa.archived && (
                            <Badge
                              className="bg-amber-100 text-amber-700 border-amber-200 text-xs font-medium"
                              variant="outline"
                            >
                              <Archive className="h-3 w-3 mr-1" />
                              Archived
                            </Badge>
                          )}
                        </div>

                        <h3 className="font-semibold text-gray-900 text-base leading-tight line-clamp-2 mb-2">
                          {capa.title}
                        </h3>
                      </div>

                      <div className="flex flex-col items-end gap-2">
                        <StatusBadge status={capa.status as (typeof statusEnum.enumValues)[number]} />
                        <PriorityBadge priority={capa.priority as (typeof capaPriorityEnum.enumValues)[number]} />
                      </div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="px-4 pb-3">
                    <div className="space-y-2.5">
                      {/* Type and Tags */}
                      <div className="flex items-center justify-between">
                        <CapaTypeBadge type={capa.type as (typeof capaTypeEnum.enumValues)[number]} />
                        {capa.dueDate && (
                          <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="h-3 w-3 mr-1.5" />
                            {format(new Date(capa.dueDate), 'MMM d, yyyy')}
                          </div>
                        )}
                      </div>

                      {/* Tags */}
                      {capa.tags && capa.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {capa.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="bg-gray-100 text-xs px-1.5 py-0">
                              {CAPA_TAGS_MAP[tag as (typeof capaTagsEnum.enumValues)[number]] || tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Owner */}
                      <div className="flex items-center text-sm text-gray-600">
                        <User className="h-3.5 w-3.5 mr-2 text-gray-400" />
                        <span className="truncate">{capa.owner?.fullName || 'Unassigned'}</span>
                      </div>

                      {/* Linked Event */}
                      {capa.eventId && (
                        <div className="flex items-center text-blue-600 font-medium">
                          <Button
                            variant="link"
                            className="text-blue-600 p-0 cursor-pointer h-auto text-sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              analytics.track(ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED, {
                                event_id: capa.eventId!,
                                action: 'View',
                              });
                              navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(capa.eventId!));
                            }}
                          >
                            <Link className="h-3 w-3 mr-1" />
                            {capa.eventSlug}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions Section */}
                  <div className="px-4">
                    <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                      <div className="flex items-center gap-1.5">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRowAction(capa.id, 'View');
                            navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                          }}
                        >
                          <Eye className="h-3.5 w-3.5 mr-1.5" />
                          View
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRowAction(capa.id, 'Edit');
                            analytics.track(ANALYTICS_EVENTS.CAPA.EDIT_INITIATED, {
                              capa_id: capa.id,
                              source: 'tracker_table',
                            });
                            navigate(ROUTES.BUILD_CAPA_EDIT_PATH(capa.id));
                          }}
                        >
                          <Pencil className="h-3.5 w-3.5 mr-1.5" />
                          Edit
                        </Button>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                          <DropdownMenuItem
                            className={capa.archived ? 'text-amber-600' : 'text-red-600'}
                            onClick={async (e) => {
                              e.stopPropagation();
                              setSelectedCapa(capa);
                              setShowArchiveConfirm(true);
                            }}
                          >
                            {capa.archived ? (
                              <>
                                <Archive className="h-4 w-4 mr-2" />
                                Unarchive
                              </>
                            ) : (
                              <>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Archive
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
};
