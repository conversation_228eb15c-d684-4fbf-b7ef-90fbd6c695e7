import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { capaPriorityEnum } from '@shared/schema';
import { CAPA_PRIORITY_MAP } from '@shared/schema.types';
import { Flag } from 'lucide-react';

export const PriorityBadge = ({ priority }: { priority: (typeof capaPriorityEnum.enumValues)[number] }) => {
  const priorityLabel = CAPA_PRIORITY_MAP[priority];

  const priorityIconMap = {
    high: <Flag className="h-3 w-3 mr-1" />,
    medium: null,
    low: null,
  };

  const priorityColorMap = {
    high: 'bg-red-50 text-red-700 border-red-200',
    medium: 'bg-orange-50 text-orange-700 border-orange-200',
    low: 'bg-green-50 text-green-700 border-green-200',
  };

  return (
    <Badge className={cn(priorityColorMap[priority])} variant="outline">
      {priorityIconMap[priority]}
      {priorityLabel}
    </Badge>
  );
};
