import { CapaTags } from '@/components/capas/list/capa-tags';
import { CapaTypeBadge } from '@/components/capas/list/capa-type-badge';
import { PriorityBadge } from '@/components/capas/list/priority-badge';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { StatusBadge } from '@/components/composite/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { capaPriorityEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { format } from 'date-fns';
import { Archive, Calendar, Eye, Link, MoreHorizontal, Pencil, Trash2, User } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';
import { useAnalytics } from '@/hooks/use-analytics';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';

export const CapaTable = ({ capas }: { capas?: RouterOutputs['capa']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedCapa, setSelectedCapa] = useState<RouterOutputs['capa']['list']['result'][number] | null>(null);
  const handleRowAction = (capaId: string, action: 'View' | 'Edit') => {
    analytics.track(ANALYTICS_EVENTS.CAPA.ROW_ACTION_CLICKED, {
      capa_id: capaId,
      action,
    });
  };

  return (
    <div className="overflow-hidden">
      {selectedCapa && (
        <ArchiveConfirmationDialog
          archived={selectedCapa.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedCapa.id}
          entityType="capa"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-[350px]">CAPA</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Linked Safety Event</TableHead>
            <TableHead>Owner</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead>Tags</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {capas?.map((capa: RouterOutputs['capa']['list']['result'][number]) => {
            return (
              <TableRow
                key={capa.id}
                className={`cursor-pointer ${capa.archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
                onClick={() => {
                  handleRowAction(capa.id, 'View');
                  navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                }}
              >
                <TableCell className="max-w-[350px]">
                  <div className="flex flex-col">
                    <div className="font-medium">{capa.slug}</div>
                    <div className="text-sm text-muted-foreground truncate">{capa.title}</div>
                    {capa.archived && (
                      <Badge className="mt-1 w-fit bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                        <Archive className="h-3 w-3 mr-1" />
                        Archived
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <CapaTypeBadge type={capa.type as (typeof capaTypeEnum.enumValues)[number]} />
                </TableCell>
                <TableCell>
                  <StatusBadge status={capa.status as (typeof statusEnum.enumValues)[number]} />
                </TableCell>
                <TableCell>
                  <PriorityBadge priority={capa.priority as (typeof capaPriorityEnum.enumValues)[number]} />
                </TableCell>
                <TableCell>
                  {capa.eventId ? (
                    <div
                      className="text-blue-600 font-medium flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        analytics.track(ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED, {
                          event_id: capa.eventId!,
                          action: 'View',
                        });
                        navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(capa.eventId!));
                      }}
                    >
                      <Link className="h-4 w-4 mr-1.5" />
                      {capa.eventSlug}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">—</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1.5 text-gray-400" />
                    <span>{capa?.owner?.fullName || 'Unknown User'}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {capa.dueDate ? (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1.5 text-gray-400" />
                      {format(new Date(capa.dueDate), 'MMM d, yyyy')}
                    </div>
                  ) : (
                    <span className="text-gray-400">No due date</span>
                  )}
                </TableCell>
                <TableCell>{capa.tags && capa.tags.length > 0 && <CapaTags tags={capa.tags} />}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-1" onClick={(e) => e.stopPropagation()}>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRowAction(capa.id, 'View');
                        navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRowAction(capa.id, 'Edit');
                        navigate(ROUTES.BUILD_CAPA_EDIT_PATH(capa.id));
                      }}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          className={capa.archived ? 'text-amber-600' : 'text-red-600'}
                          onClick={async (e) => {
                            e.stopPropagation();
                            setSelectedCapa(capa);
                            setShowArchiveConfirm(true);
                          }}
                        >
                          {capa.archived ? (
                            <>
                              <Archive className="h-4 w-4 mr-2" />
                              Unarchive
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Archive
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
