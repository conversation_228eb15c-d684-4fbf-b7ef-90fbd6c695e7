import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { CAPA_PRIORITY_MAP, CAPA_TAGS_MAP, CAPA_TYPE_MAP, CapasFilters, STATUS_MAP } from '@shared/schema.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

export const Filters = ({
  filters,
  toggleFilter,
  activeFilterCount,
  resetFilters,
  updateFilter,
  trackFilterApplied,
}: {
  filters: CapasFilters;
  toggleFilter: (type: 'status' | 'type' | 'priority' | 'owner' | 'tags', value: string) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  updateFilter: (key: keyof CapasFilters, value: CapasFilters[keyof CapasFilters]) => void;
  trackFilterApplied: (
    type: 'status' | 'type' | 'priority' | 'owner' | 'tags' | 'dueDateRange' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex gap-3 items-center">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        {/* Status Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.status?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            {statusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('status', status);
                  trackFilterApplied('status', status);
                }}
                checked={filters.status?.includes(status)}
              >
                <span>{STATUS_MAP[status]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Type Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.type?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            {capaTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('type', type);
                  trackFilterApplied('type', type);
                }}
                checked={filters.type?.includes(type)}
              >
                <span>{CAPA_TYPE_MAP[type]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Priority Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Priority
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.priority?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            {capaPriorityEnum.enumValues.map((priority) => (
              <DropdownMenuCheckboxItem
                key={priority}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('priority', priority);
                  trackFilterApplied('priority', priority);
                }}
                checked={filters.priority?.includes(priority)}
              >
                <span>{CAPA_PRIORITY_MAP[priority]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Tags Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Tags
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.tags?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            {capaTagsEnum.enumValues.map((tag) => (
              <DropdownMenuCheckboxItem
                key={tag}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('tags', tag);
                  trackFilterApplied('tags', tag);
                }}
                checked={filters.tags?.includes(tag)}
              >
                <span>{CAPA_TAGS_MAP[tag]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Owner Filter Dropdown */}

        <AsyncUsersFilter
          selected={filters.owner}
          onSelect={(owner) => {
            updateFilter('owner', owner);
            trackFilterApplied('owner', owner.join(','));
          }}
          label="Owner"
          placeholder="Search owner"
        />

        {/* Due Date Filter */}
        <DateRangePicker
          placeholder="Due Date Range"
          range={{
            from: filters.dueDateRange?.from,
            to: filters.dueDateRange?.to,
          }}
          setRange={(range) => {
            updateFilter('dueDateRange', range);
            trackFilterApplied('dueDateRange', range?.from?.toISOString() + ' - ' + range?.to?.toISOString() || '');
          }}
        />

        {/* Show Archived Checkbox */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => {
            updateFilter('includeArchived', !filters.includeArchived);
            trackFilterApplied('includeArchived', `${!filters.includeArchived}`);
          }}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
