import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RouterOutputs } from '@shared/router.types';
import { getYear } from 'date-fns';
import { Building } from 'lucide-react';

export const EstablishmentInformationDetails = ({
  establishmentInfo,
  onEdit,
}: {
  establishmentInfo?: RouterOutputs['oshaSummary']['getEstablishmentInformation'];
  onEdit: () => void;
}) => {
  return (
    <Card className="pb-5">
      <CardHeader>
        <CardTitle className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
              <Building className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <div>Establishment Information</div>
              {establishmentInfo && (
                <div className="text-sm font-normal text-gray-600 mt-1">
                  Consolidated view for {getYear(establishmentInfo?.createdAt)} at{' '}
                  {establishmentInfo?.oshaLocation?.name}
                </div>
              )}
            </div>
          </div>
          {!establishmentInfo?.archived && (
            <div>
              <Button variant="outline" size="sm" onClick={onEdit}>
                Edit Summary
              </Button>
            </div>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          {/* Company Name */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Company Name</Label>
            <p className="text-gray-900 font-medium">{establishmentInfo?.companyName ?? 'Not specified'}</p>
          </div>

          {/* Facility ID */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Facility ID</Label>
            <p className="text-gray-900 font-medium">{establishmentInfo?.companyFacilityId ?? 'Not specified'}</p>
          </div>

          {/* NAICS Code */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">NAICS Code</Label>
            <p className="text-gray-900 font-medium">{establishmentInfo?.companyNAICSCode ?? 'Not specified'}</p>
          </div>

          {/* EIN */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">EIN</Label>
            <p className="text-gray-900 font-medium">{establishmentInfo?.companyEIN ?? 'Not specified'}</p>
          </div>

          {/* Year */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Year</Label>
            <p className="text-gray-900 font-medium">{establishmentInfo?.year ?? 'Not specified'}</p>
          </div>

          {/* Annual Average Number of Employees */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Annual Average Number of Employees</Label>
            <p className="text-gray-900 font-medium">
              {establishmentInfo?.companyAnnualAverageNumberOfEmployees ?? 'Not specified'}
            </p>
          </div>

          {/* Total Hours Worked */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Total Hours Worked</Label>
            <p className="text-gray-900 font-medium">{establishmentInfo?.companyTotalHoursWorked ?? 'Not specified'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
