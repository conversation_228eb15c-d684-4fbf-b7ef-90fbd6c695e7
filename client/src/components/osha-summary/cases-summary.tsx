import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { AlertCircle, Calendar, CheckCircle, FileText, XCircle } from 'lucide-react';
import { useLocation } from 'wouter';

export const CasesSummary = ({
  summary,
  year,
}: {
  summary?: RouterOutputs['oshaSummary']['getOshaCasesSummary'];
  year: number;
}) => {
  const [_, navigate] = useLocation();

  // Helper function to navigate to OSHA reports with filters
  const navigateToOshaReports = (filterType: 'deaths' | 'daysAway' | 'restricted' | 'other' | 'total') => {
    const baseUrl = ROUTES.OSHA_REPORTS;
    const params = new URLSearchParams();

    // Always include the year filter
    params.set('year', year.toString());

    // Add specific filters based on the case type
    switch (filterType) {
      case 'deaths':
        params.set('caseType', 'fatality');
        break;
      case 'daysAway':
        params.set('caseType', 'days_away_from_work');
        break;
      case 'restricted':
        params.set('caseType', 'job_restriction');
        break;
      case 'other':
        params.set('caseType', 'medical_treatment_beyond_first_aid,loss_of_consciousness,significant_injury');
        break;
      case 'total':
        // For total cases, just show all cases for the year
        break;
    }

    const url = `${baseUrl}?${params.toString()}`;
    navigate(url);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center w-full">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
            <AlertCircle className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <div>Cases Summary</div>
            <div className="text-sm font-normal text-gray-600 ">Injury & illness statistics</div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div
              className={cn(
                'bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow',
                summary?.deaths && 'cursor-pointer',
              )}
              onClick={() => summary?.deaths && navigateToOshaReports('deaths')}
            >
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Deaths</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                  <XCircle className="h-4 w-4 text-gray-700" />
                </div>
              </div>
              <p className="text-2xl font-bold text-gray-900 mt-2 hover:text-blue-600 transition-colors">
                {summary?.deaths || 0}
              </p>
            </div>
            <div
              className={cn(
                'bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow',
                summary?.totalDaysAway && 'cursor-pointer',
              )}
              onClick={() => summary?.totalDaysAway && navigateToOshaReports('daysAway')}
            >
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Days Away</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600 mt-2 hover:text-blue-800 transition-colors">
                {summary?.totalDaysAway || 0}
              </p>
            </div>
            <div
              className={cn(
                'bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow',
                summary?.totalDaysRestricted && 'cursor-pointer',
              )}
              onClick={() => summary?.totalDaysRestricted && navigateToOshaReports('restricted')}
            >
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Restricted Work</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600 mt-2 hover:text-blue-800 transition-colors">
                {summary?.totalDaysRestricted || 0}
              </p>
            </div>
            <div
              className={cn(
                'bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow',
                summary?.otherCases && 'cursor-pointer',
              )}
              onClick={() => summary?.otherCases && navigateToOshaReports('other')}
            >
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Other Cases</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                  <FileText className="h-4 w-4 text-gray-700" />
                </div>
              </div>
              <p className="text-2xl font-bold text-gray-900 mt-2 hover:text-blue-600 transition-colors">
                {summary?.otherCases || 0}
              </p>
            </div>
          </div>

          <div
            className={cn(
              'bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-xl border border-gray-200 hover:shadow-md transition-shadow',
              summary?.totalCases && 'cursor-pointer',
            )}
            onClick={() => summary?.totalCases && navigateToOshaReports('total')}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mr-3">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                </div>
                <Label className="text-lg font-semibold text-gray-800">Total Cases</Label>
              </div>
              <p className="text-3xl font-bold text-blue-600 hover:text-blue-800 transition-colors">
                {summary?.totalCases || 0}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-semibold text-gray-700">TRC Rate</Label>
                <Tooltip>
                  <TooltipTrigger>
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                      <AlertCircle className="h-4 w-4 text-blue-600" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent align="end">
                    <p>
                      <strong>Total Recordable Cases Rate (TRC Rate)</strong>
                      <br />
                      Also known as TRIR (Total Recordable Incident Rate),
                      <br />
                      This measures all OSHA-recordable injuries and illnesses <br /> per 200,000 hours worked.
                      <br />
                      <br />
                      <strong>Formula:</strong> <br /> (Total recordable cases × 200,000) ÷ Total hours worked.
                      <br />
                      <br />
                      <strong>Includes:</strong> injuries requiring treatment beyond <br /> first aid, job restrictions,
                      or days away.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <p className="text-2xl font-bold text-blue-600">{summary?.trcRate || 0}</p>
              <p className="text-xs text-gray-500 mt-1">per 200,000 hours</p>
            </div>
            <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-semibold text-gray-700">DART Rate</Label>
                <Tooltip>
                  <TooltipTrigger>
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                      <AlertCircle className="h-4 w-4 text-blue-600" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent align="end">
                    <p>
                      <strong>Days Away, Restricted, or Transferred Rate (DART)</strong>
                      <br />
                      Indicates the rate of serious OSHA-recordable cases <br /> causing employees to miss work or
                      perform modified duties.
                      <br />
                      <br />
                      <strong>Formula:</strong> <br /> (Number of DART cases × 200,000) ÷ Total hours worked.
                      <br />
                      <br />
                      A key metric for evaluating the severity <br /> and impact of workplace injuries.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <p className="text-2xl font-bold text-blue-600">{summary?.dartRate || 0}</p>
              <p className="text-xs text-gray-500 mt-1">per 200,000 hours</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
