import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { generateYears } from '@/lib/generate-years';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  OshaSummaryFilters,
  UpsertOshaCompanyInformation,
  UpsertOshaCompanyInformationSchema,
} from '@shared/osha.types';
import { RouterOutputs } from '@shared/router.types';
import { getYear } from 'date-fns';
import { Building } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const FormSchema = UpsertOshaCompanyInformationSchema;

export const EstablishmentInformationForm = ({
  filters,
  establishmentInfo,
  onCancel,
  onSave,
}: {
  filters: OshaSummaryFilters;
  establishmentInfo?: RouterOutputs['oshaSummary']['getEstablishmentInformation'];
  onCancel: () => void;
  onSave: () => void;
}) => {
  const utils = trpc.useUtils();

  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      companyName: establishmentInfo?.companyName || '',
      companyFacilityId: establishmentInfo?.companyFacilityId || '',
      companyNAICSCode: establishmentInfo?.companyNAICSCode || undefined,
      companyEIN: establishmentInfo?.companyEIN || '',
      year: filters.year,
      companyAnnualAverageNumberOfEmployees: establishmentInfo?.companyAnnualAverageNumberOfEmployees || undefined,
      companyTotalHoursWorked: establishmentInfo?.companyTotalHoursWorked || undefined,
      oshaLocationId: filters.oshaLocationId,
    },
    mode: 'onSubmit',
  });

  const { mutateAsync: upsertOshaCompanyInformation, isPending } =
    trpc.oshaSummary.upsertEstablishmentInformation.useMutation({
      onSuccess: () => {
        utils.oshaSummary.getEstablishmentInformation.invalidate();
      },
    });

  const onSubmit = async (data: UpsertOshaCompanyInformation) => {
    if (!filters.oshaLocationId) {
      toast.error('OSHA Location is required', {
        description: 'Please select an OSHA Location from the dropdown above',
      });
      return;
    }

    await upsertOshaCompanyInformation({ ...data, oshaLocationId: filters.oshaLocationId });
    onSave();
  };

  return (
    <Card className="pb-9">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
                  <Building className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <div>Establishment Information</div>
                  {establishmentInfo && (
                    <div className="text-sm font-normal text-gray-600 mt-1">
                      Consolidated view for {getYear(establishmentInfo?.createdAt)} at{' '}
                      {establishmentInfo?.oshaLocation?.name}
                    </div>
                  )}{' '}
                </div>
              </div>
              <div>
                <Button variant="outline" size="sm" onClick={onCancel} className="mr-2">
                  Cancel
                </Button>

                <Button type="submit" size="sm" disabled={isPending} onClick={() => form.handleSubmit(onSubmit)}>
                  {isPending ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>

          <CardContent className="pt-6">
            <div className="space-y-6">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter company name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyFacilityId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Facility ID <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter facility ID" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyNAICSCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      NAICS Code <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter NAICS code" value={field.value || ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center flex-col md:flex-row gap-4">
                <FormField
                  control={form.control}
                  name="companyEIN"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>
                        EIN <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter EIN" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Year <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value?.toString() || ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select year" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {generateYears().map((year) => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="companyAnnualAverageNumberOfEmployees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Annual Average Number of Employees <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter annual average number of employees"
                        inputMode="numeric"
                        type="number"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyTotalHoursWorked"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Total Hours Worked <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter total hours worked"
                        inputMode="numeric"
                        type="number"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </form>
      </Form>
    </Card>
  );
};
