import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Draw } from '@/components/ui/draw';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { OshaSummaryExecutiveCertification, OshaSummaryExecutiveCertificationSchema } from '@shared/osha.types';
import { RouterOutputs } from '@shared/router.types';
import { FileText } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const FormSchema = OshaSummaryExecutiveCertificationSchema;

export const CompanyExecutiveCertificationForm = ({
  establishmentInfo,
  onSave,
  onCancel,
}: {
  establishmentInfo?: RouterOutputs['oshaSummary']['getEstablishmentInformation'];
  onSave?: () => void;
  onCancel?: () => void;
}) => {
  const utils = trpc.useUtils();

  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      executiveName: establishmentInfo?.executiveName || '',
      executiveTitle: establishmentInfo?.executiveTitle || '',
      dateCertified: establishmentInfo?.dateCertified || undefined,
      digitalSignature: establishmentInfo?.digitalSignature || '',
      id: establishmentInfo?.id ?? '',
    },
    mode: 'onSubmit',
  });

  const { mutateAsync: upsertOshaCompanyInformation } = trpc.oshaSummary.upsertExecutiveCertification.useMutation({
    onSuccess: () => {
      utils.oshaSummary.getEstablishmentInformation.invalidate();
    },
  });

  const onSubmit = async (data: OshaSummaryExecutiveCertification) => {
    if (!establishmentInfo?.id) {
      toast.error('Establishment information is required', {
        description: 'Please save the establishment information before saving the executive certification',
      });
      return;
    }

    await upsertOshaCompanyInformation({
      ...data,
      id: establishmentInfo?.id,
    });
    onSave?.();
  };

  return (
    <Card>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <div>Company Executive Certification</div>
                  <div className="text-sm font-normal text-gray-600 mt-1">Digital signature and certification</div>
                </div>
              </div>
              <div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    form.reset();
                    onCancel?.();
                  }}
                  className="mr-2"
                >
                  Cancel
                </Button>

                <Button type="submit" size="sm" className="mr-2">
                  Save
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6 space-y-6">
            <FormField
              control={form.control}
              name="executiveName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Name of Company Executive <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter executive name" value={field.value || ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="executiveTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Title <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter executive title" value={field.value || ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dateCertified"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Date Certified <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <DateTimePicker
                      selected={field.value ?? undefined}
                      onSelect={field.onChange}
                      disabled={{
                        before: new Date(),
                      }}
                      onlyDate
                      placeholder="Select Date Certified"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="digitalSignature"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Digital Signature</FormLabel>
                  <FormControl>
                    <Draw
                      onFinish={(value) => {
                        field.onChange(value);
                      }}
                      onClear={() => {
                        field.onChange('');
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    I certify that I have examined this document and that to the best of my knowledge the information
                    contained therein is true, accurate and complete.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </form>
      </Form>
    </Card>
  );
};
