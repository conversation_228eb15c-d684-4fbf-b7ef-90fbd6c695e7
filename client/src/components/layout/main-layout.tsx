import { ConsentManager } from '@/components/consent/consent-manager';
import { Navbar } from '@/components/layout/navbar';
import { Sidebar } from '@/components/layout/sidebar';
import { useAppContext } from '@/contexts/app-context';
import { useAnalytics } from '@/hooks/use-analytics';
import { useConfig } from '@/hooks/use-config';
import { useIsMobile } from '@/hooks/use-mobile';
import type React from 'react';
import { useEffect, useState } from 'react';

const MainLayout = ({ children }: { children: React.ReactNode }) => {
  const isMobile = useIsMobile();
  const { user } = useAppContext();
  const { VITE_MIXPANEL_TOKEN, isLoading } = useConfig();
  const analytics = useAnalytics();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showConsentManager, setShowConsentManager] = useState(false);

  // Auto-show consent manager for logged in users who haven't set consent
  useEffect(() => {
    if (user && VITE_MIXPANEL_TOKEN && !isLoading) {
      const shouldShow = analytics.shouldShowConsentBanner();
      if (shouldShow) {
        // Add a small delay to ensure the app is fully loaded
        const timer = setTimeout(() => {
          setShowConsentManager(true);
        }, 1000);
        return () => clearTimeout(timer);
      }
    }
  }, [user, VITE_MIXPANEL_TOKEN, isLoading, analytics]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (isMobile && sidebarOpen) {
        const target = e.target as HTMLElement;
        if (!target.closest('.sidebar') && !target.closest('.menu-trigger')) {
          setSidebarOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, sidebarOpen]);

  return (
    <div className="h-screen flex flex-row bg-gray-50">
      <div
        className={`sidebar ${isMobile ? 'fixed z-30 inset-y-0 left-0 transform transition duration-200 ease-in-out' : ''} ${
          isMobile && sidebarOpen ? 'translate-x-0' : isMobile ? '-translate-x-full' : ''
        }`}
      >
        <Sidebar onCloseMobile={() => setSidebarOpen(false)} onOpenConsentManager={() => setShowConsentManager(true)} />
      </div>

      {/* Mobile overlay when sidebar is open */}
      {isMobile && sidebarOpen && (
        <div className="fixed inset-0 bg-black/50 z-20" onClick={() => setSidebarOpen(false)} />
      )}

      <div className="flex-1 flex flex-col overflow-hidden h-full">
        <Navbar onMenuClick={() => setSidebarOpen(!sidebarOpen)} isMobile={isMobile} />
        <div className="relative flex-1 overflow-hidden">
          <main className="absolute inset-0 bg-gray-50 overflow-y-auto">{children}</main>
        </div>
      </div>

      {/* Auto-popup consent manager */}
      <ConsentManager isOpen={showConsentManager} onClose={() => setShowConsentManager(false)} />
    </div>
  );
};

export default MainLayout;
