import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';

export function EditOshaReportLoading() {
  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      <div className="mb-8">
        {/* Back navigation skeleton */}
        <div className="flex items-center mb-6">
          <ArrowLeft className="mr-2 h-4 w-4 text-muted-foreground" />
          <Skeleton className="h-4 w-32" />
        </div>

        {/* Header skeleton */}
        <div className="flex flex-col mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start mb-2">
            <Skeleton className="h-8 w-96 mb-2" />
          </div>
          <Skeleton className="h-4 w-48" />
        </div>
      </div>

      {/* Progress bar skeleton */}
      <div className="sticky top-0 bg-white/95 backdrop-blur-sm z-20 py-3 mb-6 border border-gray-200 rounded-lg">
        <div className="container mx-auto max-w-7xl px-4 sm:px-6">
          <div className="flex items-center justify-between mb-2">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-4 w-8" />
          </div>
          <Progress value={0} className="h-2" />
        </div>
      </div>

      <div className="space-y-8">
        {/* Section 1: Linked Safety Event */}
        <div className="mb-6 border border-gray-200 rounded-lg p-4 bg-white">
          <div className="flex items-center mb-4">
            <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
              1
            </span>
            <Skeleton className="h-6 w-48" />
          </div>
          <Skeleton className="h-4 w-64 mb-4" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-3 w-80" />
          </div>
        </div>

        {/* Section 2: Employee Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                2
              </span>
              Employee Information
            </CardTitle>
            <CardDescription>Identify the employee involved in the incident</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Privacy case toggle skeleton */}
            <div className="flex flex-row items-center justify-between rounded-lg border border-blue-300 bg-blue-50 p-4">
              <div className="flex items-center space-x-2">
                <div className="bg-blue-100 text-blue-800 border-blue-300 px-2 py-1 rounded text-xs">🛡️ PRIVACY</div>
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-4 rounded-full" />
              </div>
              <Skeleton className="h-6 w-12 rounded-full" />
            </div>

            {/* Form fields skeleton */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Section 3: Medical Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                3
              </span>
              Medical Information
            </CardTitle>
            <CardDescription>Details about the injury or illness and incident context</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
            {/* Radio group skeleton */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <div className="flex flex-col space-y-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                ))}
              </div>
            </div>
            {/* Checkbox skeleton */}
            <div className="flex flex-row items-start space-x-3 rounded-md border p-4">
              <Skeleton className="h-4 w-4" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-64" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Section 4: OSHA Questions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                4
              </span>
              OSHA Questions
            </CardTitle>
            <CardDescription>Additional information required for OSHA recordkeeping</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="flex flex-row items-start space-x-3 rounded-md border p-4">
                  <Skeleton className="h-4 w-4" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-3 w-56" />
                  </div>
                </div>
              ))}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Section 5: Witnesses & People */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                5
              </span>
              Witnesses & People
            </CardTitle>
            <CardDescription>People involved in or who witnessed the incident</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-3 w-64" />
              <Skeleton className="h-20 w-full" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>

        {/* Section 6: Corrective Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                6
              </span>
              Corrective Actions
            </CardTitle>
            <CardDescription>Actions taken or planned to prevent recurrence</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-20 w-full" />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Section 7: OSHA Reporting */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                7
              </span>
              OSHA Reporting - Case Type
            </CardTitle>
            <CardDescription>OSHA reporting requirements and classification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-3 w-64" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-3 w-48" />
            </div>
          </CardContent>
        </Card>

        {/* Submit button skeleton */}
        <div className="flex justify-end">
          <Skeleton className="h-10 w-32" />
        </div>

        {/* OSHA requirement notice skeleton */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-6">
          <div className="flex gap-3">
            <div className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5">ℹ️</div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-56" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-80" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
