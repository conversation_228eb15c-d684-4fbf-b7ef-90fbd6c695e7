import { CaseTypeBadge } from '@/components/composite/case-type-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { Archive, Eye } from 'lucide-react';
import { useLocation } from 'wouter';

export const OshaReportsTable = ({ oshaReports }: { oshaReports: RouterOutputs['oshaReport']['list']['result'] }) => {
  const [_, navigate] = useLocation();

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">OSHA Log ID</TableHead>
            <TableHead className="w-[100px]">Employee</TableHead>
            <TableHead className="w-[100px]">Job Title</TableHead>
            <TableHead className="w-[110px]">Case Type</TableHead>
            <TableHead className="w-[110px]">Fatality</TableHead>
            <TableHead className="w-[130px]">Date</TableHead>
            <TableHead className="w-[110px]">OSHA Location</TableHead>
            <TableHead className="w-[110px]">Days Away</TableHead>
            <TableHead className="w-[110px]">Days Restricted</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {oshaReports.map((report) => (
            <TableRow
              key={report.id}
              className={cn(
                'cursor-pointer hover:bg-muted/50',
                report.archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : '',
              )}
              onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id))}
            >
              <TableCell>
                <div className="flex flex-col gap-1">
                  <span className="font-medium">{report.slug}</span>
                  {report.archived && (
                    <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                      <Archive className="h-3 w-3 mr-1" />
                      Archived
                    </Badge>
                  )}
                  <div className="text-sm text-muted-foreground line-clamp-1">{report.eventTitle}</div>
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground truncate">
                  {report.privacyCase ? <span className="text-blue-600">Privacy Case</span> : report.employeeName}
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground truncate">{report.employeeJobTitle}</div>
              </TableCell>
              <TableCell>
                <CaseTypeBadge caseType={report.type} />
              </TableCell>
              <TableCell>
                {report.wasDeceased ? (
                  <Badge className="bg-red-100 text-red-800 border-red-200" variant="outline">
                    Yes
                  </Badge>
                ) : (
                  <Badge className="bg-green-100 text-green-800 border-green-200" variant="outline">
                    No
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-sm text-gray-600">{formatDate(report.createdAt, true)}</TableCell>
              <TableCell className="text-sm text-gray-600">{report.oshaLocation?.name || 'Not specified'}</TableCell>
              <TableCell className="text-sm text-gray-900 font-medium">{report.daysAwayFromWork || 0}</TableCell>
              <TableCell className="text-sm text-gray-900 font-medium">{report.daysRestrictedFromWork}</TableCell>

              <TableCell className="text-right">
                <Button
                  variant="link"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id));
                  }}
                >
                  <Eye className="h-4 w-4 text-blue-600" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
