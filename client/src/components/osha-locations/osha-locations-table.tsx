import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/router.types';
import { Archive, Trash2 } from 'lucide-react';
import { useState } from 'react';

export const OshaLocationsTable = ({
  oshaLocations,
}: {
  oshaLocations: RouterOutputs['oshaLocation']['list']['result'];
}) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedOshaLocation, setSelectedOshaLocation] = useState<
    RouterOutputs['oshaLocation']['list']['result'][number] | null
  >(null);

  return (
    <div className="overflow-hidden hidden md:block">
      {selectedOshaLocation && (
        <ArchiveConfirmationDialog
          archived={!!selectedOshaLocation.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedOshaLocation.id}
          entityType="oshaLocation"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {oshaLocations.map((oshaLocation) => (
            <TableRow
              className={`cursor-pointer ${oshaLocation.archivedAt ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
              key={oshaLocation.id}
            >
              <TableCell className="font-medium">{oshaLocation.name}</TableCell>
              <TableCell>
                <Badge
                  className={`${
                    !oshaLocation.archivedAt
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-gray-50 text-gray-700 border-gray-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {!oshaLocation.archivedAt ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {typeof oshaLocation.createdBy === 'object' && oshaLocation.createdBy?.fullName
                      ? oshaLocation.createdBy.fullName
                      : '--'}
                  </span>
                  <span className="text-sm text-gray-500">
                    {typeof oshaLocation.createdBy === 'object' && oshaLocation.createdBy?.username
                      ? oshaLocation.createdBy.username
                      : '--'}
                  </span>
                </div>
              </TableCell>
              <TableCell>{formatDate(oshaLocation.createdAt, true)}</TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* Archive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={() => {
                          setSelectedOshaLocation(oshaLocation);
                          setShowArchiveConfirm(true);
                        }}
                      >
                        {oshaLocation.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{oshaLocation.archivedAt ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
