import { Button } from '@/components/ui/button';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { MapPin } from 'lucide-react';

export const OshaLocationsEmpty = ({
  hasActiveFilters,
  onResetFilters,
  onCreateOshaLocation,
}: {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
  onCreateOshaLocation: () => void;
}) => {
  const { hasPermission } = usePermissions();

  if (hasActiveFilters) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <MapPin className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No OSHA Locations Found</h3>
        <p className="text-gray-600 mb-4">
          No OSHA locations match your current filters. Try adjusting your search criteria.
        </p>
        <Button onClick={onResetFilters} variant="outline">
          Clear Filters
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <MapPin className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No OSHA Locations Yet</h3>
      <p className="text-gray-600 mb-4">
        Get started by creating your first OSHA location. OSHA locations help organize and standardize location data
        across your organization.
      </p>
      {hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.CREATE) && (
        <Button onClick={onCreateOshaLocation}>+ Create OSHA Location</Button>
      )}
    </div>
  );
};
