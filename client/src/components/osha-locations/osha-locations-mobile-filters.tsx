import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { OshaLocationsFilters } from '@shared/settings.types';
import { Archive, Filter } from 'lucide-react';

export const OshaLocationsMobileFilters = ({
  activeFilterCount,
  filters,
  updateFilter,
  resetFilters,
}: {
  activeFilterCount: number;
  filters: OshaLocationsFilters;
  updateFilter: (key: keyof OshaLocationsFilters, value: OshaLocationsFilters[keyof OshaLocationsFilters]) => void;
  resetFilters: () => void;
}) => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" className="ml-2 md:hidden">
          <Filter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="ml-1 bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5">{activeFilterCount}</span>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-full sm:max-w-md">
        <SheetHeader>
          <SheetTitle>Filters</SheetTitle>
          <SheetDescription>Filter OSHA locations by various criteria</SheetDescription>
        </SheetHeader>

        <div className="p-4 space-y-4">
          {/* Include Archived Filter */}
          <div className="bg-muted rounded-md p-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-archived-mobile"
                checked={filters.includeArchived}
                onCheckedChange={(checked) => {
                  const newValue = !!checked;
                  updateFilter('includeArchived', newValue);
                }}
              />
              <Label htmlFor="include-archived-mobile" className="flex items-center cursor-pointer">
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Label>
            </div>
          </div>

          {/* Created By Filter */}
          <div className="bg-muted rounded-md p-3">
            <h3 className="text-sm font-medium mb-2">Created By</h3>
            <AsyncUsersFilter
              selected={filters.createdBy}
              onSelect={(createdBy) => {
                updateFilter('createdBy', createdBy);
              }}
              label=""
              placeholder="Search created by"
            />
          </div>

          {/* Created Date Range Filter */}
          <div className="bg-muted rounded-md p-3">
            <h3 className="text-sm font-medium mb-2">Created Date Range</h3>
            <DateRangePicker
              range={{
                from: filters.createdDateRange?.from,
                to: filters.createdDateRange?.to,
              }}
              setRange={(dateRange) => {
                updateFilter('createdDateRange', dateRange);
              }}
              placeholder="Select date range"
            />
          </div>
        </div>

        <SheetFooter className="px-4 pb-4">
          <div className="flex space-x-2 w-full">
            <Button variant="outline" onClick={resetFilters} className="flex-1">
              Clear All
            </Button>
            <SheetClose asChild>
              <Button className="flex-1">Apply Filters</Button>
            </SheetClose>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
