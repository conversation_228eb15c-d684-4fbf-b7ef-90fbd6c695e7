import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { StatusIndicator } from '@/components/composite/status-indicator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { Archive, Trash2, User } from 'lucide-react';
import { useState } from 'react';

type OshaLocation = RouterOutputs['oshaLocation']['list']['result'][number];

export const OshaLocationsMobileView = ({ oshaLocations }: { oshaLocations: OshaLocation[] }) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedOshaLocation, setSelectedOshaLocation] = useState<
    RouterOutputs['oshaLocation']['list']['result'][number] | null
  >(null);

  const handleArchiveClick = (oshaLocation: OshaLocation) => {
    setSelectedOshaLocation(oshaLocation);
    setShowArchiveConfirm(true);
  };

  return (
    <>
      {selectedOshaLocation && (
        <ArchiveConfirmationDialog
          archived={!!selectedOshaLocation.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedOshaLocation.id}
          entityType="oshaLocation"
        />
      )}

      <div className="md:hidden space-y-4">
        {oshaLocations.map((oshaLocation) => (
          <Card
            key={oshaLocation.id}
            className={`group relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200 ${
              oshaLocation.archivedAt
                ? 'bg-gradient-to-r from-amber-50/80 to-amber-50/60'
                : 'bg-white hover:bg-gray-50/50'
            }`}
          >
            <CardContent>
              <div className="flex">
                {/* Status indicator line - left side */}
                <StatusIndicator archived={!!oshaLocation.archivedAt} />

                {/* Content Section */}
                <div className="flex-1">
                  {/* Header Section */}
                  <div className="px-4">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-muted-foreground">{oshaLocation.name}</span>
                          {oshaLocation.archivedAt && (
                            <Badge
                              className="bg-amber-100 text-amber-700 border-amber-200 text-xs font-medium"
                              variant="outline"
                            >
                              <Archive className="h-3 w-3 mr-1" />
                              Archived
                            </Badge>
                          )}
                        </div>

                        <h3 className="font-semibold text-gray-900 text-base leading-tight line-clamp-2 mb-2">
                          OSHA Location
                        </h3>
                      </div>

                      <div className="flex flex-col items-end gap-2">
                        <Badge
                          className={`${
                            !oshaLocation.archivedAt
                              ? 'bg-green-100 text-green-700 border-green-200'
                              : 'bg-gray-100 text-gray-700 border-gray-200'
                          } text-xs font-medium`}
                          variant="outline"
                        >
                          {!oshaLocation.archivedAt ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="px-4 pb-3">
                    <div className="space-y-2.5">
                      {/* Creator */}
                      <div className="flex items-center text-sm text-gray-600">
                        <User className="h-3.5 w-3.5 mr-2 text-gray-400" />
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {typeof oshaLocation.createdBy === 'object' && oshaLocation.createdBy?.fullName
                              ? oshaLocation.createdBy.fullName
                              : '--'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {typeof oshaLocation.createdBy === 'object' && oshaLocation.createdBy?.username
                              ? oshaLocation.createdBy.username
                              : '--'}
                          </span>
                        </div>
                      </div>

                      {/* Date */}
                      <div className="flex items-center text-xs text-gray-500">
                        <span>{format(new Date(oshaLocation.createdAt), 'MMM d, yyyy')}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions Section */}
                  <div className="px-4">
                    <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                      <div className="flex items-center gap-1.5">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                          onClick={() => {
                            // Navigate to osha location details or edit
                            console.log('Navigate to osha location:', oshaLocation.id);
                          }}
                        >
                          View
                        </Button>
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                        onClick={() => handleArchiveClick(oshaLocation)}
                      >
                        {oshaLocation.archivedAt ? (
                          <Archive className="h-3.5 w-3.5 mr-1.5" />
                        ) : (
                          <Trash2 className="h-3.5 w-3.5 mr-1.5" />
                        )}
                        {oshaLocation.archivedAt ? 'Unarchive' : 'Archive'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};
