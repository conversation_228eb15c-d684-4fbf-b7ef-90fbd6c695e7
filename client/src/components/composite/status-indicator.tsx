import { cn } from '@/lib/utils';
import { statusEnum } from '@shared/schema';

type StatusIndicatorProps = {
  status?: (typeof statusEnum.enumValues)[number] | 'active' | 'inactive';
  archived?: boolean | null;
};

export const StatusIndicator = ({ status, archived }: StatusIndicatorProps) => {
  const statusColorMap = {
    [statusEnum.enumValues[0]]: 'bg-blue-700',
    [statusEnum.enumValues[1]]: 'bg-yellow-700',
    [statusEnum.enumValues[2]]: 'bg-green-700',
    active: 'bg-green-700',
    inactive: 'bg-gray-700',
  };

  const getStatusColor = () => {
    if (archived) {
      return 'bg-amber-400';
    }

    if (status) {
      return statusColorMap[status] || 'bg-gray-700';
    }

    return 'bg-blue-700';
  };

  return <div className={cn('w-1 flex-shrink-0 rounded-full transition-colors duration-200', getStatusColor())} />;
};
