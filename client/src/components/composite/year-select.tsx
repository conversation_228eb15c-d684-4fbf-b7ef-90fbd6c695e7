import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { generateYears } from '@/lib/generate-years';

export const YearSelect = ({
  value,
  onChange,
  className,
}: {
  value?: number;
  onChange: (value: number) => void;
  className?: string;
}) => {
  return (
    <Select value={value?.toString()} onValueChange={(value) => onChange(parseInt(value))}>
      <SelectTrigger className={className}>
        <SelectValue placeholder="Select a year" />
      </SelectTrigger>
      <SelectContent>
        {generateYears().map((year) => (
          <SelectItem key={year} value={year.toString()}>
            {year}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
