import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { oshaAuditTrailActionEnum } from '@shared/schema';
import { Archive, Calendar, CheckCircle, Download, Edit3, FilePlus, History, PenTool, User } from 'lucide-react';
import { useState } from 'react';

const ActionIconMap = {
  [oshaAuditTrailActionEnum.enumValues[0]]: FilePlus, // created
  [oshaAuditTrailActionEnum.enumValues[1]]: Edit3, // updated
  [oshaAuditTrailActionEnum.enumValues[2]]: CheckCircle, // submitted
  [oshaAuditTrailActionEnum.enumValues[3]]: Download, // downloaded
  [oshaAuditTrailActionEnum.enumValues[4]]: PenTool, // signed
  [oshaAuditTrailActionEnum.enumValues[5]]: Archive, // archived
  [oshaAuditTrailActionEnum.enumValues[6]]: Archive, // restored
} as const;

const AuditTrailTimeline = {
  [oshaAuditTrailActionEnum.enumValues[0]]: 'Created', // created
  [oshaAuditTrailActionEnum.enumValues[1]]: 'Updated', // updated
  [oshaAuditTrailActionEnum.enumValues[2]]: 'Submitted', // submitted
  [oshaAuditTrailActionEnum.enumValues[3]]: 'Downloaded', // downloaded
  [oshaAuditTrailActionEnum.enumValues[4]]: 'Signed', // signed
  [oshaAuditTrailActionEnum.enumValues[5]]: 'Archived', // archived
  [oshaAuditTrailActionEnum.enumValues[6]]: 'Restored', // restored
} as const;

export const OshaAuditTrail = ({ oshaReportId }: { oshaReportId: string }) => {
  const { data: auditTrail } = trpc.oshaAuditTrail.get.useQuery({
    id: oshaReportId,
  });

  const [auditTrailExpanded, setAuditTrailExpanded] = useState(false);

  return (
    <>
      <Card>
        <Collapsible open={auditTrailExpanded} onOpenChange={setAuditTrailExpanded} className="border-none shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <History className="h-5 w-5 text-gray-600 mr-2" />
                Audit Trail
              </CardTitle>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                  <span className="sr-only">Toggle</span>
                  {auditTrailExpanded ? <span className="text-lg">−</span> : <span className="text-lg">+</span>}
                </Button>
              </CollapsibleTrigger>
            </div>
          </CardHeader>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {auditTrail?.map((entry, index) => {
                  const ActionIcon = ActionIconMap[entry.action as keyof typeof ActionIconMap];

                  return (
                    <div key={index} className="mb-2 relative">
                      <div className="flex items-start">
                        <div className="flex flex-col items-center mr-4 mt-1">
                          <div className="rounded-full w-8 h-8 bg-primary flex items-center justify-center text-primary-foreground">
                            {ActionIcon && <ActionIcon className="h-4 w-4" />}
                          </div>
                        </div>
                        <div>
                          <p className="font-medium">
                            {AuditTrailTimeline[entry.action as keyof typeof AuditTrailTimeline]}
                          </p>
                          <div className="flex items-center text-sm text-muted-foreground gap-2">
                            <Calendar className="h-3 w-3" />
                            {formatDate(entry.createdAt)}
                          </div>
                          {entry.createdBy?.fullName && (
                            <div className="flex items-center mt-1 text-sm text-muted-foreground">
                              <User className="h-3 w-3 mr-1" />
                              {entry.createdBy.fullName}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </>
  );
};
