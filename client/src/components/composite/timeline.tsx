import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { trpc } from '@/providers/trpc';
import { auditTrailActionEnum, entityTypeEnum } from '@shared/schema';
import { format } from 'date-fns';
import {
  Archive,
  ArchiveRestore,
  Calendar,
  CheckCircle2,
  Clock,
  Edit3,
  FilePlus,
  MessageCircle,
  RefreshCw,
  Trash2,
  Upload,
  User,
} from 'lucide-react';
import { useMemo } from 'react';

const ActionIconMap = {
  [auditTrailActionEnum.enumValues[0]]: FilePlus,
  [auditTrailActionEnum.enumValues[1]]: Edit3,
  [auditTrailActionEnum.enumValues[2]]: Trash2,
  [auditTrailActionEnum.enumValues[3]]: MessageCircle,
  [auditTrailActionEnum.enumValues[4]]: Upload,
  [auditTrailActionEnum.enumValues[5]]: CheckCircle2,
  [auditTrailActionEnum.enumValues[6]]: Clock,
  [auditTrailActionEnum.enumValues[7]]: RefreshCw,
  [auditTrailActionEnum.enumValues[8]]: Archive,
  [auditTrailActionEnum.enumValues[9]]: ArchiveRestore,
};

const StatusTimeline = {
  [auditTrailActionEnum.enumValues[0]]: 'Opened',
  [auditTrailActionEnum.enumValues[6]]: 'In Review',
  [auditTrailActionEnum.enumValues[5]]: 'Closed',
  [auditTrailActionEnum.enumValues[7]]: 'Reopened',
  [auditTrailActionEnum.enumValues[8]]: 'Archived',
  [auditTrailActionEnum.enumValues[9]]: 'Unarchived',
} as const;

const AuditTrailTimeline = {
  [auditTrailActionEnum.enumValues[0]]: 'Created',
  [auditTrailActionEnum.enumValues[1]]: 'Updated',
  [auditTrailActionEnum.enumValues[2]]: 'Deleted',
  [auditTrailActionEnum.enumValues[3]]: 'Commented',
} as const;

export const Timeline = ({
  entityId,
  entityType,
}: {
  entityId: string;
  entityType: (typeof entityTypeEnum.enumValues)[number];
}) => {
  const { data: trail } = trpc.auditTrail.get.useQuery({
    entityId: entityId,
    entityType: entityType,
  });

  const statusTimeline = useMemo(
    () => trail?.filter((event) => StatusTimeline[event.action as keyof typeof StatusTimeline]),
    [trail],
  );

  const auditTrailTimeline = useMemo(
    () => trail?.filter((event) => AuditTrailTimeline[event.action as keyof typeof AuditTrailTimeline]),
    [trail],
  );

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Status Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            {statusTimeline?.map((event, i) => {
              const ActionIcon = ActionIconMap[event.action];
              return (
                <div key={i} className="mb-2 relative">
                  <div className="flex items-start">
                    <div className="flex flex-col items-center mr-4 mt-1">
                      <div className="rounded-full w-8 h-8 bg-primary flex items-center justify-center text-primary-foreground">
                        {ActionIcon && <ActionIcon className="h-4 w-4" />}
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">{StatusTimeline[event.action as keyof typeof StatusTimeline]}</p>
                      <div className="flex items-center text-sm text-muted-foreground gap-2">
                        <Calendar className="h-3 w-3" />
                        {format(event.timestamp, 'MMM d, yyyy h:mm a')}
                      </div>
                      {event.user?.fullName && (
                        <div className="flex items-center mt-1 text-sm text-muted-foreground">
                          <User className="h-3 w-3 mr-1" />
                          {event.user?.fullName}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Audit Trail (Collapsible) */}
      <Card>
        <CardHeader className="pb-0">
          <CardTitle className="text-lg">Audit Trail</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible>
            <AccordionItem value="audit-trail">
              <AccordionTrigger className="py-3">View Internal Log</AccordionTrigger>
              <AccordionContent>
                {auditTrailTimeline?.map((event, i) => {
                  const ActionIcon = ActionIconMap[event.action];
                  return (
                    <div key={i} className="mb-2 relative">
                      <div className="flex items-start">
                        <div className="flex flex-col items-center mr-4 mt-1">
                          <div className="rounded-full w-8 h-8 bg-primary flex items-center justify-center text-primary-foreground">
                            {ActionIcon && <ActionIcon className="h-4 w-4" />}
                          </div>
                        </div>
                        <div>
                          <p className="font-medium">
                            {AuditTrailTimeline[event.action as keyof typeof AuditTrailTimeline]}
                          </p>
                          <div className="flex items-center text-sm text-muted-foreground gap-2">
                            <Calendar className="h-3 w-3" />
                            {format(event.timestamp, 'MMM d, yyyy h:mm a')}
                          </div>
                          {event.user?.fullName && (
                            <div className="flex items-center mt-1 text-sm text-muted-foreground">
                              <User className="h-3 w-3 mr-1" />
                              {event.user?.fullName}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </>
  );
};

//                 <div className="space-y-3 text-sm">
//                   <div className="border-b pb-2">
//                     <div className="flex justify-between mb-1">
//                       <span className="font-medium">Status changed</span>
//                       <span className="text-muted-foreground">{format(new Date(), 'MMM d, h:mm a')}</span>
//                     </div>
//                     <div className="flex items-center gap-2">
//                       <span className="text-muted-foreground">Open</span>
//                       <svg
//                         xmlns="http://www.w3.org/2000/svg"
//                         width="24"
//                         height="24"
//                         viewBox="0 0 24 24"
//                         fill="none"
//                         stroke="currentColor"
//                         strokeWidth="2"
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         className="h-3 w-3 text-muted-foreground"
//                       >
//                         <polyline points="9 18 15 12 9 6" />
//                       </svg>
//                       <span>In Review</span>
//                     </div>
//                     <div className="text-muted-foreground text-xs mt-1">By: Sarah Smith</div>
//                   </div>
//                   <div className="border-b pb-2">
//                     <div className="flex justify-between mb-1">
//                       <span className="font-medium">Report created</span>
//                       <span className="text-muted-foreground">{format(new Date(), 'MMM d, h:mm a')}</span>
//                     </div>
//                     <div className="text-muted-foreground text-xs">By: John Doe</div>
//                   </div>
//                 </div>
//               </AccordionContent>
//             </AccordionItem>
//           </Accordion>
//         </CardContent>
//       </Card>
//     </>
//   );
// };
