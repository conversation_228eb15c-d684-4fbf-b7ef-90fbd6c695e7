import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { TransientFileSchema } from '@shared/schema.types';
import {
  ACCEPTED_FORMATS,
  DOCUMENT_TYPE_BY_MIME_TYPE,
  FILE_TYPE_LABEL_BY_MIME_TYPE,
  SUPPORTED_MIME_TYPES,
} from '@shared/files.types';
import { Camera, File, FileImage, FileSpreadsheet, FileText, X, Loader2, Play, Sheet, Upload } from 'lucide-react';
import { MediaViewerModal } from '@/components/ui/media-viewer-modal';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { z } from 'zod';

type TransientFile = z.infer<typeof TransientFileSchema>;

interface MediaUploadProps {
  maxFiles?: number;
  maxSize?: number; // in MB
  className?: string;
  disabled?: boolean;
  files: TransientFile[];
  setFiles: (tFiles: TransientFile[]) => void;
  onFileRemove?: (file: TransientFile) => Promise<void>;
}

export function MediaUpload({
  maxFiles = 3,
  maxSize = 20,
  className,
  disabled = false,
  files,
  setFiles,
  onFileRemove,
}: MediaUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<TransientFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [remainingCount, setRemainingCount] = useState(maxFiles);
  const [isRemovingFile, setIsRemovingFile] = useState(false);
  const [isRemovingUploadedFile, setIsRemovingUploadedFile] = useState(false);

  // Media viewer modal state
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [viewerIndex, setViewerIndex] = useState(0);

  const inputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Always update uploadedFiles to match the files prop, even if it's an empty array
    setUploadedFiles(files || []);
  }, [files]);

  // Update remaining count
  useEffect(() => {
    setRemainingCount(maxFiles - uploadedFiles.length);
  }, [uploadedFiles, maxFiles]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  };

  // Process selected files
  const handleFiles = (fileList: FileList | null) => {
    if (!fileList) return;

    const newFiles: TransientFile[] = [];
    const updatedFiles = [...uploadedFiles];

    // Return if adding would exceed max files
    if (fileList.length + uploadedFiles.length > maxFiles) {
      alert(`You can only upload a maximum of ${maxFiles} files.`);
      return;
    }

    const files = Array.from(fileList);

    for (const file of files) {
      // Check file type against supported MIME types
      if (!SUPPORTED_MIME_TYPES[file.type as keyof typeof SUPPORTED_MIME_TYPES]) {
        alert(`File ${file.name} is not a supported format.`);
        return;
      }

      // Check file size (convert MB to bytes)
      if (file.size > maxSize * 1024 * 1024) {
        alert(`File ${file.name} exceeds the maximum size of ${maxSize}MB.`);
        return;
      }

      // Store the original File object with preview metadata
      newFiles.push({
        name: file.name,
        url: URL.createObjectURL(file),
        type: file.type,
        size: file.size,
        file,
      });
    }

    // Update state with new files
    const combinedFiles = [...updatedFiles, ...newFiles];
    setUploadedFiles(combinedFiles);

    // Pass the combined TransientFiles and only the new File objects to the parent
    // The parent needs to track which files are new for upload purposes
    setFiles(combinedFiles);

    // Reset input values so same file can be uploaded again if needed
    if (inputRef.current) {
      inputRef.current.value = '';
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = '';
    }
  };

  // Remove a file
  const handleFileRemove = async (name: string) => {
    setIsRemovingFile(true);
    const fileToRemove = uploadedFiles.find((item) => item.name === name);

    // If the file has an id, it's an existing file that needs to be removed from the server
    if (fileToRemove?.id && onFileRemove) {
      setIsRemovingUploadedFile(true);
      try {
        await onFileRemove(fileToRemove);
        // The parent component will handle updating the form state
        // Don't update local state here - let the parent component handle it via the files prop
        // return;
      } catch (error) {
        console.error('Error removing file from server:', error);
        // return; // Don't remove from local state if server removal failed
      }
    } else {
      // For new files (without id), just remove from local state
      const updatedFiles = uploadedFiles.filter((item) => item.name !== name);
      setUploadedFiles(updatedFiles);
      setFiles?.(updatedFiles);
    }
    setIsRemovingFile(false);
    setIsRemovingUploadedFile(false);
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  // Handle camera click
  const handleCameraClick = () => {
    cameraInputRef.current?.click();
  };

  // Handle gallery click
  const handleGalleryClick = () => {
    inputRef.current?.click();
  };

  // Handle thumbnail click to open viewer
  const handleThumbnailClick = (index: number) => {
    setViewerIndex(index);
    setIsViewerOpen(true);
  };

  // Get appropriate icon for document type
  const getFileIconFromFile = (file: TransientFile) => {
    const mimeType = file.type;
    const fileType = DOCUMENT_TYPE_BY_MIME_TYPE[mimeType as keyof typeof DOCUMENT_TYPE_BY_MIME_TYPE];

    const ICONS_BY_FILE_TYPE = {
      pdf: <FileText className="h-7 w-7 sm:h-8 sm:w-8 text-red-500" />,
      spreadsheet: <FileSpreadsheet className="h-7 w-7 sm:h-8 sm:w-8 text-green-600" />,
      word: <FileText className="h-7 w-7 sm:h-8 sm:w-8 text-blue-600" />,
      presentation: <Sheet className="h-7 w-7 sm:h-8 sm:w-8 text-orange-500" />,
      text: <FileText className="h-7 w-7 sm:h-8 sm:w-8 text-gray-600" />,
    } as const;

    const IconComponent = ICONS_BY_FILE_TYPE[fileType as keyof typeof ICONS_BY_FILE_TYPE];
    return IconComponent ?? <File className="h-7 w-7 sm:h-8 sm:w-8 text-gray-500" />;
  };

  // Get file type for display
  const getFileTypeLabel = (mimeType: string) => {
    return FILE_TYPE_LABEL_BY_MIME_TYPE[mimeType as keyof typeof FILE_TYPE_LABEL_BY_MIME_TYPE] ?? 'Document';
  };

  return (
    <>
      {/* Camera input (hidden) */}
      <input
        ref={cameraInputRef}
        type="file"
        onChange={(e) => handleFileChange(e)}
        accept="image/*,video/*"
        capture="environment" // The outward-facing camera
        className="hidden"
        aria-hidden="true"
      />

      {/* Gallery input (hidden) */}
      <input
        ref={inputRef}
        type="file"
        onChange={(e) => handleFileChange(e)}
        multiple
        accept={Object.keys(SUPPORTED_MIME_TYPES).join(',')}
        className="hidden"
        aria-hidden="true"
      />

      {/* Main upload container */}
      <div
        ref={containerRef}
        className={cn(
          'relative rounded-[8px] border border-dashed border-border transition-colors',
          'p-4 sm:p-4', // Default padding
          'bg-[#F9FAFB] sm:bg-background', // Subtle background on mobile
          'min-h-[44px]', // Minimum touch target size
          dragActive ? 'bg-primary/5 border-primary/30' : '',
          className,
        )}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        {/* Header with title and description */}
        <div className="flex flex-col items-center sm:items-start mb-4">
          {/* Title with icon */}
          <div className="flex items-center gap-2 mb-2 w-full justify-center sm:justify-start">
            <div className="shrink-0 flex items-center justify-center h-6 w-6 rounded-full bg-primary/10">
              <Upload className="h-3 w-3 text-primary" />
            </div>
            <div className="text-[14px] font-medium">Add a photo, video, or document (optional)</div>
          </div>

          {/* Description text - centered on mobile, left-aligned on desktop */}
          <div className="w-full">
            <div className="text-[12px] text-muted-foreground text-center sm:text-left">
              Upload supporting media or documentation to help clarify what happened.
            </div>

            <div className="text-[11px] text-muted-foreground mt-1 text-center sm:text-left">
              Formats: {ACCEPTED_FORMATS.join(',')} — max {maxSize}MB each ({maxFiles} max)
            </div>
          </div>
        </div>

        {/* CTA buttons - different for mobile and desktop */}
        {/* Mobile buttons (camera, gallery, and documents) */}
        <div className="flex flex-col gap-2 justify-center mt-3 sm:hidden">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleCameraClick}
            className="text-xs h-10 flex items-center justify-center gap-2"
            disabled={disabled || uploadedFiles.length >= maxFiles}
          >
            <Camera className="h-4 w-4" />
            Take Photo or Video
          </Button>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleGalleryClick}
            className="text-xs h-10 flex items-center justify-center gap-2"
            disabled={disabled || uploadedFiles.length >= maxFiles}
          >
            <FileImage className="h-4 w-4" />
            Choose Files
          </Button>
        </div>

        {/* Desktop button (browse files) */}
        <div className="hidden sm:flex justify-start mt-3">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleGalleryClick}
            className="text-xs h-9 px-3"
            disabled={disabled || uploadedFiles.length >= maxFiles}
            aria-label="Browse files to upload"
          >
            <Upload className="h-4 w-4 mr-2" />
            Browse Files
          </Button>
        </div>

        {/* File counter when files are uploaded */}
        {uploadedFiles.length > 0 && (
          <div className="mt-3 flex justify-center sm:justify-start">
            <div className="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary">
              {remainingCount} {remainingCount <= 1 ? 'file' : 'files'} remaining
            </div>
          </div>
        )}

        {/* Preview area for uploaded files */}
        {uploadedFiles.length > 0 && (
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
            {uploadedFiles.map((file, index) => (
              <div
                key={file.name}
                className="relative rounded-md border bg-white sm:bg-muted/20 p-2 group shadow-xs"
                aria-label={`Preview of uploaded file: ${file.name}`}
              >
                <div
                  className="aspect-video relative flex items-center justify-center overflow-hidden rounded cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => handleThumbnailClick(index)}
                >
                  {file.type?.startsWith('image/') ? (
                    <img src={file.url} alt={`Preview of ${file.name}`} className="h-full w-full object-cover" />
                  ) : file.type?.startsWith('video/') ? (
                    <div className="relative h-full w-full">
                      <video src={file.url} className="h-full w-full object-cover" preload="metadata" muted>
                        Your browser does not support the video tag.
                      </video>
                      {/* Play icon overlay */}
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                        <div className="flex items-center justify-center w-10 h-10 bg-white/90 rounded-full shadow-lg">
                          <Play className="h-5 w-5 text-gray-700 ml-0.5" fill="currentColor" />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full w-full bg-muted">
                      {getFileIconFromFile(file)}
                      <span className="text-xs text-muted-foreground truncate max-w-[90%] mt-1 text-center px-1">
                        {file.name}
                      </span>
                    </div>
                  )}
                </div>

                {/* Remove button */}
                <button
                  type="button"
                  onClick={() => handleFileRemove(file.name)}
                  className="absolute cursor-pointer -top-1.5 -right-1.5 h-5 w-5 rounded-full bg-destructive text-destructive-foreground flex items-center justify-center"
                  aria-label={`Remove file ${file.name}`}
                >
                  {isRemovingFile && isRemovingUploadedFile && file.id ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <X className="h-3 w-3" />
                  )}
                </button>

                {/* File caption */}
                <div className="mt-2 text-xs text-center text-muted-foreground">
                  {getFileTypeLabel(file.type)} {index + 1} of {uploadedFiles.length}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Media Viewer Modal */}
      <MediaViewerModal
        files={uploadedFiles}
        initialIndex={viewerIndex}
        isOpen={isViewerOpen}
        onClose={() => setIsViewerOpen(false)}
      />
    </>
  );
}
