import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { hazardCategoryEnum } from '@shared/schema';

export const CategoryBadge = ({ category }: { category?: (typeof hazardCategoryEnum.enumValues)[number] | null }) => {
  if (!category) {
    return null;
  }

  const categoryMap = {
    [hazardCategoryEnum.enumValues[0]]: 'Chemical',
    [hazardCategoryEnum.enumValues[1]]: 'Electrical',
    [hazardCategoryEnum.enumValues[2]]: 'Physical',
    [hazardCategoryEnum.enumValues[3]]: 'Environmental',
    [hazardCategoryEnum.enumValues[4]]: 'Ergonomic',
    [hazardCategoryEnum.enumValues[5]]: 'Fall',
    [hazardCategoryEnum.enumValues[6]]: 'Biological',
    [hazardCategoryEnum.enumValues[7]]: 'Fire',
    [hazardCategoryEnum.enumValues[8]]: 'Mechanical',
    [hazardCategoryEnum.enumValues[9]]: 'Radiation',
    [hazardCategoryEnum.enumValues[10]]: 'Noise',
    [hazardCategoryEnum.enumValues[11]]: 'Thermal',
    [hazardCategoryEnum.enumValues[12]]: 'Atmospheric',
    [hazardCategoryEnum.enumValues[13]]: 'Spill',
    [hazardCategoryEnum.enumValues[14]]: 'Transportation',
    [hazardCategoryEnum.enumValues[15]]: 'Violence',
    [hazardCategoryEnum.enumValues[16]]: 'Other',
  };

  const categoryColorMap = {
    [hazardCategoryEnum.enumValues[0]]: 'bg-green-50 text-green-700 border-green-200',
    [hazardCategoryEnum.enumValues[1]]: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    [hazardCategoryEnum.enumValues[2]]: 'bg-orange-50 text-orange-700 border-orange-200',
    [hazardCategoryEnum.enumValues[3]]: 'bg-emerald-50 text-emerald-700 border-emerald-200',
    [hazardCategoryEnum.enumValues[4]]: 'bg-blue-50 text-blue-700 border-blue-200',
    [hazardCategoryEnum.enumValues[5]]: 'bg-purple-50 text-purple-700 border-purple-200',
    [hazardCategoryEnum.enumValues[6]]: 'bg-pink-50 text-pink-700 border-pink-200',
    [hazardCategoryEnum.enumValues[7]]: 'bg-red-50 text-red-700 border-red-200',
    [hazardCategoryEnum.enumValues[8]]: 'bg-teal-50 text-teal-700 border-teal-200',
    [hazardCategoryEnum.enumValues[9]]: 'bg-indigo-50 text-indigo-700 border-indigo-200',
    [hazardCategoryEnum.enumValues[10]]: 'bg-amber-50 text-amber-700 border-amber-200',
    [hazardCategoryEnum.enumValues[11]]: 'bg-rose-50 text-rose-700 border-rose-200',
    [hazardCategoryEnum.enumValues[12]]: 'bg-sky-50 text-sky-700 border-sky-200',
    [hazardCategoryEnum.enumValues[13]]: 'bg-cyan-50 text-cyan-700 border-cyan-200',
    [hazardCategoryEnum.enumValues[14]]: 'bg-violet-50 text-violet-700 border-violet-200',
    [hazardCategoryEnum.enumValues[15]]: 'bg-slate-50 text-slate-700 border-slate-200',
    [hazardCategoryEnum.enumValues[16]]: 'bg-gray-50 text-gray-700 border-gray-200',
  };

  return (
    <Badge className={cn(categoryColorMap[category])} variant="outline">
      {categoryMap[category]}
    </Badge>
  );
};
