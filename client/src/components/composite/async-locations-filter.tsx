import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteLocationsPublic } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { useDebounce } from '@uidotdev/usehooks';
import { ChevronDown, Loader2, Search } from 'lucide-react';
import * as React from 'react';

export const AsyncLocationsFilter = ({
  selected,
  onSelect,
  className,
  label,
  placeholder,
}: {
  selected?: string[];
  onSelect: (locationIds: string[]) => void;
  label?: string;
  placeholder?: string;
  className?: string;
}) => {
  const { user } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [search, setSearch] = React.useState('');

  const debouncedSearch = useDebounce(search, 300);

  const {
    data: locations,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteLocationsPublic({
    upkeepCompanyId: user?.upkeepCompanyId!,
    search: debouncedSearch,
    enabled: open && !!user?.upkeepCompanyId,
  });

  // Handle infinite scrolling
  React.useEffect(() => {
    if (!open) return;

    const scrollArea = scrollAreaRef.current;
    if (!scrollArea || !hasNextPage) return;

    let scrollTimeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      clearTimeout(scrollTimeoutId);
      scrollTimeoutId = setTimeout(() => {
        const { scrollTop, scrollHeight, clientHeight } = scrollArea;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceFromBottom < 50 && !isFetchingNextPage) {
          fetchNextPage();
        }
      }, 100);
    };

    scrollArea.addEventListener('scroll', handleScroll);

    return () => {
      scrollArea.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  // Focus input when dropdown opens
  React.useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Handle keyboard events
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={cn('justify-between', className)}>
          <div className="flex items-center">
            {label && <span className="text-sm font-medium mr-2">{label}</span>}
            <Badge className="px-1 py-0 h-5" variant="secondary">
              {selected?.length || 'All'}
            </Badge>
          </div>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="p-0 min-w-16">
        {/* Search Input */}
        <div className="flex items-center pl-4">
          <Search size={15} />
          <Input
            ref={inputRef}
            className="w-full border-none px-2 py-1 bg-transparent shadow-none outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-0 focus-visible:ring-offset-0"
            value={search}
            placeholder={placeholder || 'Search locations...'}
            onChange={handleSearchChange}
          />
        </div>
        <Separator />

        {/* Scrollable Location List */}
        <div ref={scrollAreaRef} className="max-h-64 overflow-auto p-1">
          {isLoading && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="animate-spin size-4" />
              <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
            </div>
          )}

          {locations.length === 0 && !isLoading && (
            <div className="p-2 text-sm text-muted-foreground text-center">No locations found</div>
          )}

          {locations.map((location) => (
            <DropdownMenuCheckboxItem
              key={location.id}
              className="flex items-center space-x-2"
              onSelect={(e) => {
                e.preventDefault();
                if (!selected) {
                  onSelect([location.id]);
                  return;
                }

                const newSelected = selected.includes(location.id);
                if (newSelected) {
                  onSelect(selected.filter((id) => id !== location.id));
                } else {
                  onSelect([...selected, location.id]);
                }
              }}
              checked={selected?.includes(location.id)}
            >
              <label className="capitalize cursor-pointer">{location.name}</label>
            </DropdownMenuCheckboxItem>
          ))}

          {isFetchingNextPage && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="animate-spin size-4" />
              <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
