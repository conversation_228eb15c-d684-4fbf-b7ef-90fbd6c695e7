import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { toast } from 'sonner';

export const ArchiveConfirmationDialog = ({
  archived,
  showDialog,
  setShowDialog,
  entityId,
  entityType,
}: {
  archived: boolean;
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  entityId: string;
  entityType: 'event' | 'capa' | 'accessPoint' | 'oshaReport' | 'oshaSummary' | 'oshaLocation' | 'oshaAgencyReport';
}) => {
  const utils = trpc.useUtils();

  const configMap = {
    event: {
      label: 'Safety Event',
      payload: {
        archived: !archived,
      },
      message: {
        archive:
          'This will remove the event from the active list while preserving its current status. Archived events can be viewed by selecting the "Include Archived" filter in the event log.',
        restore:
          'This will restore the event to the active list with its previous status. Unarchived events will appear in the default event log view.',
      },
    },
    capa: {
      label: 'CAPA',
      payload: {
        archived: !archived,
      },
      message: {
        archive:
          'This will remove the CAPA from the active list while preserving its current status. Archived CAPAs can be viewed by selecting the "Include Archived" filter in the CAPA log.',
        restore:
          'This will restore the CAPA to the active list with its previous status. Unarchived CAPAs will appear in the default CAPA log view.',
      },
    },
    accessPoint: {
      label: 'Access Point',
      payload: {},
      message: {
        archive:
          'This will remove the access point from the active list while preserving its current status. Archived access points can be viewed by selecting the "Include Archived" filter in the access point log.',
        restore:
          'This will restore the access point to the active list with its previous status. Unarchived access points will appear in the default access point log view.',
      },
    },
    oshaReport: {
      label: 'OSHA Report',
      payload: {
        archived: !archived,
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the OSHA report from the active list while preserving its current status. Archived OSHA reports can be viewed by selecting the "Include Archived" filter in the OSHA report log.',
        restore:
          'This will restore the OSHA report to the active list with its previous status. Unarchived OSHA reports will appear in the default OSHA report log view.',
      },
    },
    oshaAgencyReport: {
      label: 'OSHA Agency Report',
      payload: {
        archived: !archived,
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the agency report from the active list while preserving its current status. Archived agency reports can be viewed by selecting the "Include Archived" filter in the agency report log.',
        restore:
          'This will restore the agency report to the active list with its previous status. Unarchived agency reports will appear in the default agency report log view.',
      },
    },
    oshaSummary: {
      label: 'OSHA Summary',
      payload: {
        archived: !archived,
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will lock all logs for the selected year, making them read-only. You can still view and export data. You can restore the year if needed.',
        restore:
          'This will unlock all logs for the selected year, making them editable again. You can still view and export data.',
      },
    },
    oshaLocation: {
      label: 'OSHA Location',
      payload: {},
      message: {
        archive: 'This will archive the OSHA location. It can be restored later if needed.',
        restore: 'This will restore the OSHA location to the active list.',
      },
    },
  };

  const analytics = useAnalytics();
  const trackArchive = (action: boolean) => {
    const eventKey =
      entityType === 'capa'
        ? ANALYTICS_EVENTS.CAPA.ARCHIVED
        : entityType === 'event'
          ? ANALYTICS_EVENTS.EVENT.ARCHIVED
          : ANALYTICS_EVENTS.ACCESS_POINT.ARCHIVED;
    analytics.track(eventKey, {
      [`${entityType === 'accessPoint' ? 'access_point' : entityType}_id`]: entityId,
      action: action ? 'unarchived' : 'archived',
    });
  };

  const mutationMap = {
    event: trpc.event.toggleArchive.useMutation({
      onSuccess: () => {
        utils.event.list.invalidate();
        utils.event.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    capa: trpc.capa.toggleArchive.useMutation({
      onSuccess: () => {
        utils.capa.list.invalidate();
        utils.capa.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    accessPoint: trpc.accessPoint.toggleArchive.useMutation({
      onSuccess: () => {
        utils.accessPoint.list.invalidate();
        utils.accessPoint.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    oshaReport: trpc.oshaReport.toggleArchive.useMutation({
      onSuccess: () => {
        utils.oshaReport.list.invalidate();
        utils.oshaReport.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    oshaAgencyReport: trpc.oshaAgencyReport.toggleArchive.useMutation({
      onSuccess: () => {
        utils.oshaAgencyReport.list.invalidate();
        utils.oshaAgencyReport.getById.invalidate({ id: entityId });
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    oshaSummary: trpc.oshaSummary.toggleArchiveOshaSummary.useMutation({
      onSuccess: () => {
        utils.oshaSummary.getEstablishmentInformation.invalidate();
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
    }),
    oshaLocation: trpc.oshaLocation.toggleArchive.useMutation({
      onSuccess: () => {
        utils.oshaLocation.list.invalidate();
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
  };

  const onArchive = async () => {
    const mutation = mutationMap[entityType];

    await mutation.mutateAsync({
      id: entityId,
      ...configMap[entityType].payload,
    });

    setShowDialog(false);
  };

  return (
    <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{archived ? 'Unarchive' : 'Archive'}</AlertDialogTitle>
          <AlertDialogDescription>
            {archived ? configMap[entityType].message.restore : configMap[entityType].message.archive}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onArchive} className={archived ? '' : 'bg-red-600 hover:bg-red-700'}>
            {archived ? 'Unarchive' : 'Archive'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
