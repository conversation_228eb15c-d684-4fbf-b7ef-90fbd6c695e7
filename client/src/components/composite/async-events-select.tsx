import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useInfiniteMinimalEvents } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { PopoverAnchor, PopoverPortal } from '@radix-ui/react-popover';
import { RouterOutputs } from '@shared/router.types';
import { STATUS_MAP } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search } from 'lucide-react';
import * as React from 'react';

type EventOption = RouterOutputs['event']['minimalList']['result'][number];

interface AsyncEventSelectProps {
  value?: EventOption['id'] | null;
  onChange: (selected: EventOption['id'] | undefined) => void;
  className?: string;
  placeholder?: string;
  mustIncludeObjectIds?: string[];
  oshaReportable?: boolean;
}

export const AsyncEventsSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select event...',
  mustIncludeObjectIds = [],
  oshaReportable,
  ...props
}: AsyncEventSelectProps) => {
  const [open, setOpen] = React.useState(false);
  const [selectedEvent, setSelectedEvent] = React.useState<EventOption | null>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);
  const [search, setSearch] = React.useState('');

  const debouncedSearch = useDebounce(search, 300);

  const {
    data: events,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteMinimalEvents({
    filters: {
      search: debouncedSearch,
      includeArchived: false,
      status: [],
      type: [],
      severity: [],
      oshaReportable,
      locationIds: [],
    },
    enabled: true,
    mustIncludeObjectIds,
  });

  const options = React.useMemo(() => {
    return events.reduce(
      (acc, event) => {
        acc[event.id] = event;
        return acc;
      },
      {} as Record<EventOption['id'], EventOption>,
    );
  }, [events]);

  React.useEffect(() => {
    if (selected && options[selected] && selectedEvent?.id !== selected) {
      setSelectedEvent(options[selected]);
    }
  }, [selected, options]);

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Handle infinite scrolling
  React.useEffect(() => {
    if (!open) return;

    const scrollArea = scrollAreaRef.current;
    if (!scrollArea || !hasNextPage) return;

    let scrollTimeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      clearTimeout(scrollTimeoutId);
      scrollTimeoutId = setTimeout(() => {
        const { scrollTop, scrollHeight, clientHeight } = scrollArea;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceFromBottom < 50 && !isFetchingNextPage) {
          fetchNextPage();
        }
      }, 100);
    };

    scrollArea.addEventListener('scroll', handleScroll);

    return () => {
      scrollArea.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
    setSearch(event.target.value);
  };

  const onSelect = (eventId: EventOption['id']) => {
    if (!onChange) return;
    onChange(eventId);
    setSelectedEvent(options[eventId] ?? null);
    setOpen(false);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  const renderSelectedValue = () => {
    if (selectedEvent) {
      const event = selectedEvent;

      return (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {event.slug} — {event.title}
          </span>
          <Badge variant="secondary" className="text-xs">
            {STATUS_MAP[event.status]}
          </Badge>
          <span className="text-xs text-muted-foreground">({formatDate(event.reportedAt)})</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          aria-expanded={open}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            'h-9 cursor-pointer',
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverAnchor />
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search events..."
                onChange={handleSearchChange}
                ref={inputRef}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              <ul>
                {Object.keys(options).length === 0 && !isLoading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No events found</li>
                )}
                {Object.values(options).map((event) => (
                  <li
                    onClick={() => onSelect(event.id)}
                    className="flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3"
                    key={event.id}
                  >
                    <Check
                      className={cn('size-4 flex-shrink-0', selected === event.id ? 'opacity-100' : 'opacity-0')}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="font-medium">
                        {event.slug} — {event.title}
                      </span>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {STATUS_MAP[event.status]}
                        </Badge>
                        <span className="text-xs text-muted-foreground">({formatDate(event.reportedAt)})</span>
                      </div>
                    </div>
                  </li>
                ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
