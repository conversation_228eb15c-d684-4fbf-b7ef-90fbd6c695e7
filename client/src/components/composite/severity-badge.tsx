import { Badge } from '@/components/ui/badge';
import { AlertCircle, Flag } from 'lucide-react';
import { severityEnum } from '@shared/schema';
import { cn } from '@/lib/utils';

export const SeverityBadge = ({ severity }: { severity?: (typeof severityEnum.enumValues)[number] | null }) => {
  if (!severity) {
    return null;
  }

  const severityMap = {
    [severityEnum.enumValues[0]]: 'Low',
    [severityEnum.enumValues[1]]: 'Medium',
    [severityEnum.enumValues[2]]: 'High',
    [severityEnum.enumValues[3]]: 'Critical',
  };

  const severityColorMap = {
    [severityEnum.enumValues[0]]: 'bg-green-50 text-green-700 border-green-200',
    [severityEnum.enumValues[1]]: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    [severityEnum.enumValues[2]]: 'bg-orange-50 text-orange-700 border-orange-200',
    [severityEnum.enumValues[3]]: 'bg-red-50 text-red-700 border-red-200',
  };

  const severityIconMap = {
    [severityEnum.enumValues[0]]: null,
    [severityEnum.enumValues[1]]: null,
    [severityEnum.enumValues[2]]: <Flag className="h-3 w-3 mr-1" />,
    [severityEnum.enumValues[3]]: <AlertCircle className="h-3 w-3 mr-1" />,
  };

  return (
    <Badge className={cn(severityColorMap[severity])} variant="outline">
      {severityIconMap[severity]}
      {severityMap[severity]}
    </Badge>
  );
};
