import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteAssetsPublic } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { PopoverAnchor, PopoverPortal } from '@radix-ui/react-popover';
import { RouterOutputs } from '@shared/router.types';
import { Location } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import * as React from 'react';

type AssetOption = RouterOutputs['asset']['searchPublic']['result'][number];

interface AsyncAssetMultiSelectProps {
  value?: AssetOption['id'][] | null;
  onChange: (selected: AssetOption['id'][] | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  locationId?: Location['id'];
  upkeepCompanyId?: string | null;
}

export const AsyncAssetMultiSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select assets...',
  disabled = false,
  locationId,
  upkeepCompanyId,
  ...props
}: AsyncAssetMultiSelectProps) => {
  const { user } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const [selectedAssets, setSelectedAssets] = React.useState<Record<string, AssetOption>>({});
  const inputRef = React.useRef<HTMLInputElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  const [search, setSearch] = React.useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: assets,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteAssetsPublic({
    search: debouncedSearch,
    upkeepCompanyId: upkeepCompanyId ?? user?.upkeepCompanyId!,
    locationId,
    enabled: !!upkeepCompanyId || !!user?.upkeepCompanyId,
  });

  const options = React.useMemo(() => {
    return (
      assets?.reduce(
        (acc, asset) => {
          acc[asset.id] = asset;
          return acc;
        },
        {} as Record<string, AssetOption>,
      ) || {}
    );
  }, [assets]);

  // Computed value for selected asset objects
  const selectedOptions = React.useMemo(() => {
    if (!Array.isArray(selected)) return [];
    return selected.map((id) => selectedAssets[id] || options[id]).filter(Boolean);
  }, [selected, selectedAssets, options]);

  const handleUnselect = (assetId: AssetOption['id']) => {
    if (disabled) return;

    const updatedSelectedIds = (selected || []).filter((id) => id !== assetId);
    onChange(updatedSelectedIds.length > 0 ? updatedSelectedIds : undefined);

    // Remove from selected assets when unselected
    setSelectedAssets((prev) => {
      const updated = { ...prev };
      delete updated[assetId];
      return updated;
    });
  };

  // Store asset data as it gets loaded to preserve across searches
  React.useEffect(() => {
    if (assets && assets.length > 0) {
      setSelectedAssets((prev) => {
        const updated = { ...prev };
        assets.forEach((asset) => {
          updated[asset.id] = asset;
        });
        return updated;
      });
    }
  }, [assets]);

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  React.useEffect(() => {
    if (!open) return;

    const scrollArea = scrollAreaRef.current;
    if (!scrollArea || !hasNextPage) return;

    let scrollTimeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      clearTimeout(scrollTimeoutId);
      scrollTimeoutId = setTimeout(() => {
        const { scrollTop, scrollHeight, clientHeight } = scrollArea;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceFromBottom < 50 && !isFetchingNextPage) {
          fetchNextPage();
        }
      }, 100);
    };

    scrollArea.addEventListener('scroll', handleScroll);

    return () => {
      scrollArea.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
    if (disabled) return;
    setSearch(event.target.value);
  };

  const onSelect = (assetId: AssetOption['id']) => {
    if (!onChange || disabled) return;

    const currentSelected = selected || [];
    const isSelected = currentSelected.includes(assetId);

    const updatedSelected = isSelected ? currentSelected.filter((id) => id !== assetId) : [...currentSelected, assetId];

    onChange(updatedSelected.length > 0 ? updatedSelected : undefined);
    setOpen(true);
  };

  const renderSelectedValue = () => {
    if (selectedOptions.length === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>;
    }

    return selectedOptions.map((asset) => (
      <Badge variant="outline" key={asset.id} className="flex items-center gap-1 pr-1 group-hover:bg-background">
        {asset.name}
        <Button
          variant="ghost"
          size="xs"
          className="border-none"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleUnselect(asset.id);
            }
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleUnselect(asset.id);
          }}
        >
          <X size={16} className="text-muted-foreground" />
        </Button>
      </Badge>
    ));
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          aria-expanded={open}
          aria-disabled={disabled}
          className={cn(
            'group flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            Array.isArray(selected) && selected.length > 1 ? 'h-full min-h-[2.25rem]' : 'h-9',
            disabled && 'cursor-not-allowed opacity-50',
            'cursor-pointer',
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0 flex-wrap">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverAnchor />
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search assets..."
                onChange={handleSearchChange}
                disabled={disabled}
                ref={inputRef}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              {Object.keys(options).length === 0 && !isLoading && (
                <div className="p-2 text-sm text-muted-foreground text-center">No assets found</div>
              )}

              {Object.values(options).map((asset) => {
                const isSelected = selected?.includes(asset.id) ?? false;
                return (
                  <div
                    key={asset.id}
                    onClick={() => !disabled && onSelect(asset.id)}
                    className={cn(
                      'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                      disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                    )}
                  >
                    <Check className={cn('size-4 flex-shrink-0', isSelected ? 'opacity-100' : 'opacity-0')} />
                    <span>{asset.name}</span>
                  </div>
                );
              })}

              {isFetchingNextPage && (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="animate-spin size-4" />
                  <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
