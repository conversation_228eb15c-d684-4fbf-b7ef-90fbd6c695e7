import { analytics } from '@/analytics';
import { Switch } from '@/components/ui/switch';
import { useAppContext } from '@/contexts/app-context';
import { BarChart3, Settings, Shield, X } from 'lucide-react';
import { useEffect, useState } from 'react';

export const ConsentManager = (
  {
    isOpen,
    onClose
  }: {
    isOpen: boolean,
    onClose: () => void
  }) => {
  const { user } = useAppContext();
  const [consents, setConsents] = useState({
    analytics: true,
    functional: true,
  });

  useEffect(() => {
    if (isOpen) {
      const currentConsent = analytics.getConsent();
      setConsents({
        analytics: currentConsent.analytics,
        functional: currentConsent.functional,
      });
    }
  }, [isOpen]);

  const handleSave = async () => {
    try {
      await analytics.handleConsentUpdate(
        {
          analytics: consents.analytics,
        },
        user
      );
      onClose();
    } catch (error) {
      console.error('Failed to update consent:', error);
    }
  };

  const handleToggle = (type: 'analytics') => {
    setConsents(prev => ({
      ...prev,
      [type]: !prev[type],
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-20">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 shadow-xl">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            Privacy & Data Settings
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div className="border rounded-lg p-4 bg-blue-50">
            <div className="flex items-start gap-3">
              <BarChart3 className="h-6 w-6 text-blue-600 mt-1" />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">Analytics & Performance</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      Help us improve your experience by sharing usage data and performance metrics.
                    </p>
                  </div>
                  <Switch
                    checked={consents.analytics}
                    onCheckedChange={() => handleToggle('analytics')}
                    className="ml-4"
                  />
                </div>
              </div>
            </div>
          </div>



          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-start gap-3">
              <Settings className="h-6 w-6 text-gray-600 mt-1" />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">Essential Functionality</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      Required for core features like authentication, session management, and security.
                    </p>
                  </div>
                  <Switch
                    checked={true}
                    disabled={true}
                    className="ml-4"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2">Always enabled</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 transition-colors"
          >
            Save Preferences
          </button>
        </div>
      </div>
    </div>
  );
}; 
