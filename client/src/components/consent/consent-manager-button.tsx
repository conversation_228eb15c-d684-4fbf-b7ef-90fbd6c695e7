import { useState } from 'react';
import { Shield } from 'lucide-react';
import { ConsentManager } from './consent-manager';
import { useConfig } from '@/hooks/use-config';

export const ConsentManagerButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { VITE_MIXPANEL_TOKEN } = useConfig();
  
  if (!VITE_MIXPANEL_TOKEN) {
    return <></>;
  }

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors border border-gray-200 hover:border-gray-300"
        aria-label="Privacy Settings"
      >
        <Shield className="h-4 w-4" />
        <span className="hidden sm:inline">Privacy</span>
      </button>
      
      <ConsentManager 
        isOpen={isOpen} 
        onClose={() => setIsOpen(false)} 
      />
    </>
  );
}; 