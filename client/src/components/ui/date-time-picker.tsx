'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { formatDate, formatTime } from '@shared/date-utils';
import { Calendar as CalendarIcon } from 'lucide-react';
import React from 'react';

export function DateTimePicker({
  selected,
  onSelect,
  className,
  onFocus,
  onlyDate,
  placeholder,
  ...props
}: {
  selected?: Date;
  onSelect?: (date: Date) => void;
  className?: string;
  onFocus?: () => void;
  onlyDate?: boolean;
  placeholder?: string;
} & React.ComponentProps<typeof Calendar>) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          data-empty={!selected}
          className={cn(
            'w-full data-[empty=true]:text-muted-foreground justify-start items-center pl-3 text-left font-normal',
            !selected && 'text-muted-foreground',
            className,
          )}
          onFocus={onFocus}
        >
          {selected ? formatDate(selected, onlyDate) : <span>{placeholder ?? 'Pick a date'}</span>}
          <CalendarIcon className="ml-auto h-4 w-4 text-muted-foreground" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          {...props}
          mode="single"
          captionLayout="dropdown"
          selected={selected}
          onSelect={onSelect}
          required={true}
        />
        {!onlyDate && (
          <div className="p-3 border-t border-border">
            <Input
              type="time"
              min="00:00"
              max="23:59"
              value={selected ? formatTime(selected, true) : ''}
              onChange={(e) => {
                const [hours, minutes] = e.target.value.split(':');
                const newDate = new Date(selected as Date);
                newDate.setHours(parseInt(hours), parseInt(minutes));
                onSelect?.(newDate);
              }}
            />
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
