import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { TransientFileSchema } from '@shared/schema.types';
import { ChevronLeft, ChevronRight, Download } from 'lucide-react';
import { useEffect, useState } from 'react';
import { z } from 'zod';

interface MediaViewerModalProps {
  files: z.infer<typeof TransientFileSchema>[];
  initialIndex: number;
  isOpen: boolean;
  onClose: () => void;
}

export function MediaViewerModal({ 
  files, 
  initialIndex, 
  isOpen, 
  onClose 
}: MediaViewerModalProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  
  // Reset current index when modal opens with new initial index
  useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex, isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex]);

  const currentFile = files[currentIndex];
  const isVideo = currentFile?.type?.startsWith('video/');
  const isImage = currentFile?.type?.startsWith('image/');

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : files.length - 1));
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev < files.length - 1 ? prev + 1 : 0));
  };

  const handleDownload = () => {
    if (currentFile) {
      const link = document.createElement('a');
      link.href = currentFile.url;
      link.download = currentFile.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (!currentFile) return null;

  return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-5xl max-w-[95vw] max-h-[90vh] p-0 border-0">
          <div className="h-full flex flex-col bg-background rounded-lg overflow-hidden">
            {/* Header */}
            <div className="flex-shrink-0 p-4 border-b">
              <div className="flex items-center justify-between">              
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDownload}
                >
                  <Download className="h-3.5 w-3.5" />
                </Button>
                <div className="flex justify-center flex-1">
                  <h3 className="font-medium truncate text-sm">
                    {currentFile.name}
                  </h3>
                </div>
              </div>
            </div>

            {/* Media Content */}
            <div 
              className="flex items-center justify-center p-4 relative overflow-hidden"
              style={{ 
                height: files.length > 1 
                  ? 'calc(90vh - 140px)' // Reserve space for header + thumbnails
                  : 'calc(90vh - 80px)'   // Reserve space for header only
              }}
            >
              {isImage && (
                <img
                  src={currentFile.url}
                  alt={currentFile.name}
                  className="max-w-full max-h-full object-contain"
                />
              )}
              
              {isVideo && (
                <video
                  src={currentFile.url}
                  controls
                  className="max-w-full max-h-full"
                  autoPlay={false}
                >
                  Your browser does not support the video tag.
                </video>
              )}

              {/* Navigation Arrows */}
              {files.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goToPrevious}
                    className="absolute left-6 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-black/40 text-white border border-white/20 hover:border-white/40 shadow-lg transition-all duration-200 hover:scale-105"
                  >
                    <ChevronLeft className="h-6 w-6" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goToNext}
                    className="absolute right-6 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-black/40 text-white border border-white/20 hover:border-white/40 shadow-lg transition-all duration-200 hover:scale-105"
                  >
                    <ChevronRight className="h-6 w-6" />
                  </Button>
                </>
              )}
          </div>

            {/* Bottom Thumbnail Strip (for multiple files) */}
            {files.length > 1 && (
              <div className="flex-shrink-0 p-3 border-t bg-background/95">
                <div className="flex justify-center gap-2 overflow-x-auto">
                  {files.map((file, index) => (
                    <button
                      key={`${file.name}-${index}`}
                      onClick={() => setCurrentIndex(index)}
                      className={cn(
                        "flex-shrink-0 w-14 h-14 rounded border-2 overflow-hidden transition-colors",
                        index === currentIndex 
                          ? "border-primary" 
                          : "border-border hover:border-primary/60"
                      )}
                    >
                    {file.type?.startsWith('image/') ? (
                      <img
                        src={file.url}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <video
                        src={file.url}
                        className="w-full h-full object-cover"
                        muted
                      />
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}
          </div>
        </DialogContent>
      </Dialog>
  );
} 