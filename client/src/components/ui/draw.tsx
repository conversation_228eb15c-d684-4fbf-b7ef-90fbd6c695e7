import { Button } from '@/components/ui/button';
import React from 'react';

interface DrawProps {
  onFinish?: (data: string) => void;
  onClear?: () => void;
  viewMode?: boolean;
  data?: string;
}

export const Draw = ({ onFinish, onClear, viewMode = false, data }: DrawProps) => {
  const canvasRef = React.useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = React.useState(false);
  const [hasDrawn, setHasDrawn] = React.useState(false);

  // Initialize canvas with proper scaling
  React.useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set up canvas scaling for high DPI displays
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;

    ctx.scale(dpr, dpr);

    // Set drawing styles
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // If in view mode and signature data exists, display it
    if (viewMode && data) {
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, rect.width, rect.height);
      };
      img.src = data;
    }
  }, [viewMode, data]);

  // Get scaled coordinates
  const getCoordinates = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    return {
      x: clientX - rect.left,
      y: clientY - rect.top,
    };
  };

  // Signature drawing functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (viewMode) return;
    e.preventDefault();
    const canvas = canvasRef.current;
    if (!canvas) return;

    setIsDrawing(true);
    setHasDrawn(true);

    const { x, y } = getCoordinates(e);
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.beginPath();
      ctx.moveTo(x, y);
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (viewMode) return;
    e.preventDefault();
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const { x, y } = getCoordinates(e);
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.lineTo(x, y);
      ctx.stroke();
    }
  };

  const stopDrawing = (e?: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (viewMode) return;
    if (e) e.preventDefault();
    setIsDrawing(false);
  };

  const handleFinish = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const data = canvas.toDataURL();
      onFinish?.(data);
    }

    setHasDrawn(false);
    setIsDrawing(false);
  };

  const handleClear = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
    setHasDrawn(false);
    setIsDrawing(false);
    onClear?.();
  };

  return (
    <div className="border border-gray-200 rounded-md">
      {!viewMode && (
        <div className="p-3 border-b border-gray-200 bg-gray-50 rounded-t-md">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Sign below:</span>
            <div className="flex gap-2">
              <Button type="button" variant="outline" size="sm" onClick={handleClear} className="text-xs">
                Clear
              </Button>
              {hasDrawn && (
                <Button type="button" variant="default" size="sm" onClick={handleFinish} className="text-xs">
                  Done
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
      <div className="p-4">
        <canvas
          ref={canvasRef}
          className={`w-full h-[200px] border border-gray-200 rounded ${viewMode ? 'cursor-default' : 'cursor-crosshair'}`}
          style={{ touchAction: viewMode ? 'auto' : 'none' }}
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
        />
      </div>
    </div>
  );
};
