'use client';

import * as React from 'react';

import { Calendar } from '@/components/ui/calendar';
import { type DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { addDays, format } from 'date-fns';
import { cn } from '@/lib/utils';

export function DateRangePicker({
  placeholder,
  range,
  setRange,
  className,
}: {
  placeholder?: string;
  range: DateRange | undefined;
  setRange: (date: DateRange) => void;
  className?: string;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          id="date-range"
          variant="outline"
          size="sm"
          className={cn('justify-start items-center pl-3 text-left font-normal', className)}
        >
          <CalendarIcon className="h-4 w-4" />
          {range?.from && range?.to ? (
            <>
              {format(range.from, 'LLL dd, y')} - {format(range.to, 'LLL dd, y')}
            </>
          ) : (
            <span>{placeholder || 'Pick a date range'}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          captionLayout="dropdown"
          selected={range}
          onSelect={(range: DateRange | undefined) => {
            if (range && range.from && !range.to) {
              setRange({
                from: range.from,
                to: addDays(range.from, 1),
              });
              return;
            }

            if (range) {
              setRange(range);
            }
          }}
          numberOfMonths={2}
          min={2}
        />
      </PopoverContent>
    </Popover>
  );
}
