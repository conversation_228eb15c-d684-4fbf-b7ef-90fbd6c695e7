import { Gatekeeper } from '@/components/layout/gatekeeper';
import MainLayout from '@/components/layout/main-layout';
import { Toaster } from '@/components/ui/sonner';
import { AppContextProvider } from '@/contexts/app-context';
import { useConfig } from '@/hooks/use-config';
import AccessPointsView from '@/pages/AccessPointsView';
import CapaDetails from '@/pages/CapaDetails';
import CapaLog from '@/pages/CapaLog';
import EditCapa from '@/pages/EditCapa';
import EditEvent from '@/pages/EditEvent';
import EditJha from '@/pages/EditJha';
import EditOshaAgencyReport from '@/pages/EditOshaAgencyReport';
import EditOshaReport from '@/pages/EditOshaReport';
import EventDetails from '@/pages/EventDetails';
import EventLog from '@/pages/EventLog';
import JhaDetails from '@/pages/JhaDetails';
import JhaLog from '@/pages/JhaLog';
import NewCapa from '@/pages/NewCapa';
import NewEvent from '@/pages/NewEvent';
import NewEventReport from '@/pages/NewEventReport';
import NewJha from '@/pages/NewJha';
import NewOshaAgencyReport from '@/pages/NewOshaAgencyReport';
import NewOshaReport from '@/pages/NewOshaReport';
import NotFound from '@/pages/NotFound';
import OshaAgencyReportDetails from '@/pages/OshaAgencyReportDetails';
import OshaAgencyReportLogs from '@/pages/OshaAgencyReportLogs';
import OshaLocationsLog from '@/pages/OshaLocationsLog';
import OshaLogs from '@/pages/OshaLogs';
import OshaReportDetails from '@/pages/OshaReportDetails';
import OshaSummary from '@/pages/OshaSummary';
import { trpc, trpcClient } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { StrictMode } from 'react';
import { Route, Switch, useLocation } from 'wouter';

function Router() {
  return (
    <Switch>
      <Route path={ROUTES.EVENT_NEW} component={NewEvent} />
      <Route path={ROUTES.EVENT_EDIT} component={EditEvent} />
      <Route path={ROUTES.EVENT_DETAILS} component={EventDetails} />
      <Route path={ROUTES.EVENT_LIST} component={EventLog} />
      <Route path={ROUTES.ACCESS_POINTS_LIST} component={AccessPointsView} />
      <Route path={ROUTES.ACCESS_POINTS_NEW} component={AccessPointsView} />
      <Route path={ROUTES.OSHA_LOCATIONS_LIST} component={OshaLocationsLog} />
      <Route path={ROUTES.OSHA_LOCATIONS_NEW} component={OshaLocationsLog} />
      <Route path={ROUTES.CAPA_LIST} component={CapaLog} />
      <Route path={ROUTES.CAPA_NEW} component={NewCapa} />
      <Route path={ROUTES.CAPA_EDIT} component={EditCapa} />
      <Route path={ROUTES.CAPA_DETAILS} component={CapaDetails} />
      <Route path={ROUTES.OSHA_REPORTS} component={OshaLogs} />
      <Route path={ROUTES.OSHA_REPORT_NEW} component={NewOshaReport} />
      <Route path={ROUTES.OSHA_REPORT_EDIT} component={EditOshaReport} />
      <Route path={ROUTES.OSHA_REPORT_DETAILS} component={OshaReportDetails} />
      <Route path={ROUTES.OSHA_SUMMARY} component={OshaSummary} />
      <Route path={ROUTES.OSHA_AGENCY_REPORTS} component={OshaAgencyReportLogs} />
      <Route path={ROUTES.OSHA_AGENCY_REPORTS_NEW} component={NewOshaAgencyReport} />
      <Route path={ROUTES.OSHA_AGENCY_REPORT_EDIT} component={EditOshaAgencyReport} />
      <Route path={ROUTES.OSHA_AGENCY_REPORT_DETAILS} component={OshaAgencyReportDetails} />
      <Route path={ROUTES.JHA_LIST} component={JhaLog} />
      <Route path={ROUTES.JHA_NEW} component={NewJha} />
      <Route path={ROUTES.JHA_EDIT} component={EditJha} />
      <Route path={ROUTES.JHA_DETAILS} component={JhaDetails} />
      <Route path={ROUTES.NOT_FOUND} component={NotFound} />
      <Route path="*" component={NotFound} />
    </Switch>
  );
}

function PublicRouter() {
  return (
    <Switch>
      <Route path={ROUTES.EVENT_PUBLIC_REPORT} component={NewEventReport} />
      <Route path="*" component={NotFound} />
    </Switch>
  );
}

function AppContent() {
  const [location] = useLocation();

  // Initialize config
  useConfig();

  // Check if current location is a public route
  const isPublicRoute = location.startsWith(ROUTES.PUBLIC_BASE);

  if (isPublicRoute) {
    return (
      <>
        <Toaster />
        <PublicRouter />
      </>
    );
  }

  return (
    <AppContextProvider>
      <Toaster />
      <MainLayout>
        <Gatekeeper>
          <Router />
        </Gatekeeper>
      </MainLayout>
    </AppContextProvider>
  );
}

const queryClient = new QueryClient();

export function App() {
  return (
    <StrictMode>
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <AppContent />
        </QueryClientProvider>
      </trpc.Provider>
    </StrictMode>
  );
}
