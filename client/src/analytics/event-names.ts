/**
 * Analytics Event Name Constants
 *
 * This file contains all analytics event names as constants to prevent typos
 * and provide better IDE support. Event names are organized by domain and
 * match exactly with the event types defined in the event-types folder.
 *
 * Usage:
 * analytics.track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, { ... });
 *
 * Instead of:
 * analytics.track('event.form_viewed', { ... });
 */

export const ANALYTICS_EVENTS = {
  /**
   * Event-related analytics events
   * Covers event creation, form interactions, voice features, and management
   */
  EVENT: {
    // Event Creation & Form Interaction Events
    FORM_VIEWED: 'event.form_viewed',
    VOICE_STARTED: 'event.voice_started',
    VOICE_SUCCESSFUL: 'event.voice_successful',
    VOICE_FAILED: 'event.voice_failed',
    FIELD_AI_POPULATED: 'event.field_ai_populated',
    FORM_VALIDATION_FAILED: 'event.form_validation_failed',
    FORM_SUBMITTED: 'event.form_submitted',
    VOICE_TO_FORM_ABANDONED: 'event.voice_to_form_abandoned',
    FORM_ABANDONED: 'event.form_abandoned',

    // Event View & Interaction Events
    LOG_VIEW_OPENED: 'event.log_view_opened',
    DETAIL_VIEW_OPENED: 'event.detail_view_opened',
    FILTER_APPLIED: 'event.filter_applied',
    FILTER_RESET: 'event.filter_reset',
    SEARCH_PERFORMED: 'event.search_performed',
    ROW_ACTION_CLICKED: 'event.row_action_clicked',
    ACTION_TAKEN: 'event.action_taken',
    EDIT_INITIATED: 'event.edit_initiated',
    CAPA_CREATED_FROM_EVENT: 'event.capa_created_from_event',
    OSHA_LOG_CREATED_FROM_EVENT: 'event.osha_log_created_from_event',
    REVIEWED: 'event.reviewed',
    CLOSED_WITHOUT_ACTION: 'event.closed_without_action',
    ARCHIVED: 'event.archived',
    PRINT_TRIGGERED: 'event.print_triggered',
    EXPORT_TRIGGERED: 'event.export_triggered',
  },

  /**
   * CAPA (Corrective and Preventive Action) related analytics events
   * Covers CAPA creation, form interactions, tracking, and workflow management
   */
  CAPA: {
    // CAPA Creation & Form Interaction Events
    CREATE_INITIATED: 'capa.create_initiated',
    FORM_VIEWED: 'capa.form_viewed',
    VOICE_STARTED: 'capa.voice_started',
    VOICE_SUCCESSFUL: 'capa.voice_successful',
    VOICE_FAILED: 'capa.voice_failed',
    FIELD_AI_POPULATED: 'capa.field_ai_populated',
    FORM_VALIDATION_FAILED: 'capa.form_validation_failed',
    FORM_SAVED_DRAFT: 'capa.form_saved_draft',
    FORM_SUBMITTED: 'capa.form_submitted',
    FORM_ABANDONED: 'capa.form_abandoned',

    // CAPA View & Interaction Events
    TRACKER_VIEW_OPENED: 'capa.tracker_view_opened',
    DETAIL_VIEW_OPENED: 'capa.detail_view_opened',
    FILTER_APPLIED: 'capa.filter_applied',
    FILTER_RESET: 'capa.filter_reset',
    SEARCH_PERFORMED: 'capa.search_performed',
    ROW_ACTION_CLICKED: 'capa.row_action_clicked',
    EDIT_INITIATED: 'capa.edit_initiated',
    ARCHIVED: 'capa.archived',
    MARKED_COMPLETE: 'capa.marked_complete',
    PRINT_TRIGGERED: 'capa.print_triggered',
    EXPORT_TRIGGERED: 'capa.export_triggered',

    // CAPA Status & Workflow Events
    STATUS_CHANGED: 'capa.status_changed',
    WORK_ORDER_CREATED: 'capa.work_order_created',
  },

  /**
   * Access Point (QR Code) related analytics events
   * Covers QR code generation, management, and usage tracking
   */
  ACCESS_POINT: {
    // QR Code Access Point Events
    TABLE_VIEW_OPENED: 'access_point.table_view_opened',
    CREATE_INITIATED: 'access_point.create_initiated',
    CREATED: 'access_point.created',
    DOWNLOADED: 'access_point.downloaded',
    ARCHIVED: 'access_point.archived',
    FILTER_APPLIED: 'access_point.filter_applied',
    SEARCH_PERFORMED: 'access_point.search_performed',

    // Bulk Import Events
    BULK_IMPORT_INITIATED: 'access_point.bulk_import_initiated',
    BULK_IMPORT_SUCCESS: 'access_point.bulk_import_success',
    BULK_IMPORT_ERROR: 'access_point.bulk_import_error',
    BULK_IMPORT_CANCELLED: 'access_point.bulk_import_cancelled',

    // Custom QR Code Usage Events
    EVENT_SUBMITTED_VIA_QR: 'access_point.event_submitted_via_qr',
  },
} as const;

/**
 * Type helper to extract all event names as a union type
 * This ensures type safety when using the constants
 */
export type AnalyticsEventName =
  (typeof ANALYTICS_EVENTS)[keyof typeof ANALYTICS_EVENTS][keyof (typeof ANALYTICS_EVENTS)[keyof typeof ANALYTICS_EVENTS]];
