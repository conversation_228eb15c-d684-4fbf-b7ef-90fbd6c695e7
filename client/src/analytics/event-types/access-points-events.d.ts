import { BaseEventProperties } from './base-event-properties';
import { ANALYTICS_EVENTS } from '../event-names';

export interface AccessPointEvents {
  // Access Point Management Events
  [ANALYTICS_EVENTS.ACCESS_POINT.TABLE_VIEW_OPENED]: {};

  [ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED]: {};

  [ANALYTICS_EVENTS.ACCESS_POINT.CREATED]: {
    access_point_id?: string;
    access_point_name?: string;
    location_id?: string;
    has_label_text?: boolean;
  };

  [ANALYTICS_EVENTS.ACCESS_POINT.DOWNLOADED]: {
    access_point_id?: string;
    download_format?: 'PNG' | 'SVG';
  };

  [ANALYTICS_EVENTS.ACCESS_POINT.ARCHIVED]: {
    access_point_id?: string;
  };

  [ANALYTICS_EVENTS.ACCESS_POINT.FILTER_APPLIED]: {
    filter_name?: string;
    filter_value?: string | string[];
  };

  [ANALYTICS_EVENTS.ACCESS_POINT.SEARCH_PERFORMED]: {
    search_term?: string;
    result_count?: number;
  };

  // Bulk Import Events
  [ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_INITIATED]: {};

  [ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_SUCCESS]: {
    total_count?: number;
    success_count?: number;
    error_count?: number;
    file_name?: string;
  };

  [ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_ERROR]: {
    error_message?: string;
    file_name?: string;
  };

  [ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_CANCELLED]: {
    file_name?: string;
  };

  // QR Code Usage Events
  [ANALYTICS_EVENTS.ACCESS_POINT.EVENT_SUBMITTED_VIA_QR]: {
    access_point_id?: string;
    location_id?: string;
    time_from_scan_to_submit?: number;
  } & BaseEventProperties;
}
