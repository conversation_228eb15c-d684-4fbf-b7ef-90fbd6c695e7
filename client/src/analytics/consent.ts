interface ConsentState {
  analytics: boolean;
  functional: boolean;
  lastUpdated: string | null;
}

const CONSENT_VERSION = '1.0';
const STORAGE_KEY = 'ehs_user_consent';

const defaultConsent: ConsentState = {
  analytics: true,
  functional: true,
  lastUpdated: null,
};

export const ConsentManager = () => {
  const getStoredConsent = (): ConsentState => {
    if (typeof window === 'undefined' || !window.localStorage) {
      return { ...defaultConsent };
    }

    try {
      const stored = window.localStorage.getItem(STORAGE_KEY);
      if (!stored) return { ...defaultConsent };

      const parsed = JSON.parse(stored);

      // Version check - reset if old version
      if (parsed.version !== CONSENT_VERSION) {
        return { ...defaultConsent };
      }

      return {
        analytics: parsed.consent?.analytics ?? defaultConsent.analytics,
        functional: parsed.consent?.functional ?? defaultConsent.functional,
        lastUpdated: parsed.consent?.lastUpdated ?? null,
      };
    } catch (error) {
      console.error('[EHS Consent] Failed to read stored consent:', error);
      return { ...defaultConsent };
    }
  };

  const saveConsent = (consent: ConsentState): void => {
    if (typeof window === 'undefined' || !window.localStorage) {
      return;
    }

    try {
      const toStore = {
        version: CONSENT_VERSION,
        consent: {
          ...consent,
          lastUpdated: new Date().toISOString(),
        },
      };

      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(toStore));
    } catch (error) {
      console.error('[EHS Consent] Failed to save consent:', error);
    }
  };

  const updateConsent = (updates: Partial<Pick<ConsentState, 'analytics'>>): void => {
    const current = getStoredConsent();
    const updated = {
      ...current,
      ...updates,
      lastUpdated: new Date().toISOString(),
    };

    saveConsent(updated);
  };

  const getConsent = (): ConsentState => {
    return getStoredConsent();
  };

  const shouldShowConsentBanner = (): boolean => {
    const consent = getStoredConsent();
    return consent.lastUpdated === null;
  };

  return {
    updateConsent,
    getConsent,
    shouldShowConsentBanner,
  };
};
