import { TransientAccessPoint } from '@/components/access-points/access-point.types';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const generatePublicEventUrl = (accessPoint: TransientAccessPoint) => {
  // Construct the full URL
  const baseUrl = window.location.origin;
  const queryParams = new window.URLSearchParams();
  queryParams.append('accessPointId', accessPoint.id);
  queryParams.append('upkeepCompanyId', accessPoint.upkeepCompanyId);

  return `${baseUrl}${ROUTES.EVENT_PUBLIC_REPORT}?${queryParams.toString()}`;
};

export const cleanUpOshaLocationName = (name: string) => {
  return name.toLowerCase().replace(/[^a-z0-9]+/g, '_');
};
/**
 * Formats a phone number string into the format (XXX) XXX-XXXX
 * Strips all non-digit characters and applies formatting based on length
 */
export const formatPhoneNumber = (value: string) => {
  const phoneNumber = value.replace(/[^\d]/g, '');
  const phoneNumberLength = phoneNumber.length;

  if (phoneNumberLength < 4) return phoneNumber;
  if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  }
  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
};
