import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/router.types';
import { CAPA_PRIORITY_MAP, CAPA_TAGS_MAP, CAPA_TYPE_MAP, RCA_METHOD_MAP, STATUS_MAP } from '@shared/schema.types';

export const exportCapasCSV = (capas: RouterOutputs['capa']['export']) => {
  // CSV Headers for comprehensive CAPA export
  const headers = [
    'CAPA ID',
    'Title',
    'Type',
    'Status',
    'Priority',
    'Owner',
    'Due Date',
    'Created Date',
    'Last Updated',
    'Related Event',
    'Tags',
    'RCA Method',
    'Archived',
    'Summary',
    'Actions to Address',
  ];

  // Convert data to CSV rows
  const rows = capas.map((capa) => {
    // Format dates
    const dueDate = capa.dueDate ? formatDate(capa.dueDate) : '';
    const createdDate = capa.createdAt ? formatDate(capa.createdAt) : '';
    const lastUpdated = capa.updatedAt ? formatDate(capa.updatedAt) : '';

    // Map enum values to human-readable strings
    const capaType = capa.type ? CAPA_TYPE_MAP[capa.type] : '';
    const capaStatus = capa.status ? STATUS_MAP[capa.status] : '';
    const capaPriority = capa.priority ? CAPA_PRIORITY_MAP[capa.priority] : '';

    // Get owner name
    const ownerName = capa.owner ? `${capa.owner.fullName || ''} (${capa.owner.email || ''})`.trim() : '';

    // Format tags as comma-separated string
    const formattedTags =
      capa.tags?.map((tag) => CAPA_TAGS_MAP[tag as keyof typeof CAPA_TAGS_MAP] || tag).join(', ') || '';

    // Get RCA method
    const rcaMethod = capa.rcaMethod ? RCA_METHOD_MAP[capa.rcaMethod as keyof typeof RCA_METHOD_MAP] : '';

    // Format archived status
    const archivedStatus = capa.archived ? 'Yes' : 'No';

    // Get related event information
    const relatedEvent = capa.eventSlug || '';

    // Clean up summary and actions for CSV (remove line breaks and extra spaces)
    const cleanSummary = (capa.summary || '').replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
    const cleanActions = (capa.actionsToAddress || '').replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();

    return [
      `"${capa.slug || ''}"`,
      `"${capa.title || ''}"`,
      `"${capaType}"`,
      `"${capaStatus}"`,
      `"${capaPriority}"`,
      `"${ownerName}"`,
      `"${dueDate}"`,
      `"${createdDate}"`,
      `"${lastUpdated}"`,
      `"${relatedEvent}"`,
      `"${formattedTags}"`,
      `"${rcaMethod}"`,
      `"${archivedStatus}"`,
      `"${cleanSummary}"`,
      `"${cleanActions}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `capas_export` });
};
