import { handleExportCSVFile } from '@/lib/download-file';
import { cleanUpOshaLocationName } from '@/lib/utils';
import { RouterOutputs } from '@shared/router.types';
import { toast } from 'sonner';

export const generateOshaSummaryReport = ({
  summary,
  establishmentInfo,
  onDownload,
}: {
  summary: RouterOutputs['oshaSummary']['getOshaCasesSummary'];
  establishmentInfo: RouterOutputs['oshaSummary']['getEstablishmentInformation'];
  onDownload: () => void;
}) => {
  try {
    // Generate CSV content using OSHA ITA compliant format
    const csvHeaders = [
      'company_name',
      'ein',
      'naics_code',
      'size',
      'year_filing_for',
      'annual_average_employees',
      'total_hours_worked',
      'no_injuries_illnesses',
      'total_deaths',
      'total_days_away_cases',
      'total_days_away',
      'total_restricted_work_cases',
      'total_restricted_work_days',
      'total_other_cases',
    ];

    if (
      !establishmentInfo?.companyAnnualAverageNumberOfEmployees ||
      summary.totalCases === null ||
      summary.totalDaysAway === null ||
      summary.totalDaysRestricted === null
    ) {
      toast.error('Failed to generate report', {
        description: 'Please try again later',
      });
      console.error('Establishment info malformed', establishmentInfo, summary);
      return;
    }

    // Determine establishment size code based on employee count
    const getSizeCode = (employees: number) => {
      if (employees < 20) return 1;
      if (employees <= 99) return 21;
      if (employees <= 249) return 22;
      return 3; // 250 or more
    };

    // Determine if there were injuries/illnesses
    const hasInjuries = (summary?.totalCases ?? 0) > 0 ? 1 : 2;

    // Calculate total days (for prototype, estimate based on case counts)
    const estimatedDaysAway = (summary?.totalDaysAway ?? 0) * 15; // Estimate 15 days per case
    const estimatedDaysRestricted = (summary?.totalDaysRestricted ?? 0) * 10; // Estimate 10 days per case

    const csvData = [
      establishmentInfo?.companyName,
      establishmentInfo?.companyEIN,
      establishmentInfo?.companyNAICSCode,
      getSizeCode(establishmentInfo.companyAnnualAverageNumberOfEmployees),
      establishmentInfo?.year,
      establishmentInfo?.companyAnnualAverageNumberOfEmployees,
      establishmentInfo?.companyTotalHoursWorked,
      hasInjuries,
      summary.deaths,
      summary.daysAwayCases,
      estimatedDaysAway,
      summary.restrictedWorkCases,
      estimatedDaysRestricted,
      summary.otherCases,
    ];

    // Create CSV string
    const csvContent = [csvHeaders.join(','), csvData.join(',')].join('\n');

    handleExportCSVFile({
      content: csvContent,
      fileName: `osha_300a_summary_${cleanUpOshaLocationName(establishmentInfo?.oshaLocation?.name ?? '')}_${establishmentInfo?.year}`,
    });

    onDownload();
  } catch (error) {
    console.error(error);
    toast.error('Export Error', {
      description: 'Failed to generate OSHA report. Please try again.',
    });
  }
};
