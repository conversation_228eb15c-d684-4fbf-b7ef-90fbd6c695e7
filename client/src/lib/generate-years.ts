/**
 * Generates an array of years from 2025 to the current year (inclusive)
 * @returns Array of years as numbers, sorted in ascending order
 */
export const generateYears = (): number[] => {
  const currentYear = new Date().getFullYear();
  const startYear = 2025;

  // If current year is before 2025, return empty array
  if (currentYear < startYear) {
    return [];
  }

  const years: number[] = [];
  for (let year = currentYear; year >= startYear; year--) {
    years.push(year);
  }

  return years;
};
