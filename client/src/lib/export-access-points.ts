import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/router.types';
import { ACCESS_POINT_STATUS_MAP } from '@shared/schema.types';

export const exportAccessPointsCSV = (accessPoints: RouterOutputs['accessPoint']['export']) => {
  // CSV Headers for access points export
  const headers = [
    'Access Point ID',
    'Name',
    'Description',
    'Status',
    'Location',
    'Created By',
    'Created Date',
    'Archived',
  ];

  // Convert data to CSV rows
  const rows = accessPoints.map((accessPoint) => {
    // Format the created date
    const createdDate = accessPoint.createdAt ? formatDate(accessPoint.createdAt) : '';

    // Map status enum to human-readable string
    const status = accessPoint.status ? ACCESS_POINT_STATUS_MAP[accessPoint.status] : '';

    // Get location name
    const locationName = accessPoint.location?.name || '';

    // Get created by information with user details
    const createdBy = accessPoint.createdByUser
      ? `${accessPoint.createdByUser.fullName || ''} (${accessPoint.createdByUser.email || ''})`.trim()
      : accessPoint.createdBy || '';

    // Format archived status
    const archivedStatus = accessPoint.archived ? 'Yes' : 'No';

    // Clean up description for CSV (remove line breaks and extra spaces)
    const cleanDescription = (accessPoint.description || '').replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();

    return [
      `"${accessPoint.id || ''}"`,
      `"${accessPoint.name || ''}"`,
      `"${cleanDescription}"`,
      `"${status}"`,
      `"${locationName}"`,
      `"${createdBy}"`,
      `"${createdDate}"`,
      `"${archivedStatus}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `access_points_export` });
};
