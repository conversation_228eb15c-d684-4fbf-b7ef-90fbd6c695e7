import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { OSHA_TYPE_MAP } from '@shared/osha.types';
import { RouterOutputs } from '@shared/router.types';

export const exportOshaLogsCSV = (oshaReports: RouterOutputs['oshaReport']['export']) => {
  // CSV Headers matching the example format
  const headers = [
    'Case No.',
    'Employee Name',
    'Job Title',
    'Date of Injury/Illness',
    'Where Event Occurred',
    'Description of Injury/Illness',
    'Classification',
    'Days Away from Work',
    'Days of Job Transfer/Restriction',
    'OSHA Reportable',
  ];

  // Convert data to CSV rows
  const rows = oshaReports.map((report) => {
    // Format the date
    const dateOfInjury = report.eventReportedAt ? formatDate(report.eventReportedAt) : '';

    // Get location name or fallback to work location
    const whereEventOccurred = report.oshaLocation?.name || report.employeeWorkLocation || '';

    // Get event title as description of injury/illness
    const description = report.eventTitle || '';

    // Map OSHA type to classification
    const classification = report.type ? OSHA_TYPE_MAP[report.type] : '';

    // Format days away and restricted
    const daysAway = report.daysAwayFromWork?.toString() || '0';
    const daysRestricted = report.daysRestrictedFromWork?.toString() || '0';

    // OSHA Reportable - assume all reports in this list are reportable
    const oshaReportable = 'Yes';

    return [
      `"${report.slug || ''}"`,
      `"${report.employeeName || ''}"`,
      `"${report.employeeJobTitle || ''}"`,
      `"${dateOfInjury}"`,
      `"${whereEventOccurred}"`,
      `"${description}"`,
      `"${classification}"`,
      `"${daysAway}"`,
      `"${daysRestricted}"`,
      `"${oshaReportable}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `osha_logs_export` });
};
