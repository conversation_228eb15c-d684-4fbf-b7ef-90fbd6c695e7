import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/router.types';

export const exportOshaLocationsCSV = (oshaLocations: RouterOutputs['oshaLocation']['export']) => {
  // CSV Headers for OSHA locations export
  const headers = ['Location ID', 'Location Name', 'Created Date', 'Created By', 'Status', 'Last Updated'];

  // Convert data to CSV rows
  const rows = oshaLocations.map((location) => {
    // Format dates
    const createdDate = location.createdAt ? formatDate(location.createdAt) : '';
    const lastUpdated = location.updatedAt ? formatDate(location.updatedAt) : '';

    // Determine status based on archivedAt
    const status = location.archivedAt ? 'Archived' : 'Active';

    // Get created by information with user details
    const createdBy = location.createdByUser
      ? `${location.createdByUser.fullName} (${location.createdByUser.email})`.trim()
      : location.createdBy || '';

    return [
      `"${location.id || ''}"`,
      `"${location.name || ''}"`,
      `"${createdDate}"`,
      `"${createdBy}"`,
      `"${status}"`,
      `"${lastUpdated}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `osha_locations_export` });
};
