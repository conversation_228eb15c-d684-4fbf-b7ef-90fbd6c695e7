import { trpc } from '@/providers/trpc';
import { analytics } from '@/analytics';
import { useEffect } from 'react';

/**
 * hook to get config from API and initialize analytics
 */
export const useConfig = () => {
  const { data: config, isLoading } = trpc.config.getClientConfig.useQuery();

  // Initialize analytics when config is available
  useEffect(() => {
    if (config) {
      const token = config.VITE_MIXPANEL_TOKEN;
      const debugEnabled = config.VITE_SHOW_ANALYTICS_DEBUG || false;

      analytics.initialize(token, debugEnabled);
    }
  }, [config]);

  return {
    VITE_MIXPANEL_TOKEN: config?.VITE_MIXPANEL_TOKEN || '',
    VITE_SHOW_ANALYTICS_DEBUG: config?.VITE_SHOW_ANALYTICS_DEBUG || false,
    DROMO_API_KEY: config?.DROMO_API_KEY || '',
    isLoading,
    hasToken: !!config?.VITE_MIXPANEL_TOKEN?.trim(),
  };
};
