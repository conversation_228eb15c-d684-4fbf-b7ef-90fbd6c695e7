import { useCallback } from 'react';
import { analytics } from '@/analytics';
import type { AnalyticsEvents } from '@/analytics/analytics-events';
import { useAppContext } from '@/contexts/app-context';

/**
 * Main analytics hook for EHS application
 * Automatically integrates user context for GDPR/CCPA compliant tracking
 */
export const useAnalytics = () => {
  const { user } = useAppContext();

  const track = useCallback(
    <T extends keyof AnalyticsEvents>(eventName: T, properties?: AnalyticsEvents[T]) => {
      analytics.track(eventName, properties, user);
    },
    [user],
  );

  const updateConsent = useCallback((consent: { analytics?: boolean }) => {
    analytics.updateConsent(consent);
  }, []);

  const getConsent = useCallback(() => {
    return analytics.getConsent();
  }, []);

  const shouldShowConsentBanner = useCallback(() => {
    return analytics.shouldShowConsentBanner();
  }, []);

  const handleConsentUpdate = useCallback(
    (consentChoices: { analytics?: boolean }) => {
      analytics.handleConsentUpdate(consentChoices, user);
    },
    [user],
  );

  return {
    track,
    updateConsent,
    getConsent,
    shouldShowConsentBanner,
    handleConsentUpdate,
  };
};
