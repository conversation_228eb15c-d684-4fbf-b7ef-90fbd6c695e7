import { useAppContext } from '@/contexts/app-context';
import { AllowedActions, Modules, PERMISSION_LEVELS } from '@shared/user-permissions';
import { toast } from 'sonner';

export const usePermissions = () => {
  const { user } = useAppContext();

  const hasPermission = (module: Modules, action: AllowedActions, resourceOwnerId?: string) => {
    if (!user?.permissions) return false;

    const permission = user.permissions[module]?.[action];

    if (!permission) {
      toast.error('You do not have permission to perform this action', {
        description: `If you believe this is an error, please try logging out and logging back in.`,
      });
      return false;
    }

    if (permission === PERMISSION_LEVELS.FULL) {
      return true;
    }

    if (permission === PERMISSION_LEVELS.PARTIAL) {
      if (resourceOwnerId) {
        return resourceOwnerId === user.id;
      }
      return true;
    }

    return false;
  };

  return { hasPermission };
};
