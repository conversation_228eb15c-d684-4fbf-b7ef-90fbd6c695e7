import { trpc } from '@/providers/trpc';
import { OshaReportsFilters, OshaAgencyReportsFilters } from '@shared/osha.types';
import { RouterOutputs } from '@shared/router.types';
import { AccessPointsFilters, CapasFilters, EventsFilters, PublicSearchSchema } from '@shared/schema.types';
import { OshaLocationsFilters } from '@shared/settings.types';
import { useCallback, useMemo } from 'react';
import z from 'zod';

const TABLE_LIMIT = 20;

type BaseSearchParams = {
  enabled?: boolean;
  mustIncludeObjectIds?: string[];
  limit?: number;
};

type LocationSearchParams = BaseSearchParams & {
  search?: string;
};

type AssetSearchParams = BaseSearchParams & {
  locationId?: string;
};

type EventSearchParams = BaseSearchParams & {
  filters: EventsFilters;
};

type CapaSearchParams = BaseSearchParams & {
  filters: CapasFilters;
};

type AccessPointSearchParams = BaseSearchParams & {
  filters: AccessPointsFilters;
};

type OshaReportSearchParams = BaseSearchParams & {
  filters: OshaReportsFilters;
};

type AgencyReportSearchParams = BaseSearchParams & {
  filters: OshaAgencyReportsFilters;
};

type OshaLocationSearchParams = BaseSearchParams & {
  filters?: OshaLocationsFilters;
};

type PublicLocationSearchParams = BaseSearchParams & {
  upkeepCompanyId: string;
  search?: string;
};

type PublicAssetSearchParams = {
  upkeepCompanyId: string;
  search?: string;
  locationId?: string;
  objectId?: string | string[];
  enabled?: boolean;
};

/**
 * Cursor-based hooks using useInfiniteQuery for efficient pagination
 */

/**
 * Hook for cursor-based location search with infinite loading
 */
export function useInfiniteLocations({ search = '', enabled = true, mustIncludeObjectIds }: LocationSearchParams = {}) {
  const response = trpc.location.search.useInfiniteQuery(
    { search, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['location']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['location']['search']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based asset search with infinite loading
 */
export function useInfiniteAssets({ locationId, enabled = true, mustIncludeObjectIds }: AssetSearchParams) {
  const response = trpc.asset.search.useInfiniteQuery(
    { locationId, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['asset']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['asset']['search']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based event list with infinite loading
 */
export function useInfiniteEvents({ filters, enabled = true, mustIncludeObjectIds }: EventSearchParams) {
  const response = trpc.event.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['event']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based minimal event list with infinite loading
 */
export function useInfiniteMinimalEvents({ filters, enabled = true, mustIncludeObjectIds }: EventSearchParams) {
  const response = trpc.event.minimalList.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['event']['minimalList']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based CAPA list with infinite loading
 */
export function useInfiniteCapas({ filters, enabled = true }: CapaSearchParams) {
  const response = trpc.capa.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['capa']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['capa']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based access points list with infinite loading
 */
export function useInfiniteAccessPoints({ enabled = true, filters }: AccessPointSearchParams) {
  const response = trpc.accessPoint.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['accessPoint']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['accessPoint']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

export function useInfiniteAgencyReports({ filters, enabled = true }: AgencyReportSearchParams) {
  const response = trpc.oshaAgencyReport.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaAgencyReport']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['oshaAgencyReport']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based public location search with infinite loading
 */
export function useInfiniteLocationsPublic({
  upkeepCompanyId,
  search = '',
  mustIncludeObjectIds,
  enabled = true,
}: PublicLocationSearchParams) {
  const response = trpc.location.searchPublic.useInfiniteQuery(
    { upkeepCompanyId, search, mustIncludeObjectIds, limit: TABLE_LIMIT },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['location']['searchPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['location']['searchPublic']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based public asset search with infinite loading
 */
export function useInfiniteAssetsPublic({
  upkeepCompanyId,
  search = '',
  locationId,
  objectId,
  enabled = true,
}: PublicAssetSearchParams) {
  const response = trpc.asset.searchPublic.useInfiniteQuery(
    { upkeepCompanyId, search, locationId, objectId, limit: 50 },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['asset']['searchPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < 50) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['asset']['searchPublic']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based public user search with infinite loading
 */
export function useInfiniteUsersPublic({
  search = '',
  enabled = true,
  mustIncludeObjectIds,
  upkeepCompanyId,
  limit = TABLE_LIMIT,
  userAccountType,
}: z.infer<typeof PublicSearchSchema> & { enabled?: boolean }) {
  const response = trpc.user.getUsersPublic.useInfiniteQuery(
    { search, mustIncludeObjectIds, upkeepCompanyId, limit, userAccountType },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['user']['getUsersPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: false,
      refetchOnMount: true,
      initialCursor: 0,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['user']['getUsersPublic']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based event list with infinite loading
 */
export function useInfiniteOshaReports({
  filters,
  enabled = true,
  mustIncludeObjectIds,
  limit = TABLE_LIMIT,
}: OshaReportSearchParams) {
  const response = trpc.oshaReport.list.useInfiniteQuery(
    { ...filters, limit, mustIncludeObjectIds, sortBy: 'createdAt', sortOrder: 'desc' },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaReport']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based OSHA locations list with infinite loading
 */
export function useInfiniteOshaLocations({ filters, enabled = true }: OshaLocationSearchParams) {
  const response = trpc.oshaLocation.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaLocation']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['oshaLocation']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based minimal OSHA locations list with infinite loading
 */
export function useInfiniteMinimalOshaLocations({
  filters,
  enabled = true,
  mustIncludeObjectIds,
}: OshaLocationSearchParams) {
  const response = trpc.oshaLocation.minimalList.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaLocation']['minimalList']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['oshaLocation']['minimalList']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}
