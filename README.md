# UpKeep EHS - Safety Management Platform

UpKeep EHS is a comprehensive safety management platform designed for manufacturing teams to efficiently report, track, and manage safety incidents. The application features an innovative voice-to-text input system with AI-powered form autocompletion.

changes for release

![UpKeep EHS Platform](./attached_assets/Screenshot%202025-04-14%20at%2011.49.22%20AM.png)

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Key Features](#key-features)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Environment Variables](#environment-variables)
- [Deployment](#deployment)
- [Voice Input System](#voice-input-system)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [Future Enhancements](#future-enhancements)
- [License](#license)

## Architecture Overview

UpKeep EHS is built on a full-stack architecture with the following components:

1. **Frontend**: React with TypeScript, managed by Vite for rapid development
2. **Backend**: Express.js server handling API requests, data persistence, and OpenAI integration
3. **Database**: PostgreSQL with <PERSON><PERSON><PERSON> ORM for data modeling and migrations
4. **AI Services**: OpenAI integration for voice transcript analysis and data extraction

The application follows a client-server model where:

- The frontend contains all UI components, forms, and client-side logic
- The backend handles API requests, database operations, and AI service integrations
- The shared folder contains common schemas used by both frontend and backend

## Key Features

- **Voice-to-Form Input**: Revolutionary voice recording and transcription with AI-powered field autocompletion
- **Incident Management**: Comprehensive incident reporting with severity classification, root cause analysis, work order generation, and status-preserving archive/unarchive functionality
- **Responsive UI**: Fully adaptive design that works on desktop and mobile devices
- **Visual Feedback**: Advanced animations and UI indicators for form autocompletion
- **Database Integration**: PostgreSQL database for reliable data persistence
- **Archive System**: Sophisticated incident archiving with status preservation, visual indicators, and filtering options
- **AI Safety Insights**: Intelligent analysis of incident data with actionable recommendations
- **Comments System**: Rich commenting functionality with user attribution and timestamps

## Tech Stack

### Frontend

- React 18 with TypeScript
- Tailwind CSS for styling
- Shadcn/UI components
- React Hook Form with Zod validation
- Framer Motion for animations
- TanStack Query for API interactions
- Wouter for routing

### Backend

- Express.js with TypeScript
- OpenAI API integration
- PostgreSQL with Drizzle ORM
- Authentication with Passport.js

### Development Tools

- Vite for frontend bundling and HMR
- TypeScript for type safety
- PostgreSQL for data persistence
- ESLint for code quality

## Project Structure

```
├── client/                  # Frontend application
│   ├── src/
│   │   ├── components/      # UI components
│   │   │   ├── layout/      # Layout components (sidebar, navbar)
│   │   │   ├── ui/          # Shadcn UI components
│   │   │   └── voice/       # Voice input components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── lib/             # Utility functions and config
│   │   ├── pages/           # Page components
│   │   ├── App.tsx          # Main application component
│   │   └── main.tsx         # Application entry point
│   └── index.html           # HTML entry point
│
├── server/                  # Backend application
│   ├── services/            # Service integrations (OpenAI)
│   ├── db.ts                # Database configuration
│   ├── index.ts             # Server entry point
│   ├── routes.ts            # API route definitions
│   ├── storage.ts           # Data access layer
│   └── vite.ts              # Vite server integration
│
├── shared/                  # Shared code between frontend and backend
│   └── schema.ts            # Database schema definitions
│
├── drizzle.config.ts        # Drizzle ORM configuration
├── package.json             # Project dependencies
├── tsconfig.json            # TypeScript configuration
└── vite.config.ts           # Vite configuration
```

## Getting Started

### Prerequisites

- Node.js 18 or later
- PostgreSQL database
- OpenAI API key

### Installation

1. Clone the repository:

   ```
   git clone https://github.com/your-org/upkeep-ehs.git
   cd upkeep-ehs
   ```

2. Install dependencies:

   ```
   npm install
   ```

3. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Fill in the required values (see Environment Variables section)

4. Push the database schema:

   ```
   npm run db:migrate
   npm run db:push
   ```

5. Start the development server:
   ```
   npm run dev
   ```

## CI/CD and Testing

### Automated Testing

The project includes comprehensive test coverage with:

- **Unit Tests**: Service layer and utility function tests
- **Integration Tests**: API endpoint tests using supertest
- **Type Checking**: TypeScript compilation validation
- **Linting**: ESLint for code quality

### Available Test Commands

```bash
# Run all server tests
npm run test:server

# Run tests with coverage
npm run test:server:coverage

# Run tests in watch mode
npm run test:watch

# Type checking
npm run check

# Linting
npm run lint:server
```

### GitHub Actions CI

The repository includes automated CI that runs on all pull requests and pushes to `main`/`develop` branches:

- ✅ **Type checking**: Ensures TypeScript compilation
- ✅ **Linting**: Code quality and style checks
- ✅ **Testing**: Runs full test suite
- ✅ **Coverage**: Generates test coverage reports

### Setting Up Branch Protection

To ensure tests pass before merging, set up branch protection rules:

1. Go to **Settings** → **Branches** in your GitHub repository
2. Click **Add rule** for your main branch (`main` or `develop`)
3. Configure the following settings:
   - ✅ **Require status checks to pass before merging**
   - ✅ **Require branches to be up to date before merging**
   - ✅ **Status checks**: Select `CI` (this will appear after the first workflow run)
   - ✅ **Require pull request reviews before merging** (recommended)
   - ✅ **Dismiss stale pull request approvals when new commits are pushed**

4. Click **Create** to save the rule

**Result**: Pull requests will be blocked from merging if any tests fail, type checking fails, or linting errors exist.

### Test Coverage

The CI workflow automatically generates test coverage reports. To view coverage locally:

```bash
npm run test:server:coverage
open coverage/index.html
```

## Environment Variables

The application requires the following environment variables:

```
# Database
DATABASE_URL=postgresql://user:password@host:port/database

# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# Server
PORT=5000
NODE_ENV=development or production
```

## Deployment

### Deploy to Production

1. **Build the application**:

   ```
   npm run build
   ```

2. **Database setup**:
   - Ensure you have a PostgreSQL database provisioned
   - Set the `DATABASE_URL` environment variable to point to your production database
   - Run the schema migrations: `npm run db:push`

3. **Server configuration**:
   - Set `NODE_ENV=production`
   - Configure a reverse proxy (Nginx/Apache) to serve the static frontend assets and proxy API requests
   - Ensure HTTPS is enabled for production environments

4. **Deploy options**:

   **A. Traditional hosting**:
   - Deploy the built frontend to a static file server
   - Run the Node.js server with a process manager like PM2

   ```
   npm install -g pm2
   pm2 start dist/server/index.js
   ```

   **B. Docker deployment**:
   - Create a Dockerfile and docker-compose.yml configuration
   - Build and deploy the Docker containers to your hosting platform

   **C. Platform as a Service (PaaS)**:
   - Deploy directly to platforms like Heroku, Railway, or Render
   - Configure the build process and environment variables according to the platform guidelines

   **D. Serverless deployment**:
   - Convert the Express server to serverless functions (AWS Lambda, Vercel Functions)
   - Deploy the frontend as static assets

### CI/CD Integration

For automated deployments, set up a CI/CD pipeline using:

- GitHub Actions
- GitLab CI
- Jenkins
- CircleCI

Example GitHub Actions workflow:

```yaml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm run build
      - run: npm run db:push
      # Deploy steps for your platform of choice
```

## Voice Input System

The voice input system is a key feature of UpKeep EHS, enabling rapid incident reporting through natural language.

### How it works:

1. User activates voice recording through the microphone button
2. Browser Web Speech API captures and transcribes speech in real-time
3. Upon completion, the transcript is sent to the OpenAI API for analysis
4. The AI extracts structured information about the incident
5. The form is automatically populated with the extracted data
6. Visual indicators highlight AI-populated fields
7. Users can review and edit any field as needed before submission

### Implementation Details:

- The `VoiceInput` component handles recording and transcription
- Server-side OpenAI integration extracts structured data from transcripts
- Animations provide visual feedback during the analysis process
- All fields remain editable, preserving user control

## Database Schema

The application uses PostgreSQL with Drizzle ORM for data management.

### Key Tables:

**Users**:

- Basic user information for authentication

**Incidents**:

- Complete incident reports including:
  - Report type (incident or near miss)
  - Title and description
  - DateTime and location
  - Severity level and hazard category
  - Root cause analysis
  - Work order information (if applicable)
  - Archive status with previous status tracking
  - Status tracking (Open, In Review, Closed)

**Comments**:

- User comments on incidents:
  - Content
  - Timestamp
  - User reference
  - Incident reference

## API Endpoints

The backend exposes the following key API endpoints:

- `POST /api/incidents`: Create a new incident report
- `GET /api/incidents`: Retrieve all incidents
- `GET /api/incidents/:id`: Get details for a specific incident
- `PATCH /api/incidents/:id`: Update an existing incident
- `PATCH /api/incidents/:id/status`: Update the status of an incident
- `PATCH /api/incidents/:id/archive`: Archive or unarchive an incident (preserves status)
- `POST /api/analyze-transcript`: Process voice transcript and extract structured data
- `GET /api/incidents/:incidentId/comments`: Get comments for a specific incident
- `POST /api/incidents/:incidentId/comments`: Add a comment to an incident

## Future Enhancements

Planned features for future development:

1. **Real-time Notifications**: Alert relevant personnel about new incidents
2. **Advanced Analytics**: Trend analysis and safety performance dashboards
3. **Mobile Application**: Native mobile apps for field reporting
4. **Offline Support**: Enable reporting without internet connection
5. **Multi-language Support**: Internationalization for global teams
6. **Document Attachments**: Allow photos and documents to be attached to reports
7. **Integration with Equipment Systems**: Connect with maintenance systems

## License

[MIT License](LICENSE)
